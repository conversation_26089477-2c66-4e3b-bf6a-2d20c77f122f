pygame C API
============

.. toctree::
   :maxdepth: 1
   :glob:

   c_api/slots.rst
   c_api/base.rst
   c_api/bufferproxy.rst
   c_api/color.rst
   c_api/display.rst
   c_api/event.rst
   c_api/freetype.rst
   c_api/mixer.rst
   c_api/rect.rst
   c_api/rwobject.rst
   c_api/surface.rst
   c_api/surflock.rst
   c_api/version.rst


src_c/include/ contains header files for applications
that use the pygame C API, while src_c/ contains
headers used by pygame internally.
