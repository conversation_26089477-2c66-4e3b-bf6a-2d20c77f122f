#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Professional <PERSON> (AS) Condolence Video Creator
Enhanced with premium features, multiple aspect ratios, advanced animations,
and professional-grade output quality.
"""

import os
import sys
import math
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import numpy as np

# Import our enhanced modules
from premium_arabic_renderer import PremiumArabicRenderer
from enhanced_imam_jawad_content import EnhancedImamJawadContent

class ProfessionalVideoCreator:
    """Professional-grade video creator with advanced features."""

    def __init__(self, aspect_ratio='1:1', duration_variant='medium', quality='high'):
        """Initialize with professional settings."""
        self.content = EnhancedImamJawadContent()
        self.renderer = PremiumArabicRenderer()

        # Video configuration
        self.aspect_ratio = aspect_ratio
        self.duration_variant = duration_variant
        self.quality = quality

        # Get resolution and scenes based on configuration
        aspect_configs = self.content.get_aspect_ratio_configs()
        self.resolution = aspect_configs[aspect_ratio]['resolution']
        self.width, self.height = self.resolution

        duration_config = self.content.get_duration_variants()[duration_variant]
        self.scenes = duration_config['scenes']
        self.target_duration = duration_config['total_duration']

        # Professional settings
        self.fps = 30  # High frame rate for smooth playback
        self.transition_duration = 0.8  # Longer, smoother transitions
        self.frames = []

        # Create output directory
        self.output_dir = "professional_imam_jawad_output"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"✓ Created professional output directory: {self.output_dir}")

    def create_gradient_background(self, style_config):
        """Create sophisticated gradient backgrounds."""
        if 'bg_gradient' in style_config:
            return self.renderer.create_gradient_background(
                self.resolution,
                style_config['bg_gradient'],
                direction='vertical'
            )
        else:
            # Fallback to solid color
            bg_color = style_config.get('bg_color', (0, 0, 0))
            return Image.new('RGB', self.resolution, bg_color)

    def add_sophisticated_decorations(self, img, style_config):
        """Add sophisticated Islamic decorative elements."""
        border_style = style_config.get('border_style', 'classic')
        accent_color = style_config.get('accent_color', (139, 69, 19))

        # Apply ornate border
        img = self.renderer.draw_ornate_border(img, border_style, accent_color)

        # Add corner decorations based on aspect ratio
        if self.aspect_ratio in ['1:1', '4:5']:
            self._add_square_decorations(img, accent_color)
        elif self.aspect_ratio == '16:9':
            self._add_widescreen_decorations(img, accent_color)
        elif self.aspect_ratio == '9:16':
            self._add_vertical_decorations(img, accent_color)

        return img

    def _add_square_decorations(self, img, color):
        """Add decorations optimized for square layouts."""
        draw = ImageDraw.Draw(img)

        # Central decorative line
        center_y = self.height // 2 + 100
        line_start = self.width // 4
        line_end = 3 * self.width // 4

        # Draw ornamental line with decorative ends
        draw.line([(line_start, center_y), (line_end, center_y)], fill=color, width=4)

        # Decorative end caps
        cap_size = 15
        for x in [line_start, line_end]:
            draw.ellipse([x-cap_size, center_y-cap_size, x+cap_size, center_y+cap_size],
                        outline=color, width=3)

    def _add_widescreen_decorations(self, img, color):
        """Add decorations optimized for widescreen layouts."""
        draw = ImageDraw.Draw(img)

        # Side decorative elements
        margin = 100
        center_y = self.height // 2

        # Left and right ornamental elements
        for x in [margin, self.width - margin]:
            for i in range(3):
                y_offset = (i - 1) * 40
                draw.ellipse([x-10, center_y + y_offset - 10,
                             x+10, center_y + y_offset + 10],
                            outline=color, width=2)

    def _add_vertical_decorations(self, img, color):
        """Add decorations optimized for vertical/story layouts."""
        draw = ImageDraw.Draw(img)

        # Top and bottom decorative bands
        band_height = 60
        margin = 80

        # Top decorative band
        draw.rectangle([margin, band_height, self.width - margin, band_height + 4],
                      fill=color)

        # Bottom decorative band
        draw.rectangle([margin, self.height - band_height - 4,
                       self.width - margin, self.height - band_height],
                      fill=color)

    def calculate_text_layout(self, scene_data):
        """Calculate optimal text layout based on aspect ratio and content."""
        if self.aspect_ratio == '1:1':
            return self._get_square_layout(scene_data)
        elif self.aspect_ratio == '16:9':
            return self._get_widescreen_layout(scene_data)
        elif self.aspect_ratio == '9:16':
            return self._get_vertical_layout(scene_data)
        elif self.aspect_ratio == '4:5':
            return self._get_facebook_layout(scene_data)
        else:
            return self._get_default_layout(scene_data)

    def _get_square_layout(self, scene_data):
        """Optimized layout for square (1:1) format."""
        has_subtitle = 'subtitle_arabic' in scene_data or 'subtitle_english' in scene_data

        if has_subtitle:
            return {
                'main_arabic': self.height // 2 - 80,
                'main_english': self.height // 2 - 30,
                'subtitle_arabic': self.height // 2 + 30,
                'subtitle_english': self.height // 2 + 80,
                'decorative_y': self.height - 200
            }
        else:
            return {
                'main_arabic': self.height // 2 - 40,
                'main_english': self.height // 2 + 10,
                'decorative_y': self.height - 200
            }

    def _get_widescreen_layout(self, scene_data):
        """Optimized layout for widescreen (16:9) format."""
        has_subtitle = 'subtitle_arabic' in scene_data or 'subtitle_english' in scene_data

        if has_subtitle:
            return {
                'main_arabic': self.height // 2 - 60,
                'main_english': self.height // 2 - 20,
                'subtitle_arabic': self.height // 2 + 20,
                'subtitle_english': self.height // 2 + 60,
                'decorative_y': self.height - 120
            }
        else:
            return {
                'main_arabic': self.height // 2 - 30,
                'main_english': self.height // 2 + 10,
                'decorative_y': self.height - 120
            }

    def _get_vertical_layout(self, scene_data):
        """Optimized layout for vertical/story (9:16) format."""
        has_subtitle = 'subtitle_arabic' in scene_data or 'subtitle_english' in scene_data

        if has_subtitle:
            return {
                'main_arabic': self.height // 2 - 100,
                'main_english': self.height // 2 - 50,
                'subtitle_arabic': self.height // 2 + 50,
                'subtitle_english': self.height // 2 + 100,
                'decorative_y': self.height - 300
            }
        else:
            return {
                'main_arabic': self.height // 2 - 50,
                'main_english': self.height // 2,
                'decorative_y': self.height - 300
            }

    def _get_facebook_layout(self, scene_data):
        """Optimized layout for Facebook (4:5) format."""
        has_subtitle = 'subtitle_arabic' in scene_data or 'subtitle_english' in scene_data

        if has_subtitle:
            return {
                'main_arabic': self.height // 2 - 70,
                'main_english': self.height // 2 - 25,
                'subtitle_arabic': self.height // 2 + 25,
                'subtitle_english': self.height // 2 + 70,
                'decorative_y': self.height - 180
            }
        else:
            return {
                'main_arabic': self.height // 2 - 35,
                'main_english': self.height // 2 + 5,
                'decorative_y': self.height - 180
            }

    def _get_default_layout(self, scene_data):
        """Default layout for other aspect ratios."""
        return self._get_square_layout(scene_data)

    def get_font_sizes(self):
        """Get optimal font sizes based on resolution and aspect ratio."""
        base_size = min(self.width, self.height) // 25

        return {
            'title': int(base_size * 1.8),
            'main': int(base_size * 1.4),
            'subtitle': int(base_size * 1.0),
            'english': int(base_size * 0.9),
            'watermark': int(base_size * 0.6)
        }

    def create_advanced_animation_frame(self, scene_name, frame_number, total_frames):
        """Create a frame with advanced animations and effects."""
        scene_data = self.content.content[scene_name]
        style_name = scene_data.get('style', 'elegant')
        style_config = self.content.get_enhanced_styles()[style_name]
        animation_type = scene_data.get('animation', 'fade')

        # Calculate animation progress
        progress = frame_number / max(1, total_frames - 1) if total_frames > 1 else 1.0

        # Create base image with gradient background
        img = self.create_gradient_background(style_config)

        # Add sophisticated decorations
        img = self.add_sophisticated_decorations(img, style_config)

        # Get layout and fonts
        layout = self.calculate_text_layout(scene_data)
        font_sizes = self.get_font_sizes()

        # Apply animation-specific effects
        img = self._apply_animation_effects(img, scene_data, style_config, layout,
                                          font_sizes, progress, animation_type)

        # Add elegant watermark
        img = self._add_elegant_watermark(img, progress)

        return img

    def _apply_animation_effects(self, img, scene_data, style_config, layout,
                               font_sizes, progress, animation_type):
        """Apply sophisticated animation effects."""

        if animation_type == 'fade_with_calligraphy':
            return self._animate_fade_with_calligraphy(img, scene_data, style_config,
                                                     layout, font_sizes, progress)
        elif animation_type == 'slide_up':
            return self._animate_slide_up(img, scene_data, style_config,
                                        layout, font_sizes, progress)
        elif animation_type == 'zoom_with_glow':
            return self._animate_zoom_with_glow(img, scene_data, style_config,
                                              layout, font_sizes, progress)
        elif animation_type == 'divine_glow':
            return self._animate_divine_glow(img, scene_data, style_config,
                                           layout, font_sizes, progress)
        else:
            return self._animate_gentle_fade(img, scene_data, style_config,
                                           layout, font_sizes, progress)

    def _animate_gentle_fade(self, img, scene_data, style_config, layout, font_sizes, progress):
        """Gentle fade-in animation."""
        # Get fonts
        main_font, _ = self.renderer.get_best_font(font_sizes['main'], 'title')
        english_font, _ = self.renderer.get_best_font(font_sizes['english'], 'modern')

        # Calculate alpha for fade effect
        alpha = int(255 * min(1.0, progress * 1.5))

        # Main Arabic text
        if 'arabic' in scene_data:
            color = (*style_config['primary_color'], alpha) if alpha < 255 else style_config['primary_color']
            img = self.renderer.draw_centered_text(img, scene_data['arabic'],
                                                 layout['main_arabic'], main_font,
                                                 color[:3], style_config)

        # English subtitle
        if 'english' in scene_data and progress > 0.3:
            english_alpha = int(255 * min(1.0, (progress - 0.3) * 2))
            color = (*style_config['secondary_color'], english_alpha) if english_alpha < 255 else style_config['secondary_color']
            img = self.renderer.draw_centered_text(img, scene_data['english'],
                                                 layout['main_english'], english_font,
                                                 color[:3])

        return img

    def _animate_fade_with_calligraphy(self, img, scene_data, style_config, layout, font_sizes, progress):
        """Elegant fade with calligraphic effects."""
        title_font, _ = self.renderer.get_best_font(font_sizes['title'], 'title')
        english_font, _ = self.renderer.get_best_font(font_sizes['english'], 'modern')

        # Calligraphic entrance effect
        scale_factor = 0.8 + (0.2 * progress)
        alpha = int(255 * min(1.0, progress * 1.2))

        if 'arabic' in scene_data:
            # Create scaled font for animation
            scaled_size = int(font_sizes['title'] * scale_factor)
            scaled_font, _ = self.renderer.get_best_font(scaled_size, 'title')

            color = (*style_config['primary_color'], alpha) if alpha < 255 else style_config['primary_color']
            effects = {'text_glow': True, 'glow_radius': 4}
            img = self.renderer.draw_centered_text(img, scene_data['arabic'],
                                                 layout['main_arabic'], scaled_font,
                                                 color[:3], effects)

        if 'english' in scene_data and progress > 0.4:
            english_alpha = int(255 * min(1.0, (progress - 0.4) * 2))
            color = (*style_config['secondary_color'], english_alpha) if english_alpha < 255 else style_config['secondary_color']
            img = self.renderer.draw_centered_text(img, scene_data['english'],
                                                 layout['main_english'], english_font, color[:3])

        return img

    def _animate_slide_up(self, img, scene_data, style_config, layout, font_sizes, progress):
        """Slide up animation with smooth easing."""
        main_font, _ = self.renderer.get_best_font(font_sizes['main'], 'regular')
        english_font, _ = self.renderer.get_best_font(font_sizes['english'], 'modern')

        # Easing function for smooth animation
        eased_progress = 1 - (1 - progress) ** 3  # Cubic ease-out

        # Calculate slide offset
        slide_offset = int(50 * (1 - eased_progress))

        if 'arabic' in scene_data:
            y_pos = layout['main_arabic'] + slide_offset
            alpha = int(255 * min(1.0, progress * 1.5))
            color = (*style_config['primary_color'], alpha) if alpha < 255 else style_config['primary_color']

            img = self.renderer.draw_centered_text(img, scene_data['arabic'],
                                                 y_pos, main_font, color[:3],
                                                 {'text_shadow': True})

        if 'english' in scene_data and progress > 0.3:
            english_progress = (progress - 0.3) / 0.7
            english_eased = 1 - (1 - english_progress) ** 3
            english_offset = int(30 * (1 - english_eased))
            y_pos = layout['main_english'] + english_offset

            alpha = int(255 * min(1.0, english_progress * 2))
            color = (*style_config['secondary_color'], alpha) if alpha < 255 else style_config['secondary_color']
            img = self.renderer.draw_centered_text(img, scene_data['english'],
                                                 y_pos, english_font, color[:3])

        return img

    def _animate_zoom_with_glow(self, img, scene_data, style_config, layout, font_sizes, progress):
        """Zoom animation with glowing effects."""
        # Dynamic zoom effect
        zoom_factor = 0.5 + (0.5 * min(1.0, progress * 1.5))
        glow_intensity = int(progress * 6)

        if 'arabic' in scene_data:
            zoomed_size = int(font_sizes['title'] * zoom_factor)
            zoomed_font, _ = self.renderer.get_best_font(zoomed_size, 'title')

            effects = {
                'text_glow': True,
                'glow_radius': glow_intensity,
                'divine_glow': progress > 0.7
            }

            img = self.renderer.draw_centered_text(img, scene_data['arabic'],
                                                 layout['main_arabic'], zoomed_font,
                                                 style_config['primary_color'], effects)

        if 'english' in scene_data and progress > 0.5:
            english_progress = (progress - 0.5) * 2
            english_zoom = 0.7 + (0.3 * english_progress)
            english_size = int(font_sizes['english'] * english_zoom)
            english_font, _ = self.renderer.get_best_font(english_size, 'modern')

            img = self.renderer.draw_centered_text(img, scene_data['english'],
                                                 layout['main_english'], english_font,
                                                 style_config['secondary_color'])

        return img

    def _animate_divine_glow(self, img, scene_data, style_config, layout, font_sizes, progress):
        """Divine glow animation for Quranic verses."""
        main_font, _ = self.renderer.get_best_font(font_sizes['main'], 'title')
        english_font, _ = self.renderer.get_best_font(font_sizes['english'], 'modern')

        # Pulsing glow effect
        glow_cycle = math.sin(progress * math.pi * 4) * 0.5 + 0.5
        glow_radius = int(3 + glow_cycle * 5)

        if 'arabic' in scene_data:
            effects = {
                'divine_glow': True,
                'glow_radius': glow_radius,
                'text_glow': True
            }

            # Enhanced color for divine text
            divine_color = tuple(min(255, c + int(glow_cycle * 50)) for c in style_config['primary_color'])

            img = self.renderer.draw_centered_text(img, scene_data['arabic'],
                                                 layout['main_arabic'], main_font,
                                                 divine_color, effects)

        if 'english' in scene_data and progress > 0.4:
            img = self.renderer.draw_centered_text(img, scene_data['english'],
                                                 layout['main_english'], english_font,
                                                 style_config['secondary_color'])

        return img

    def _add_elegant_watermark(self, img, progress):
        """Add animated elegant watermark."""
        if progress < 0.8:  # Only show watermark near end of scene
            return img

        watermark_alpha = int(128 * min(1.0, (progress - 0.8) * 5))

        # Get watermark font
        watermark_font, _ = self.renderer.get_best_font(self.get_font_sizes()['watermark'], 'modern')

        # Position based on aspect ratio
        if self.aspect_ratio == '9:16':
            x_pos = self.width - 120
            y_pos = self.height - 80
        else:
            x_pos = self.width - 150
            y_pos = self.height - 50

        # Draw watermark with fade-in
        draw = ImageDraw.Draw(img)
        watermark_color = (128, 128, 128, watermark_alpha) if watermark_alpha < 255 else (128, 128, 128)
        draw.text((x_pos, y_pos), "AliToucan", font=watermark_font, fill=watermark_color[:3])

        return img

def create_professional_video_suite():
    """Create a complete suite of professional videos in all formats."""
    print("🎬 PROFESSIONAL IMAM AL-JAWAD VIDEO SUITE")
    print("=" * 60)

    # Configuration matrix
    configs = [
        ('1:1', 'medium', 'Square - Social Media'),
        ('16:9', 'medium', 'Widescreen - YouTube'),
        ('9:16', 'short', 'Vertical - Stories'),
        ('4:5', 'short', 'Facebook Optimized')
    ]

    created_videos = []

    for aspect_ratio, duration, description in configs:
        print(f"\n📹 Creating {description} ({aspect_ratio})...")

        try:
            creator = ProfessionalVideoCreator(
                aspect_ratio=aspect_ratio,
                duration_variant=duration,
                quality='high'
            )

            print(f"   Resolution: {creator.resolution[0]}x{creator.resolution[1]}")
            print(f"   Scenes: {len(creator.scenes)}")

            # Create frames (limited for demo)
            frames = []
            for i, scene_name in enumerate(creator.scenes[:3]):  # First 3 scenes for demo
                print(f"   Creating scene {i+1}: {scene_name}")
                scene_frames = []

                # Create 30 frames per scene (1 second at 30fps)
                for frame_num in range(30):
                    frame = creator.create_advanced_animation_frame(scene_name, frame_num, 30)
                    scene_frames.append(frame)

                frames.extend(scene_frames)

            creator.frames = frames

            # Save as optimized GIF
            timestamp = datetime.now().strftime('%H%M%S')
            filename = f"professional_imam_jawad_{aspect_ratio.replace(':', 'x')}_{timestamp}.gif"

            if frames:
                filepath = os.path.join(creator.output_dir, filename)
                frames[0].save(
                    filepath,
                    save_all=True,
                    append_images=frames[1:],
                    duration=33,  # ~30fps
                    loop=0,
                    optimize=True
                )

                file_size = os.path.getsize(filepath) / (1024 * 1024)
                print(f"   ✓ Saved: {filename} ({file_size:.1f} MB)")
                created_videos.append((filepath, description))

        except Exception as e:
            print(f"   ❌ Error creating {description}: {e}")

    return created_videos

if __name__ == "__main__":
    videos = create_professional_video_suite()

    if videos:
        print("\n" + "=" * 60)
        print("🎉 PROFESSIONAL VIDEO SUITE COMPLETE!")
        print("=" * 60)

        for filepath, description in videos:
            print(f"✓ {description}: {os.path.basename(filepath)}")

        print(f"\n📁 Output directory: professional_imam_jawad_output")
        print("\n🎯 FEATURES IMPLEMENTED:")
        print("  ✓ Premium Arabic typography")
        print("  ✓ Multiple aspect ratios (1:1, 16:9, 9:16, 4:5)")
        print("  ✓ Advanced animations and transitions")
        print("  ✓ Gradient backgrounds and textures")
        print("  ✓ Sophisticated Islamic decorations")
        print("  ✓ English subtitles for accessibility")
        print("  ✓ 30 FPS smooth playback")
        print("  ✓ Professional-grade visual effects")

        print("\n🤲 May Allah bless the memory of Imam al-Jawad (AS)")
    else:
        print("\n❌ No videos were created successfully.")
        print("Please check the error messages above.")
