from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QCursor, QPixmap, QPen, QColor, QIcon
from PyQt5.QtCore import Qt, QPoint, QPointF, QRect, QRectF

import math
from drawing_canvas import LineItem, CircleItem, RectangleItem, TextItem

class DrawingTool:
    """Base class for all drawing tools"""
    
    def __init__(self, name):
        self.name = name
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.ArrowCursor)
        
    def is_drawing_tool(self):
        """Check if this is a drawing tool"""
        return True
        
    def start_drawing(self, pos):
        """Start drawing at the given position"""
        return None
        
    def continue_drawing(self, item, pos):
        """Continue drawing to the given position"""
        pass
        
    def finish_drawing(self, item, pos):
        """Finish drawing at the given position"""
        return None

class SelectionTool(DrawingTool):
    """Tool for selecting objects"""
    
    def __init__(self):
        super().__init__("select")
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.ArrowCursor)
        
    def is_drawing_tool(self):
        """This is not a drawing tool"""
        return False

class LineTool(DrawingTool):
    """Tool for drawing lines"""
    
    def __init__(self):
        super().__init__("line")
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.CrossCursor)
        
    def start_drawing(self, pos):
        """Start drawing a line at the given position"""
        return LineItem(pos, pos)
        
    def continue_drawing(self, item, pos):
        """Continue drawing the line to the given position"""
        if isinstance(item, LineItem):
            item.end_point = QPointF(pos)
        
    def finish_drawing(self, item, pos):
        """Finish drawing the line at the given position"""
        if isinstance(item, LineItem):
            item.end_point = QPointF(pos)
            
            # Check if the line has a valid length (not just a point)
            start_x, start_y = item.start_point.x(), item.start_point.y()
            end_x, end_y = item.end_point.x(), item.end_point.y()
            
            length = ((end_x - start_x) ** 2 + (end_y - start_y) ** 2) ** 0.5
            
            if length > 1:  # Minimum length threshold
                return item
                
        return None

class RectangleTool(DrawingTool):
    """Tool for drawing rectangles"""
    
    def __init__(self):
        super().__init__("rectangle")
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.CrossCursor)
        
    def start_drawing(self, pos):
        """Start drawing a rectangle at the given position"""
        return RectangleItem(pos, pos)
        
    def continue_drawing(self, item, pos):
        """Continue drawing the rectangle to the given position"""
        if isinstance(item, RectangleItem):
            item.bottom_right = QPointF(pos)
        
    def finish_drawing(self, item, pos):
        """Finish drawing the rectangle at the given position"""
        if isinstance(item, RectangleItem):
            item.bottom_right = QPointF(pos)
            
            # Check if the rectangle has a valid size
            x1, y1 = item.top_left.x(), item.top_left.y()
            x2, y2 = item.bottom_right.x(), item.bottom_right.y()
            
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            
            if width > 1 and height > 1:  # Minimum size threshold
                # Ensure correct ordering of points
                top_left = QPointF(min(x1, x2), min(y1, y2))
                bottom_right = QPointF(max(x1, x2), max(y1, y2))
                
                item.top_left = top_left
                item.bottom_right = bottom_right
                return item
                
        return None

class CircleTool(DrawingTool):
    """Tool for drawing circles"""
    
    def __init__(self):
        super().__init__("circle")
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.CrossCursor)
        
    def start_drawing(self, pos):
        """Start drawing a circle at the given position"""
        return CircleItem(pos, 0)
        
    def continue_drawing(self, item, pos):
        """Continue drawing the circle to the given position"""
        if isinstance(item, CircleItem):
            dx = pos.x() - item.center.x()
            dy = pos.y() - item.center.y()
            item.radius = (dx ** 2 + dy ** 2) ** 0.5
        
    def finish_drawing(self, item, pos):
        """Finish drawing the circle at the given position"""
        if isinstance(item, CircleItem):
            dx = pos.x() - item.center.x()
            dy = pos.y() - item.center.y()
            item.radius = (dx ** 2 + dy ** 2) ** 0.5
            
            if item.radius > 1:  # Minimum radius threshold
                return item
                
        return None

class ArcTool(DrawingTool):
    """Tool for drawing arcs"""
    
    def __init__(self):
        super().__init__("arc")
        self.start_angle = 0
        self.end_angle = 0
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.CrossCursor)
        
    def start_drawing(self, pos):
        """Start drawing an arc at the given position"""
        # Create a temporary CircleItem that we'll convert to an arc
        return CircleItem(pos, 0)
        
    def continue_drawing(self, item, pos):
        """Continue drawing the arc to the given position"""
        if isinstance(item, CircleItem):
            dx = pos.x() - item.center.x()
            dy = pos.y() - item.center.y()
            
            # Calculate radius
            item.radius = (dx ** 2 + dy ** 2) ** 0.5
            
            # Calculate angle in degrees
            self.end_angle = math.degrees(math.atan2(dy, dx))
            if self.end_angle < 0:
                self.end_angle += 360
        
    def finish_drawing(self, item, pos):
        """Finish drawing the arc at the given position"""
        if isinstance(item, CircleItem):
            dx = pos.x() - item.center.x()
            dy = pos.y() - item.center.y()
            
            # Calculate radius
            item.radius = (dx ** 2 + dy ** 2) ** 0.5
            
            # Calculate end angle in degrees
            self.end_angle = math.degrees(math.atan2(dy, dx))
            if self.end_angle < 0:
                self.end_angle += 360
                
            # Create arc parameters (in a real app, we'd create a proper ArcItem class)
            if item.radius > 1:  # Minimum radius threshold
                # We'll set arc properties in the existing CircleItem
                item.properties["arc_start_angle"] = self.start_angle
                item.properties["arc_end_angle"] = self.end_angle
                item.properties["is_arc"] = True
                return item
                
        return None

class TextTool(DrawingTool):
    """Tool for adding text"""
    
    def __init__(self):
        super().__init__("text")
        self.default_text = "Text"
        
    def get_cursor(self):
        """Get the cursor for this tool"""
        return QCursor(Qt.IBeamCursor)
        
    def start_drawing(self, pos):
        """Start adding text at the given position"""
        # In a real app, this would open a dialog to enter text
        return TextItem(pos, self.default_text)
        
    def continue_drawing(self, item, pos):
        """For text, there's no need to update during movement"""
        pass
        
    def finish_drawing(self, item, pos):
        """Finish adding text at the given position"""
        if isinstance(item, TextItem):
            # In a real app, this would set the text from user input
            return item
        return None

class ToolManager:
    """Manager for all drawing tools"""
    
    def __init__(self):
        self.tools = {}
        self.initialize_tools()
        
    def initialize_tools(self):
        """Initialize all available tools"""
        self.add_tool(SelectionTool())
        self.add_tool(LineTool())
        self.add_tool(RectangleTool())
        self.add_tool(CircleTool())
        self.add_tool(ArcTool())
        self.add_tool(TextTool())
        
    def add_tool(self, tool):
        """Add a tool to the manager"""
        self.tools[tool.name] = tool
        
    def get_tool(self, name):
        """Get a tool by name"""
        return self.tools.get(name)
        
    def get_all_tools(self):
        """Get all available tools"""
        return self.tools.values() 