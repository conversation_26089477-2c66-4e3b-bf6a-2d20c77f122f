<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم العزاء التقليدي الأسود والأبيض - الإمام الجواد عليه السلام</title>
    <meta name="description" content="تصميم العزاء التقليدي الأسود والأبيض لإحياء ذكرى استشهاد الإمام محمد الجواد عليه السلام">
    <link rel="stylesheet" href="styles/common.css">
    <style>
        body {
            background: #1a1a1a;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .design-container {
            background: #000000;
        }

        
        /* Enhanced shadow effects */
        .enhanced-shadow {
            text-shadow: 
                3px 3px 6px rgba(0, 0, 0, 0.8),
                0 0 10px rgba(255, 255, 255, 0.3);
        }

        .enhanced-shadow.secondary {
            text-shadow: 
                3px 3px 6px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(255, 255, 255, 0.2);
        }
        
        /* Add subtle animation */
        .text-element {
            animation: fadeInUp 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        
        .text-element:nth-child(1) { animation-delay: 0.2s; }
        .text-element:nth-child(2) { animation-delay: 0.4s; }
        .text-element:nth-child(3) { animation-delay: 0.6s; }
        .text-element:nth-child(4) { animation-delay: 0.8s; }
        .text-element:nth-child(5) { animation-delay: 1.0s; }
        .text-element:nth-child(6) { animation-delay: 1.2s; }

        /* Responsive font adjustments */
        @media (max-width: 480px) {
            .imam-name {
                font-size: clamp(1.3rem, 4vmin, 2.5rem);
                line-height: 1.1;
            }
            
            .condolence-phrase {
                font-size: clamp(1.1rem, 3.5vmin, 2rem);
                line-height: 1.2;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navigation">
        <a href="index.html">← العودة للرئيسية</a>
        <a href="traditional-outline.html">نسخة الحدود</a>
    </nav>

    <!-- Main Design Container -->
    <div class="design-container traditional-theme">
        <!-- Corner Decorations -->
        <div class="corner-decoration top-left"></div>
        <div class="corner-decoration top-right"></div>
        <div class="corner-decoration bottom-left"></div>
        <div class="corner-decoration bottom-right"></div>

        <!-- Text Content -->
        <h1 class="text-element bismillah arabic-text enhanced-shadow secondary">
            بسم الله الرحمن الرحيم
        </h1>

        <h2 class="text-element condolence-intro arabic-text enhanced-shadow">
            بمناسبة ذكرى استشهاد
        </h2>

        <h1 class="text-element imam-name arabic-text enhanced-shadow">
            الإمام محمد الجواد عليه السلام
        </h1>

        <h3 class="text-element date arabic-text enhanced-shadow secondary">
            التاسع والعشرون من ذي القعدة
        </h3>

        <h2 class="text-element condolence-phrase arabic-text enhanced-shadow">
            أحيا الله ذكراكم وأعظم أجوركم
        </h2>

        <h3 class="text-element salawat arabic-text enhanced-shadow secondary">
            اللهم صل على محمد وآل محمد
        </h3>

        <!-- Decorative Line -->
        <div class="decorative-line"></div>

        <!-- Watermark -->
        <div class="watermark">AliToucan</div>
    </div>

    <script>
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'Escape':
                    window.location.href = 'index.html';
                    break;
                case 'ArrowRight':
                    window.location.href = 'royal-shadow.html';
                    break;
                case 'ArrowLeft':
                    window.location.href = 'traditional-outline.html';
                    break;
                case 's':
                case 'S':
                    if (navigator.share) {
                        navigator.share({
                            title: 'تصميم العزاء للإمام الجواد عليه السلام - التقليدي الأسود والأبيض',
                            text: 'بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام',
                            url: window.location.href
                        });
                    } else {
                        navigator.clipboard.writeText(window.location.href).then(() => {
                            alert('تم نسخ الرابط إلى الحافظة');
                        });
                    }
                    break;
                case 'p':
                case 'P':
                    window.print();
                    break;
            }
        });

        // Add touch gestures for mobile
        let touchStartX = 0;
        let touchStartY = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            if (!touchStartX || !touchStartY) return;

            let touchEndX = e.changedTouches[0].clientX;
            let touchEndY = e.changedTouches[0].clientY;

            let diffX = touchStartX - touchEndX;
            let diffY = touchStartY - touchEndY;

            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swipe left - go to next design
                    window.location.href = 'royal-shadow.html';
                } else {
                    // Swipe right - go to other version
                    window.location.href = 'traditional-outline.html';
                }
            }

            touchStartX = 0;
            touchStartY = 0;
        });

        // Preload next designs
        ['royal-shadow.html', 'traditional-outline.html'].forEach(page => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = page;
            document.head.appendChild(link);
        });

        // Add meta tags for social sharing
        const metaTags = [
            { property: 'og:title', content: 'تصميم العزاء للإمام الجواد عليه السلام - التقليدي الأسود والأبيض' },
            { property: 'og:description', content: 'بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام - التاسع والعشرون من ذي القعدة' },
            { property: 'og:type', content: 'website' },
            { property: 'og:url', content: window.location.href }
        ];

        metaTags.forEach(tag => {
            const meta = document.createElement('meta');
            if (tag.property) meta.setAttribute('property', tag.property);
            if (tag.name) meta.setAttribute('name', tag.name);
            meta.setAttribute('content', tag.content);
            document.head.appendChild(meta);
        });
    </script>
</body>
</html>