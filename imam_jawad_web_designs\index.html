<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصاميم العزاء للإمام الجواد عليه السلام - <PERSON> (AS) Condolence Designs</title>
    <meta name="description" content="تصاميم العزاء الرقمية لإحياء ذكرى استشهاد الإمام محمد الجواد عليه السلام">
    <meta name="keywords" content="الإمام الجواد, عزاء, تصاميم, شيعة, إسلامي">
    <link rel="stylesheet" href="styles/common.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .main-container {
            max-width: 1200px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 3rem;
            color: #FFD700;
        }

        .header h1 {
            font-size: clamp(2rem, 5vw, 3.5rem);
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            color: #FFFFFF;
            margin-bottom: 0.5rem;
        }

        .header .date {
            font-size: clamp(0.9rem, 2vw, 1.2rem);
            color: #C8C8C8;
        }

        .designs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .design-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .design-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        .design-preview {
            width: 100%;
            aspect-ratio: 1;
            border-radius: 10px;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .design-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #FFD700;
            margin-bottom: 0.5rem;
        }

        .design-description {
            font-size: 0.9rem;
            color: #C8C8C8;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .design-links {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .design-link {
            padding: 0.5rem 1rem;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .design-link:hover {
            background: linear-gradient(45deg, #FFA500, #FFD700);
            transform: scale(1.05);
        }

        .design-link.outline {
            background: transparent;
            border: 2px solid #FFD700;
            color: #FFD700;
        }

        .design-link.outline:hover {
            background: #FFD700;
            color: #000;
        }

        .footer {
            text-align: center;
            color: #C8C8C8;
            margin-top: 2rem;
        }

        .footer p {
            margin-bottom: 0.5rem;
        }

        .salawat {
            font-size: 1.1rem;
            color: #FFD700;
            font-weight: 500;
        }

        /* Preview thumbnails */
        .classic-preview {
            background: #000;
            color: #FFD700;
        }

        .elegant-preview {
            background: #191919;
            color: #C8C8C8;
        }

        .traditional-preview {
            background: #000;
            color: #FFF;
        }

        .royal-preview {
            background: #141428;
            color: #FFD700;
        }

        .preview-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 1rem;
            font-size: 0.7rem;
            text-align: center;
        }

        .preview-imam-name {
            font-weight: bold;
            margin: 0.5rem 0;
        }

        @media (max-width: 768px) {
            .designs-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .design-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <header class="header arabic-text">
            <h1>تصاميم العزاء للإمام الجواد عليه السلام</h1>
            <p>بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام</p>
            <p class="date">التاسع والعشرون من ذي القعدة</p>
        </header>

        <main class="designs-grid">
            <!-- Classic Black & Gold Design -->
            <div class="design-card">
                <div class="design-preview classic-preview" onclick="window.open('classic-shadow.html', '_blank')">
                    <div class="preview-content arabic-text">
                        <div>بسم الله الرحمن الرحيم</div>
                        <div>بمناسبة ذكرى استشهاد</div>
                        <div class="preview-imam-name">الإمام محمد الجواد عليه السلام</div>
                        <div>أحيا الله ذكراكم وأعظم أجوركم</div>
                    </div>
                </div>
                <h3 class="design-title arabic-text">الكلاسيكي الأسود والذهبي</h3>
                <p class="design-description arabic-text">التصميم التقليدي بألوان الحداد الأنيقة مع النص الذهبي</p>
                <div class="design-links">
                    <a href="classic-shadow.html" class="design-link" target="_blank">مع الظلال</a>
                    <a href="classic-outline.html" class="design-link outline" target="_blank">مع الحدود</a>
                </div>
            </div>

            <!-- Elegant Dark Gray & Silver Design -->
            <div class="design-card">
                <div class="design-preview elegant-preview" onclick="window.open('elegant-outline.html', '_blank')">
                    <div class="preview-content arabic-text">
                        <div>بسم الله الرحمن الرحيم</div>
                        <div>بمناسبة ذكرى استشهاد</div>
                        <div class="preview-imam-name">الإمام محمد الجواد عليه السلام</div>
                        <div>أحيا الله ذكراكم وأعظم أجوركم</div>
                    </div>
                </div>
                <h3 class="design-title arabic-text">الأنيق الرمادي والفضي</h3>
                <p class="design-description arabic-text">المظهر العصري المتطور بالألوان الرمادية والفضية</p>
                <div class="design-links">
                    <a href="elegant-shadow.html" class="design-link" target="_blank">مع الظلال</a>
                    <a href="elegant-outline.html" class="design-link outline" target="_blank">مع الحدود</a>
                </div>
            </div>

            <!-- Traditional Black & White Design -->
            <div class="design-card">
                <div class="design-preview traditional-preview" onclick="window.open('traditional-shadow.html', '_blank')">
                    <div class="preview-content arabic-text">
                        <div>بسم الله الرحمن الرحيم</div>
                        <div>بمناسبة ذكرى استشهاد</div>
                        <div class="preview-imam-name">الإمام محمد الجواد عليه السلام</div>
                        <div>أحيا الله ذكراكم وأعظم أجوركم</div>
                    </div>
                </div>
                <h3 class="design-title arabic-text">التقليدي الأسود والأبيض</h3>
                <p class="design-description arabic-text">التصميم الكلاسيكي عالي التباين بالأبيض والأسود</p>
                <div class="design-links">
                    <a href="traditional-shadow.html" class="design-link" target="_blank">مع الظلال</a>
                    <a href="traditional-outline.html" class="design-link outline" target="_blank">مع الحدود</a>
                </div>
            </div>

            <!-- Royal Navy & Gold Design -->
            <div class="design-card">
                <div class="design-preview royal-preview" onclick="window.open('royal-outline.html', '_blank')">
                    <div class="preview-content arabic-text">
                        <div>بسم الله الرحمن الرحيم</div>
                        <div>بمناسبة ذكرى استشهاد</div>
                        <div class="preview-imam-name">الإمام محمد الجواد عليه السلام</div>
                        <div>أحيا الله ذكراكم وأعظم أجوركم</div>
                    </div>
                </div>
                <h3 class="design-title arabic-text">الملكي الأزرق والذهبي</h3>
                <p class="design-description arabic-text">التصميم المميز بالأزرق العميق مع اللمسات الذهبية</p>
                <div class="design-links">
                    <a href="royal-shadow.html" class="design-link" target="_blank">مع الظلال</a>
                    <a href="royal-outline.html" class="design-link outline" target="_blank">مع الحدود</a>
                </div>
            </div>
        </main>

        <footer class="footer arabic-text">
            <p>تم إنشاء هذه التصاميم بكل احترام وتقدير لذكرى الإمام الجواد عليه السلام</p>
            <p>مناسبة للمشاركة على وسائل التواصل الاجتماعي والطباعة</p>
            <p class="salawat">اللهم صل على محمد وآل محمد</p>
            <p style="margin-top: 1rem; font-size: 0.8rem;">Created by: AliToucan Design System</p>
        </footer>
    </div>

    <script>
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close any modals or return to main page
                window.location.href = 'index.html';
            }
        });

        // Add touch support for mobile devices
        document.querySelectorAll('.design-preview').forEach(preview => {
            preview.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            preview.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Preload design pages for better performance
        const designPages = [
            'classic-shadow.html', 'classic-outline.html',
            'elegant-shadow.html', 'elegant-outline.html',
            'traditional-shadow.html', 'traditional-outline.html',
            'royal-shadow.html', 'royal-outline.html'
        ];

        designPages.forEach(page => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = page;
            document.head.appendChild(link);
        });
    </script>
</body>
</html>
