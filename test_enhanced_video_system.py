#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Suite for Enhanced Professional Imam <PERSON> Video System
Comprehensive testing of all components and features.
"""

import os
import sys
import traceback
from datetime import datetime

def test_imports():
    """Test all module imports."""
    print("🔍 Testing Module Imports...")
    print("-" * 40)
    
    modules_to_test = [
        ('premium_arabic_renderer', 'PremiumArabicRenderer'),
        ('enhanced_imam_jawad_content', 'EnhancedImamJawadContent'),
        ('enhanced_video_effects', 'EnhancedVideoEffects'),
        ('audio_manager', 'IslamicAudioManager'),
        ('mp4_video_creator', 'ProfessionalMP4Creator'),
        ('professional_imam_jawad_video_enhanced', 'ProfessionalEnhancedVideoCreator')
    ]
    
    import_results = {}
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name)
            class_obj = getattr(module, class_name)
            import_results[module_name] = True
            print(f"✓ {module_name}.{class_name}")
        except Exception as e:
            import_results[module_name] = False
            print(f"❌ {module_name}.{class_name}: {e}")
    
    return import_results

def test_arabic_renderer():
    """Test the premium Arabic renderer."""
    print("\n🔤 Testing Premium Arabic Renderer...")
    print("-" * 40)
    
    try:
        from premium_arabic_renderer import PremiumArabicRenderer
        
        renderer = PremiumArabicRenderer()
        
        # Test font loading
        font, font_name = renderer.get_best_font(36, 'title')
        print(f"✓ Font loading: {font_name}")
        
        # Test Arabic text processing
        test_text = "بسم الله الرحمن الرحيم"
        processed = renderer.process_arabic_text(test_text)
        print(f"✓ Arabic text processing: {len(processed)} characters")
        
        # Test gradient creation
        gradient = renderer.create_gradient_background((400, 300), [(0, 0, 0), (50, 50, 100)])
        print(f"✓ Gradient background: {gradient.size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Arabic renderer test failed: {e}")
        return False

def test_content_system():
    """Test the enhanced content system."""
    print("\n📝 Testing Enhanced Content System...")
    print("-" * 40)
    
    try:
        from enhanced_imam_jawad_content import EnhancedImamJawadContent
        
        content = EnhancedImamJawadContent()
        
        # Test content loading
        print(f"✓ Content scenes: {len(content.content)}")
        print(f"✓ Quranic verses: {len(content.quranic_verses)}")
        print(f"✓ Authentic hadith: {len(content.authentic_hadith)}")
        
        # Test duration variants
        variants = content.get_duration_variants()
        print(f"✓ Duration variants: {list(variants.keys())}")
        
        # Test aspect ratio configs
        configs = content.get_aspect_ratio_configs()
        print(f"✓ Aspect ratios: {list(configs.keys())}")
        
        # Test enhanced styles
        styles = content.get_enhanced_styles()
        print(f"✓ Visual styles: {len(styles)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Content system test failed: {e}")
        return False

def test_video_effects():
    """Test the enhanced video effects."""
    print("\n🎨 Testing Enhanced Video Effects...")
    print("-" * 40)
    
    try:
        from enhanced_video_effects import EnhancedVideoEffects
        from PIL import Image
        
        effects = EnhancedVideoEffects((800, 600))
        
        # Test pattern creation
        pattern = effects.create_islamic_geometric_pattern((400, 300), 'star')
        print(f"✓ Islamic pattern: {pattern.size}")
        
        # Test fade transition
        test_img = Image.new('RGB', (400, 300), (100, 100, 100))
        faded_img = effects.apply_fade_transition(test_img, 0.5)
        print(f"✓ Fade transition: {faded_img.mode}")
        
        # Test color grading
        graded_img = effects.apply_professional_color_grading(test_img, 'warm_gold')
        print(f"✓ Color grading: {graded_img.mode}")
        
        # Test particle effects
        particle_img = effects.create_particle_effect(test_img, 0.8, 'stars')
        print(f"✓ Particle effects: {particle_img.size}")
        
        return True
        
    except Exception as e:
        print(f"❌ Video effects test failed: {e}")
        return False

def test_audio_manager():
    """Test the Islamic audio manager."""
    print("\n🎵 Testing Islamic Audio Manager...")
    print("-" * 40)
    
    try:
        from audio_manager import IslamicAudioManager
        
        audio_manager = IslamicAudioManager()
        
        # Test audio library
        print(f"✓ Audio library loaded")
        
        # Test recommendations
        recommendation = audio_manager.get_audio_recommendations('peaceful', 45)
        print(f"✓ Audio recommendation: {recommendation['description']}")
        
        # Test audio creation (if libraries available)
        try:
            test_audio = audio_manager.create_islamic_ambient_audio(5, 'peaceful')
            if test_audio:
                print(f"✓ Audio creation: {len(test_audio)}ms")
            else:
                print("⚠️  Audio creation skipped (libraries not available)")
        except:
            print("⚠️  Audio creation skipped (libraries not available)")
        
        return True
        
    except Exception as e:
        print(f"❌ Audio manager test failed: {e}")
        return False

def test_mp4_creator():
    """Test the MP4 video creator."""
    print("\n🎬 Testing MP4 Video Creator...")
    print("-" * 40)
    
    try:
        from mp4_video_creator import ProfessionalMP4Creator
        from PIL import Image
        
        mp4_creator = ProfessionalMP4Creator()
        
        # Test initialization
        print(f"✓ MP4 creator initialized")
        
        # Test frame resizing
        test_frame = Image.new('RGB', (800, 600), (100, 150, 200))
        resized_frame = mp4_creator._resize_frame_with_padding(test_frame, (1080, 1080))
        print(f"✓ Frame resizing: {resized_frame.size}")
        
        # Test video creation (if libraries available)
        try:
            test_frames = [Image.new('RGB', (400, 300), (i * 50, 100, 200)) for i in range(5)]
            video_path = mp4_creator.create_mp4_from_frames(test_frames, "test_video", fps=10)
            if video_path:
                print(f"✓ MP4 creation: {os.path.basename(video_path)}")
            else:
                print("⚠️  MP4 creation skipped (MoviePy not available)")
        except:
            print("⚠️  MP4 creation skipped (MoviePy not available)")
        
        return True
        
    except Exception as e:
        print(f"❌ MP4 creator test failed: {e}")
        return False

def test_enhanced_video_creator():
    """Test the main enhanced video creator."""
    print("\n🎬 Testing Enhanced Video Creator...")
    print("-" * 40)
    
    try:
        from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
        
        # Test initialization
        creator = ProfessionalEnhancedVideoCreator(
            aspect_ratio='1:1',
            duration_variant='short',  # Use short for faster testing
            quality='high'
        )
        
        print(f"✓ Creator initialized: {creator.resolution}")
        print(f"✓ Scenes to create: {len(creator.scenes)}")
        
        # Test single frame creation
        test_frame = creator.create_enhanced_scene_frame('opening_bismillah', 0, 30, 0.5)
        print(f"✓ Frame creation: {test_frame.size}")
        
        # Test layout calculation
        layout = creator._calculate_enhanced_text_layout({'arabic': 'test', 'english': 'test'})
        print(f"✓ Text layout: {len(layout)} positions")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced video creator test failed: {e}")
        traceback.print_exc()
        return False

def create_test_video():
    """Create a small test video to verify the complete system."""
    print("\n🎥 Creating Test Video...")
    print("-" * 40)
    
    try:
        from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
        
        # Create a minimal test video
        creator = ProfessionalEnhancedVideoCreator(
            aspect_ratio='1:1',
            duration_variant='short',
            quality='high'
        )
        
        # Create just a few frames for testing
        print("Creating test frames...")
        test_frames = []
        
        # Create frames for first scene only
        scene_name = creator.scenes[0]
        for i in range(10):  # Just 10 frames for quick test
            frame = creator.create_enhanced_scene_frame(scene_name, i, 10, i/9)
            test_frames.append(frame)
        
        creator.frames = test_frames
        print(f"✓ Created {len(test_frames)} test frames")
        
        # Try to export (without audio for faster testing)
        results = creator.export_professional_video(include_audio=False, create_variants=False)
        
        if results and results.get('main_video'):
            print(f"✓ Test video created: {os.path.basename(results['main_video'])}")
            return True
        else:
            print("⚠️  Test video creation skipped (dependencies not available)")
            return False
        
    except Exception as e:
        print(f"❌ Test video creation failed: {e}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Run comprehensive test suite."""
    print("🧪 COMPREHENSIVE ENHANCED VIDEO SYSTEM TEST")
    print("=" * 60)
    print("🤲 Testing Imam al-Jawad (AS) Video Creation System")
    print("=" * 60)
    
    test_results = {}
    
    # Run all tests
    test_functions = [
        ('Module Imports', test_imports),
        ('Arabic Renderer', test_arabic_renderer),
        ('Content System', test_content_system),
        ('Video Effects', test_video_effects),
        ('Audio Manager', test_audio_manager),
        ('MP4 Creator', test_mp4_creator),
        ('Enhanced Creator', test_enhanced_video_creator),
        ('Test Video Creation', create_test_video)
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✓ PASSED" if result else "❌ FAILED"
        print(f"{status:<10} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for video creation.")
    elif passed >= total * 0.7:
        print("⚠️  Most tests passed. Some optional features may not be available.")
    else:
        print("❌ Multiple test failures. Please check dependencies and installation.")
    
    print("\n🤲 May Allah bless this work in memory of Imam al-Jawad (AS)")
    
    return test_results

if __name__ == "__main__":
    run_comprehensive_test()
