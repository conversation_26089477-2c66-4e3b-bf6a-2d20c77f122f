<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>في ذكرى وفاة عمتي الغالية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }

        .memorial-container {
            background-color: #fff;
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            padding: 40px;
            width: 500px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border-left: 5px solid #8e44ad;
        }

        .memorial-container::before {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 8px;
            background: linear-gradient(to left, #8e44ad, #2c3e50);
        }

        .header {
            margin-bottom: 30px;
        }

        .date {
            color: #777;
            font-size: 1.1em;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .name {
            font-weight: 700;
            color: #2c3e50;
            font-size: 2em;
            margin: 20px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .message {
            line-height: 1.9;
            color: #444;
            margin-bottom: 25px;
            font-size: 1.1em;
        }

        .divider {
            width: 60%;
            margin: 25px auto;
            border: none;
            height: 1px;
            background: linear-gradient(to left, transparent, #8e44ad, transparent);
        }

        .prayer {
            font-style: italic;
            color: #555;
            margin: 20px 0;
            font-size: 1.1em;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 10px;
            border-right: 3px solid #8e44ad;
        }

        .cause {
            color: #666;
            font-size: 1em;
            margin: 20px 0;
            font-weight: 500;
        }

        .heart-icon {
            color: #e74c3c;
            font-size: 2.5em;
            margin: 15px 0;
            animation: pulse 1.5s infinite;
            text-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
        }

        .hashtags {
            color: #3498db;
            margin-top: 25px;
            font-weight: 500;
        }

        .frame {
            border: 1px solid #eaeaea;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
        }

        .flower-decoration {
            position: absolute;
            opacity: 0.1;
            font-size: 8em;
            z-index: 0;
            color: #8e44ad;
        }

        .flower-top-right {
            top: -20px;
            right: -20px;
            transform: rotate(45deg);
        }

        .flower-bottom-left {
            bottom: -20px;
            left: -20px;
            transform: rotate(225deg);
        }

        .tear-drop {
            display: inline-block;
            margin: 0 5px;
            color: #3498db;
            animation: fall 3s infinite;
            opacity: 0.7;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes fall {
            0% { transform: translateY(-10px); opacity: 0; }
            50% { opacity: 0.7; }
            100% { transform: translateY(10px); opacity: 0; }
        }

        @media (max-width: 600px) {
            .memorial-container {
                width: 100%;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="memorial-container">
        <div class="flower-decoration flower-top-right">✿</div>
        <div class="flower-decoration flower-bottom-left">✿</div>
        
        <div class="header">
            <p class="date">في مثل هذا اليوم قبل ٤ سنوات</p>
            <div class="heart-icon">💔</div>
            <span class="tear-drop">💧</span>
            <span class="tear-drop" style="animation-delay: 0.5s">💧</span>
            <span class="tear-drop" style="animation-delay: 1s">💧</span>
        </div>
        
        <h1 class="name">عمتي الغالية</h1>
        
        <div class="frame">
            <p class="message">رحلت عنا عمتنا الحبيبة، تاركة في قلوبنا فراغ. كانت نبع الحنان والعطاء، ومثال للصبر والإيمان. نفتقد ابتسامتها وكلماتها الطيبة كل يوم.</p>
        </div>
        
        <div class="divider"></div>
        
        <p class="cause">بعد معاناة مع مرض السكري، اختارها الله إلى جواره</p>
        
        <p class="prayer">اللهم ارحمها واغفر لها وأسكنها فسيح جناتك، واجعل قبرها روضة من رياض الجنة، وألهمنا الصبر والسلوان</p>
        
        <div class="divider"></div>
        
        <p class="hashtags">#في_ذكرى_وفاتها #رحمك_الله_يا_عمتي #ذكرى_أليمة</p>
    </div>
</body>
</html>




