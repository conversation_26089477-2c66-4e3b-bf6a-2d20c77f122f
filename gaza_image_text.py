#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os
import textwrap

def add_text_to_image(image_path, output_path, text, font_path=None, font_size=40, 
                      text_color=(255, 255, 255), stroke_color=(0, 0, 0), 
                      stroke_width=2, position='bottom', padding=20):
    """
    Add Arabic text to an image and save the result.
    
    Args:
        image_path (str): Path to the source image
        output_path (str): Path to save the resulting image
        text (str): Arabic text to add to the image
        font_path (str): Path to a TTF font file that supports Arabic
        font_size (int): Font size
        text_color (tuple): RGB color for the text
        stroke_color (tuple): RGB color for the text outline
        stroke_width (int): Width of the text outline
        position (str): Position of the text ('top', 'bottom', 'center')
        padding (int): Padding from the edge of the image
    """
    try:
        # Open the image
        img = Image.open(image_path)
        draw = ImageDraw.Draw(img)
        
        # Set up the font
        if font_path and os.path.exists(font_path):
            try:
                font = ImageFont.truetype(font_path, font_size)
            except Exception as e:
                print(f"Error loading font: {e}")
                # Fall back to default font
                font = ImageFont.load_default()
        else:
            # Try to find a system font that supports Arabic
            try:
                # Common paths for Arabic fonts on different systems
                possible_fonts = [
                    "arial.ttf",  # Windows
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
                    "/System/Library/Fonts/Arial Unicode.ttf",  # macOS
                    "/usr/share/fonts/truetype/freefont/FreeSans.ttf",  # Some Linux
                ]
                
                for font_file in possible_fonts:
                    if os.path.exists(font_file):
                        font = ImageFont.truetype(font_file, font_size)
                        break
                else:
                    # If no suitable font found, use default
                    font = ImageFont.load_default()
                    print("Warning: Using default font which may not support Arabic properly.")
            except Exception:
                font = ImageFont.load_default()
                print("Warning: Using default font which may not support Arabic properly.")
        
        # Wrap text to fit image width
        width = img.width - (2 * padding)
        # Estimate average character width (this is approximate for Arabic)
        avg_char_width = font_size * 0.6
        chars_per_line = int(width / avg_char_width)
        
        # Wrap the text (note: may not work perfectly for Arabic)
        wrapped_text = textwrap.fill(text, width=chars_per_line)
        lines = wrapped_text.split('\n')
        
        # Calculate text height
        line_height = font_size * 1.5
        text_height = len(lines) * line_height
        
        # Determine text position
        if position == 'top':
            y_position = padding
        elif position == 'bottom':
            y_position = img.height - text_height - padding
        else:  # center
            y_position = (img.height - text_height) // 2
        
        # Add semi-transparent background for better readability
        for i, line in enumerate(lines):
            line_y = y_position + (i * line_height)
            text_width, text_height = draw.textsize(line, font=font)
            # Draw text with stroke (outline) for better visibility
            draw.text((padding, line_y), line, font=font, fill=text_color, 
                      stroke_width=stroke_width, stroke_fill=stroke_color)
        
        # Save the image
        img.save(output_path)
        print(f"Image saved to {output_path}")
        return True
    
    except Exception as e:
        print(f"Error processing image: {e}")
        return False

def main():
    # Example usage
    phrases = [
        "صمت العالم أمام معاناة غزة هو شهادة وفاة للضمير الإنساني",
        "عندما يصبح الخبز حلماً والأمان ذكرى، أين العدالة الدولية؟",
        "أطفال غزة يحملون أثقال العالم على أكتافهم الصغيرة",
        "في غزة، الإنسانية تُختبر والعالم يفشل في الامتحان",
        "عندما تتحول المساعدات الإنسانية إلى منّة، تموت كرامة الشعوب",
        "غزة تنزف والعالم يتفرج... أي عدالة هذه؟",
        "الصمت العالمي أمام مأساة غزة جريمة لا تُغتفر",
        "عندما يُحرم الأطفال من طفولتهم، تفقد الإنسانية معناها",
        "في عالم الشعارات الفارغة، غزة تدفع ثمن النفاق الدولي",
        "أين وعود العالم عندما يحمل الأطفال أعباء البقاء على قيد الحياة؟"
    ]
    
    # Path to your image - update this with the correct path
    image_path = "gaza_image.jpg"  # Update this with your image path
    
    # Create output directory if it doesn't exist
    output_dir = "output_images"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Process each phrase
    for i, phrase in enumerate(phrases):
        output_path = os.path.join(output_dir, f"gaza_with_text_{i+1}.jpg")
        add_text_to_image(image_path, output_path, phrase)

if __name__ == "__main__":
    main()
