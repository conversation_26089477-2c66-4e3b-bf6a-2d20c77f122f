#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Video Effects for Professional Islamic Videos
Advanced animations, transitions, and visual effects for <PERSON> condolence videos.
"""

import math
import numpy as np
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os

class EnhancedVideoEffects:
    """Professional video effects for Islamic commemorative videos."""

    def __init__(self, resolution=(1080, 1080)):
        self.width, self.height = resolution
        self.center_x = self.width // 2
        self.center_y = self.height // 2

    def create_islamic_geometric_pattern(self, size, pattern_type='star', color=(255, 215, 0), alpha=50):
        """Create sophisticated Islamic geometric patterns."""
        pattern_img = Image.new('RGBA', size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(pattern_img)

        if pattern_type == 'star':
            self._draw_star_pattern(draw, size, color, alpha)
        elif pattern_type == 'arabesque':
            self._draw_arabesque_pattern(draw, size, color, alpha)
        elif pattern_type == 'geometric_grid':
            self._draw_geometric_grid(draw, size, color, alpha)
        elif pattern_type == 'calligraphy_border':
            self._draw_calligraphy_border(draw, size, color, alpha)

        return pattern_img

    def _draw_star_pattern(self, draw, size, color, alpha):
        """Draw Islamic star pattern."""
        width, height = size
        center_x, center_y = width // 2, height // 2
        
        # Create multiple star layers
        for radius in [100, 150, 200]:
            if radius < min(width, height) // 3:
                points = []
                for i in range(8):  # 8-pointed star
                    angle = i * math.pi / 4
                    # Outer points
                    x1 = center_x + radius * math.cos(angle)
                    y1 = center_y + radius * math.sin(angle)
                    points.append((x1, y1))
                    
                    # Inner points
                    inner_angle = angle + math.pi / 8
                    inner_radius = radius * 0.4
                    x2 = center_x + inner_radius * math.cos(inner_angle)
                    y2 = center_y + inner_radius * math.sin(inner_angle)
                    points.append((x2, y2))

                # Draw star outline
                draw.polygon(points, outline=(*color, alpha), width=2)

    def _draw_arabesque_pattern(self, draw, size, color, alpha):
        """Draw arabesque decorative pattern."""
        width, height = size
        
        # Create flowing curves typical of Islamic art
        for i in range(4):
            start_x = width // 4 + i * (width // 8)
            start_y = height // 4
            
            # Create curved path
            points = []
            for t in range(0, 100, 5):
                x = start_x + 50 * math.sin(t * 0.1)
                y = start_y + t * 2 + 30 * math.cos(t * 0.15)
                if y < height - 50:
                    points.append((x, y))
            
            if len(points) > 1:
                for j in range(len(points) - 1):
                    draw.line([points[j], points[j + 1]], fill=(*color, alpha), width=3)

    def _draw_geometric_grid(self, draw, size, color, alpha):
        """Draw Islamic geometric grid pattern."""
        width, height = size
        grid_size = 60
        
        # Draw diamond grid
        for x in range(0, width + grid_size, grid_size):
            for y in range(0, height + grid_size, grid_size):
                # Diamond shape
                diamond_points = [
                    (x, y - grid_size // 4),
                    (x + grid_size // 4, y),
                    (x, y + grid_size // 4),
                    (x - grid_size // 4, y)
                ]
                draw.polygon(diamond_points, outline=(*color, alpha), width=1)

    def _draw_calligraphy_border(self, draw, size, color, alpha):
        """Draw decorative border inspired by Islamic calligraphy."""
        width, height = size
        border_width = 80
        
        # Top and bottom borders
        for y in [border_width, height - border_width]:
            for x in range(border_width, width - border_width, 40):
                # Decorative elements
                draw.arc([x - 15, y - 15, x + 15, y + 15], 0, 180, fill=(*color, alpha), width=2)
                draw.arc([x - 10, y - 10, x + 10, y + 10], 180, 360, fill=(*color, alpha), width=2)

        # Left and right borders
        for x in [border_width, width - border_width]:
            for y in range(border_width, height - border_width, 40):
                draw.arc([x - 15, y - 15, x + 15, y + 15], 90, 270, fill=(*color, alpha), width=2)

    def apply_fade_transition(self, img, progress, fade_type='gentle'):
        """Apply sophisticated fade transitions."""
        if fade_type == 'gentle':
            alpha = int(255 * min(1.0, progress * 1.2))
        elif fade_type == 'dramatic':
            alpha = int(255 * (progress ** 0.5))
        elif fade_type == 'divine':
            # Pulsing fade effect
            pulse = math.sin(progress * math.pi * 2) * 0.3 + 0.7
            alpha = int(255 * progress * pulse)
        else:
            alpha = int(255 * progress)

        # Apply alpha to image
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        # Create alpha mask
        alpha_mask = Image.new('L', img.size, alpha)
        img.putalpha(alpha_mask)
        
        return img

    def create_text_entrance_effect(self, img, text_position, text_size, progress, effect_type='slide_up'):
        """Create sophisticated text entrance effects."""
        if effect_type == 'slide_up':
            offset_y = int(50 * (1 - progress))
            new_position = (text_position[0], text_position[1] + offset_y)
            return new_position
            
        elif effect_type == 'zoom_in':
            scale = 0.5 + 0.5 * progress
            return text_position, scale
            
        elif effect_type == 'fade_with_glow':
            glow_radius = int(progress * 8)
            return text_position, glow_radius
            
        elif effect_type == 'typewriter':
            # Character-by-character reveal
            chars_to_show = int(len(text_position) * progress) if hasattr(text_position, '__len__') else progress
            return chars_to_show
            
        return text_position

    def add_divine_light_effect(self, img, center_point, progress, intensity=0.3):
        """Add divine light effect radiating from a point."""
        # Create light overlay
        light_img = Image.new('RGBA', img.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(light_img)
        
        x, y = center_point
        max_radius = int(min(self.width, self.height) * 0.6 * progress)
        
        # Create multiple light rings
        for i in range(5):
            radius = max_radius - i * 20
            if radius > 0:
                alpha = int(intensity * 255 * (1 - i * 0.2) * progress)
                light_color = (255, 255, 255, alpha)
                
                # Draw light circle
                draw.ellipse([x - radius, y - radius, x + radius, y + radius],
                           fill=light_color)
        
        # Apply blur for soft light effect
        light_img = light_img.filter(ImageFilter.GaussianBlur(radius=10))
        
        # Blend with original image
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        return Image.alpha_composite(img, light_img)

    def create_particle_effect(self, img, progress, particle_type='stars'):
        """Create particle effects for enhanced visual appeal."""
        particle_img = Image.new('RGBA', img.size, (0, 0, 0, 0))
        draw = ImageDraw.Draw(particle_img)
        
        if particle_type == 'stars':
            # Twinkling stars effect
            num_particles = int(20 * progress)
            for i in range(num_particles):
                x = (i * 137) % self.width  # Pseudo-random distribution
                y = (i * 211) % self.height
                
                # Twinkling effect
                twinkle = math.sin(progress * math.pi * 4 + i) * 0.5 + 0.5
                alpha = int(twinkle * 150)
                
                # Draw star
                star_size = 3
                draw.ellipse([x - star_size, y - star_size, x + star_size, y + star_size],
                           fill=(255, 255, 255, alpha))
        
        elif particle_type == 'light_rays':
            # Radiating light rays
            center_x, center_y = self.center_x, self.center_y
            num_rays = 12
            
            for i in range(num_rays):
                angle = i * (2 * math.pi / num_rays)
                length = int(min(self.width, self.height) * 0.4 * progress)
                
                end_x = center_x + length * math.cos(angle)
                end_y = center_y + length * math.sin(angle)
                
                alpha = int(100 * progress)
                draw.line([(center_x, center_y), (end_x, end_y)],
                         fill=(255, 215, 0, alpha), width=2)
        
        # Blend particles with image
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        
        return Image.alpha_composite(img, particle_img)

    def apply_professional_color_grading(self, img, style='warm_gold'):
        """Apply professional color grading for cinematic look."""
        if style == 'warm_gold':
            # Enhance warm tones
            enhancer = ImageEnhance.Color(img)
            img = enhancer.enhance(1.1)  # Slight color boost
            
            # Add warm tint
            warm_overlay = Image.new('RGBA', img.size, (255, 215, 0, 20))
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            img = Image.alpha_composite(img, warm_overlay)
            
        elif style == 'cool_blue':
            # Cool, contemplative tones
            enhancer = ImageEnhance.Color(img)
            img = enhancer.enhance(0.9)
            
            cool_overlay = Image.new('RGBA', img.size, (100, 149, 237, 15))
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            img = Image.alpha_composite(img, cool_overlay)
            
        elif style == 'monochrome_elegant':
            # Elegant monochrome with slight sepia
            img = img.convert('L')  # Convert to grayscale
            img = img.convert('RGB')
            
            # Add sepia tint
            sepia_overlay = Image.new('RGBA', img.size, (139, 69, 19, 30))
            img = img.convert('RGBA')
            img = Image.alpha_composite(img, sepia_overlay)
        
        return img

    def create_smooth_transition(self, img1, img2, progress, transition_type='crossfade'):
        """Create smooth transitions between scenes."""
        if transition_type == 'crossfade':
            # Simple crossfade
            alpha1 = int(255 * (1 - progress))
            alpha2 = int(255 * progress)
            
            img1_alpha = img1.copy()
            img2_alpha = img2.copy()
            
            if img1_alpha.mode != 'RGBA':
                img1_alpha = img1_alpha.convert('RGBA')
            if img2_alpha.mode != 'RGBA':
                img2_alpha = img2_alpha.convert('RGBA')
            
            # Apply alpha
            img1_alpha.putalpha(alpha1)
            img2_alpha.putalpha(alpha2)
            
            # Composite
            result = Image.alpha_composite(img1_alpha, img2_alpha)
            return result
            
        elif transition_type == 'slide_left':
            # Slide transition
            offset = int(self.width * progress)
            result = Image.new('RGB', (self.width, self.height))
            
            # Position images
            result.paste(img1, (-offset, 0))
            result.paste(img2, (self.width - offset, 0))
            
            return result
        
        return img2 if progress > 0.5 else img1

def test_enhanced_effects():
    """Test the enhanced video effects."""
    print("Testing Enhanced Video Effects...")
    print("=" * 40)

    effects = EnhancedVideoEffects((800, 600))
    
    # Test pattern creation
    pattern = effects.create_islamic_geometric_pattern((400, 300), 'star')
    print(f"✓ Islamic pattern created: {pattern.size}")
    
    # Test fade transition
    test_img = Image.new('RGB', (400, 300), (100, 100, 100))
    faded_img = effects.apply_fade_transition(test_img, 0.5)
    print(f"✓ Fade transition applied: {faded_img.mode}")
    
    # Test color grading
    graded_img = effects.apply_professional_color_grading(test_img, 'warm_gold')
    print(f"✓ Color grading applied: {graded_img.mode}")
    
    print("✓ Enhanced effects test completed")

if __name__ == "__main__":
    test_enhanced_effects()
