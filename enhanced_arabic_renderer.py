#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Arabic Text Renderer for Islamic Designs
Provides proper Arabic text rendering with RTL support, ligatures, and high-quality typography.
"""

from PIL import Image, ImageDraw, ImageFont
import os
import sys

# Try to import Arabic text processing libraries
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("✓ Arabic text processing libraries loaded successfully")
except ImportError as e:
    ARABIC_SUPPORT = False
    print(f"⚠️  Arabic libraries not available: {e}")
    print("   Install with: pip install arabic-reshaper python-bidi")

class ArabicTextRenderer:
    """Enhanced Arabic text renderer with proper RTL support and typography."""

    def __init__(self):
        self.arabic_fonts = self._find_arabic_fonts()
        self.fallback_fonts = self._find_fallback_fonts()

    def _find_arabic_fonts(self):
        """Find high-quality Arabic fonts on the system."""
        arabic_font_paths = [
            # Windows Arabic fonts
            "C:\\Windows\\Fonts\\arial.ttf",
            "C:\\Windows\\Fonts\\calibri.ttf",
            "C:\\Windows\\Fonts\\tahoma.ttf",
            "C:\\Windows\\Fonts\\segoeui.ttf",
            "C:\\Windows\\Fonts\\times.ttf",
            "C:\\Windows\\Fonts\\trebuc.ttf",

            # Specialized Arabic fonts (if installed)
            "C:\\Windows\\Fonts\\AmiriQuran.ttf",
            "C:\\Windows\\Fonts\\Amiri-Regular.ttf",
            "C:\\Windows\\Fonts\\Scheherazade-Regular.ttf",
            "C:\\Windows\\Fonts\\NotoSansArabic-Regular.ttf",
            "C:\\Windows\\Fonts\\TraditionalArabic.ttf",

            # Linux Arabic fonts
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansArabic-Regular.ttf",
            "/usr/share/fonts/truetype/amiri/Amiri-Regular.ttf",
            "/usr/share/fonts/truetype/scheherazade/Scheherazade-Regular.ttf",

            # macOS Arabic fonts
            "/System/Library/Fonts/Arial Unicode.ttf",
            "/System/Library/Fonts/GeezaPro.ttc",
            "/System/Library/Fonts/Baghdad.ttf",
            "/Library/Fonts/Amiri.ttf",

            # Relative paths (if fonts are in project directory)
            "fonts/Amiri-Regular.ttf",
            "fonts/Scheherazade-Regular.ttf",
            "fonts/NotoSansArabic-Regular.ttf",
        ]

        available_fonts = []
        for font_path in arabic_font_paths:
            if os.path.exists(font_path):
                try:
                    # Test if font can be loaded
                    test_font = ImageFont.truetype(font_path, 20)
                    available_fonts.append(font_path)
                    print(f"✓ Found Arabic font: {os.path.basename(font_path)}")
                except Exception:
                    continue

        if not available_fonts:
            print("⚠️  No specialized Arabic fonts found, using system defaults")

        return available_fonts

    def _find_fallback_fonts(self):
        """Find fallback fonts that support Arabic characters."""
        fallback_paths = [
            "arial.ttf",
            "calibri.ttf",
            "tahoma.ttf"
        ]

        available_fallbacks = []
        for font_name in fallback_paths:
            try:
                test_font = ImageFont.truetype(font_name, 20)
                available_fallbacks.append(font_name)
            except Exception:
                continue

        return available_fallbacks

    def get_font(self, size, font_type="regular"):
        """Get the best available Arabic font for the given size."""
        # Priority order: specialized Arabic fonts > system fonts > fallback
        font_candidates = self.arabic_fonts + self.fallback_fonts

        for font_path in font_candidates:
            try:
                font = ImageFont.truetype(font_path, size)
                return font, os.path.basename(font_path)
            except Exception:
                continue

        # Ultimate fallback
        print(f"⚠️  Using default font for size {size}")
        return ImageFont.load_default(), "default"

    def process_arabic_text(self, text):
        """Process Arabic text for proper RTL rendering with ligatures."""
        if not ARABIC_SUPPORT:
            print("⚠️  Arabic processing not available, returning original text")
            return text

        try:
            # Create reshaper with configuration
            reshaper = arabic_reshaper.ArabicReshaper(
                configuration={
                    'delete_harakat': False,  # Keep diacritical marks
                    'support_zwj': True,      # Support zero-width joiner
                    'support_zwnj': True,     # Support zero-width non-joiner
                    'use_unshaped_instead_of_isolated': False,
                }
            )

            # Reshape Arabic text to handle ligatures and connections
            reshaped_text = reshaper.reshape(text)

            # Apply bidirectional algorithm for proper RTL display
            bidi_text = get_display(reshaped_text)

            return bidi_text

        except Exception as e:
            # Fallback to simple reshaping
            try:
                reshaped_text = arabic_reshaper.reshape(text)
                bidi_text = get_display(reshaped_text)
                return bidi_text
            except Exception as e2:
                print(f"⚠️  Error processing Arabic text: {e2}")
                return text

    def get_text_dimensions(self, text, font):
        """Get accurate text dimensions for Arabic text."""
        # Create a temporary image to measure text
        temp_img = Image.new('RGB', (1, 1))
        temp_draw = ImageDraw.Draw(temp_img)

        # Process the text first
        processed_text = self.process_arabic_text(text)

        # Get bounding box
        bbox = temp_draw.textbbox((0, 0), processed_text, font=font)
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]

        return width, height, processed_text

    def draw_text_with_shadow(self, draw, position, text, font, fill_color, shadow_color=(0, 0, 0), shadow_offset=(2, 2)):
        """Draw Arabic text with shadow for better visibility."""
        processed_text = self.process_arabic_text(text)

        # Draw shadow first
        shadow_pos = (position[0] + shadow_offset[0], position[1] + shadow_offset[1])
        draw.text(shadow_pos, processed_text, font=font, fill=shadow_color)

        # Draw main text
        draw.text(position, processed_text, font=font, fill=fill_color)

        return processed_text

    def draw_text_with_outline(self, draw, position, text, font, fill_color, outline_color=(0, 0, 0), outline_width=1):
        """Draw Arabic text with outline for better contrast."""
        processed_text = self.process_arabic_text(text)

        # Draw outline by drawing text in multiple positions
        for dx in range(-outline_width, outline_width + 1):
            for dy in range(-outline_width, outline_width + 1):
                if dx != 0 or dy != 0:
                    outline_pos = (position[0] + dx, position[1] + dy)
                    draw.text(outline_pos, processed_text, font=font, fill=outline_color)

        # Draw main text
        draw.text(position, processed_text, font=font, fill=fill_color)

        return processed_text

    def draw_centered_arabic_text(self, draw, text, y_position, canvas_width, font, color,
                                 shadow=True, outline=False, shadow_color=(0, 0, 0, 128),
                                 outline_color=(0, 0, 0), shadow_offset=(2, 2), outline_width=1):
        """Draw centered Arabic text with optional shadow or outline."""

        # Get text dimensions
        text_width, text_height, processed_text = self.get_text_dimensions(text, font)

        # Calculate centered position
        x_position = (canvas_width - text_width) // 2
        position = (x_position, y_position)

        # Draw with effects
        if outline:
            self.draw_text_with_outline(draw, position, text, font, color, outline_color, outline_width)
        elif shadow:
            self.draw_text_with_shadow(draw, position, text, font, color, shadow_color, shadow_offset)
        else:
            processed_text = self.process_arabic_text(text)
            draw.text(position, processed_text, font=font, fill=color)

        return {
            'position': position,
            'width': text_width,
            'height': text_height,
            'processed_text': processed_text
        }

def test_arabic_rendering():
    """Test function to verify Arabic rendering capabilities."""
    print("Testing Arabic Text Rendering...")
    print("=" * 40)

    renderer = ArabicTextRenderer()

    # Test texts
    test_texts = [
        "بسم الله الرحمن الرحيم",
        "الإمام محمد الجواد عليه السلام",
        "أحيا الله ذكراكم وأعظم أجوركم"
    ]

    for i, text in enumerate(test_texts):
        print(f"\nTest {i+1}: {text}")
        processed = renderer.process_arabic_text(text)
        print(f"Processed: {processed}")

        font, font_name = renderer.get_font(24)
        print(f"Font: {font_name}")

        width, height, _ = renderer.get_text_dimensions(text, font)
        print(f"Dimensions: {width}x{height}")

    print("\n✓ Arabic rendering test completed")

if __name__ == "__main__":
    test_arabic_rendering()
