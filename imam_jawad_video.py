import os
from moviepy.editor import VideoFileClip, TextClip, CompositeVideoClip, ColorClip, AudioFileClip, ImageClip
from moviepy.video.tools.subtitles import SubtitlesClip
import arabic_reshaper
from bidi.algorithm import get_display
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pygame
from islamic_patterns import combine_patterns

# Constants
VIDEO_WIDTH = 1080
VIDEO_HEIGHT = 1080
FPS = 30
DURATION = 45  # seconds

# Arabic text content
ARABIC_TEXTS = {
    'title': 'بسم الله الرحمن الرحيم',
    'main_title': 'إحياء ذكرى استشهاد الإمام محمد الجواد عليه السلام',
    'biography': 'الإمام محمد الجواد (ع) هو التاسع من أئمة أهل البيت (ع)',
    'martyrdom': 'استشهد الإمام الجواد (ع) في الخامس والعشرين من ذي القعدة سنة 220 هـ',
    'supplication': 'اللهم صل على محمد وآل محمد'
}

# English translations for subtitles
ENGLISH_SUBTITLES = {
    'title': 'In the name of Allah, the Most Gracious, the Most Merciful',
    'main_title': 'Commemorating the Martyrdom of Imam Muhammad al-Jawad (peace be upon him)',
    'biography': 'Imam Muhammad al-Jawad (a.s.) is the ninth of the Ahlul Bayt (a.s.)',
    'martyrdom': 'Imam al-Jawad (a.s.) was martyred on the 25th of Dhul Qadah, 220 AH',
    'supplication': 'O Allah, send blessings upon Muhammad and the family of Muhammad'
}

def create_arabic_text(text, font_size=60, color='white'):
    """Create Arabic text with proper RTL rendering"""
    reshaped_text = arabic_reshaper.reshape(text)
    bidi_text = get_display(reshaped_text)
    
    # Create text clip with Arabic font
    txt_clip = TextClip(bidi_text, fontsize=font_size, color=color, 
                       font='Arial', method='caption', align='center')
    return txt_clip

def create_background():
    """Create a background with Islamic pattern"""
    # Generate Islamic pattern background
    pattern = combine_patterns((VIDEO_WIDTH, VIDEO_HEIGHT), pattern_type='geometric')
    
    # Convert PIL Image to numpy array for MoviePy
    pattern_array = np.array(pattern)
    
    # Create video clip from the pattern
    bg_clip = ImageClip(pattern_array).set_duration(DURATION)
    return bg_clip

def create_title_sequence():
    """Create the opening title sequence"""
    title_clip = create_arabic_text(ARABIC_TEXTS['title'], font_size=80)
    title_clip = title_clip.set_position('center').set_duration(5)
    title_clip = title_clip.crossfadein(1).crossfadeout(1)
    return title_clip

def create_main_content():
    """Create the main content sequence"""
    clips = []
    
    # Main title
    main_title = create_arabic_text(ARABIC_TEXTS['main_title'], font_size=70)
    main_title = main_title.set_position('center').set_duration(8)
    main_title = main_title.crossfadein(1).crossfadeout(1)
    clips.append(main_title)
    
    # Biography
    bio = create_arabic_text(ARABIC_TEXTS['biography'], font_size=50)
    bio = bio.set_position('center').set_duration(10)
    bio = bio.crossfadein(1).crossfadeout(1)
    clips.append(bio)
    
    # Martyrdom
    martyr = create_arabic_text(ARABIC_TEXTS['martyrdom'], font_size=50)
    martyr = martyr.set_position('center').set_duration(10)
    martyr = martyr.crossfadein(1).crossfadeout(1)
    clips.append(martyr)
    
    # Supplication
    supp = create_arabic_text(ARABIC_TEXTS['supplication'], font_size=60)
    supp = supp.set_position('center').set_duration(8)
    supp = supp.crossfadein(1).crossfadeout(1)
    clips.append(supp)
    
    return clips

def create_subtitles():
    """Create English subtitles"""
    subtitles = []
    current_time = 0
    
    for key, text in ENGLISH_SUBTITLES.items():
        duration = 8 if key == 'title' else 10
        subtitles.append(((current_time, current_time + duration), text))
        current_time += duration
    
    return subtitles

def main():
    # Create background
    background = create_background()
    
    # Create title sequence
    title_sequence = create_title_sequence()
    
    # Create main content
    main_content = create_main_content()
    
    # Create subtitles
    subtitles = create_subtitles()
    subs_clip = SubtitlesClip(subtitles, lambda txt: TextClip(txt, font='Arial', 
                                                             fontsize=30, color='white'))
    
    # Combine all clips
    final_clip = CompositeVideoClip([background] + [title_sequence] + main_content + [subs_clip])
    
    # Add background audio (you'll need to provide the audio file)
    # audio = AudioFileClip("background_audio.mp3")
    # final_clip = final_clip.set_audio(audio)
    
    # Write the result to a file
    final_clip.write_videofile("imam_jawad_commemoration.mp4", 
                             fps=FPS, 
                             codec='libx264', 
                             audio_codec='aac')

if __name__ == "__main__":
    main() 