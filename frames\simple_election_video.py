"""
منشئ فيديو التوعية الانتخابية المبسط
يقوم بإنشاء عرض شرائح بسيط بناءً على نص الرسالة وحفظه على سطح المكتب
"""

import os
import sys
import time
from PIL import Image, ImageDraw, ImageFont, ImageSequence
import numpy as np
from datetime import datetime

# المسار للحفظ على سطح المكتب
DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")
OUTPUT_FILE = os.path.join(DESKTOP_PATH, f"election_awareness_{datetime.now().strftime('%Y%m%d_%H%M%S')}.gif")

# الألوان المستخدمة (ألوان العلم العراقي)
COLORS = {
    "red": (206, 17, 38),
    "black": (0, 0, 0),
    "white": (255, 255, 255),
    "green": (0, 131, 66),
    "gold": (255, 215, 0)
}

def create_text_image(text, font_size=60, color=(255, 255, 255), bg_color=(0, 0, 0), 
                     size=(800, 600), position=('center', 'center')):
    """إنشاء صورة تحتوي على نص"""
    
    # إنشاء صورة فارغة
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # تحديد الخط
    try:
        font = ImageFont.truetype("Arial.ttf", font_size)
    except IOError:
        font = ImageFont.load_default()
    
    # تقسيم النص إلى أسطر
    lines = text.strip().split('\n')
    
    # حساب الارتفاع الكلي للنص
    line_height = font_size * 1.5
    text_height = len(lines) * line_height
    
    # حساب نقطة البداية للنص (بحيث يكون وسط النص في وسط الصورة)
    if position[1] == 'center':
        y_start = (size[1] - text_height) // 2
    else:
        y_start = position[1]
    
    # رسم كل سطر
    for i, line in enumerate(lines):
        # حساب عرض النص
        try:
            text_width = draw.textsize(line, font=font)[0]
        except AttributeError:
            text_width = draw.textlength(line, font=font)
        
        # حساب موضع النص
        if position[0] == 'center':
            x = (size[0] - text_width) // 2
        else:
            x = position[0]
        
        y = y_start + i * line_height
        
        # رسم النص
        draw.text((x, y), line, font=font, fill=color)
    
    return img

def create_flag_background(size=(800, 600)):
    """إنشاء خلفية تشبه العلم العراقي"""
    
    img = Image.new('RGB', size, COLORS["white"])
    draw = ImageDraw.Draw(img)
    
    # رسم الأجزاء الثلاثة للعلم
    height = size[1] // 3
    
    # الجزء العلوي (أحمر)
    draw.rectangle([0, 0, size[0], height], fill=COLORS["red"])
    
    # الجزء الوسط (أبيض مع "الله أكبر")
    # الجزء السفلي (أسود)
    draw.rectangle([0, height*2, size[0], size[1]], fill=COLORS["black"])
    
    return img

def create_election_slides():
    """إنشاء شرائح فيديو التوعية الانتخابية"""
    
    slides = []
    size = (800, 600)
    
    # المشهد 1: البسملة
    bg = create_flag_background(size)
    text_img = create_text_image(
        "بسم الله الرحمن الرحيم",
        font_size=50,
        color=COLORS["white"],
        bg_color=None,
        size=size
    )
    
    # دمج النص مع الخلفية
    composite = Image.new('RGB', size)
    composite.paste(bg)
    composite.paste(text_img, (0, 0), mask=Image.new('L', size, 128))
    slides.append(composite)
    
    # المشهد 2: الترحيب
    slides.append(create_text_image(
        "إلى أبناء شعبنا العراقي العظيم",
        font_size=50,
        color=COLORS["green"],
        bg_color=COLORS["white"],
        size=size
    ))
    
    # المشهد 3: الأمانة
    slides.append(create_text_image(
        "صوتك أمانة... صوتك مسؤولية... صوتك مستقبل",
        font_size=40,
        color=COLORS["white"],
        bg_color=COLORS["black"],
        size=size
    ))
    
    # المشهد 4: المشاركة
    slides.append(create_text_image(
        """إن المشاركة في الانتخابات البرلمانية
ليست مجرد حق دستوري
بل هي واجب وطني مقدس""",
        font_size=40,
        color=COLORS["red"],
        bg_color=COLORS["white"],
        size=size
    ))
    
    # المشهد 5: أهمية التصويت
    slides.append(create_text_image(
        """عندما تدلي بصوتك،
فأنت تختار من سيمثلك ويحمل همومك
ويعمل على تلبية طموحاتك""",
        font_size=35,
        color=COLORS["white"],
        bg_color=COLORS["green"],
        size=size
    ))
    
    # المشهد 6: المشاركة في صناعة القرار
    slides.append(create_text_image(
        """لا تدع الآخرين يقررون مستقبلك نيابة عنك
لا تترك مقعدك فارغاً في صناعة القرار""",
        font_size=35,
        color=COLORS["black"],
        bg_color=COLORS["white"],
        size=size
    ))
    
    # المشهد 7: التغيير
    slides.append(create_text_image(
        """مهما كانت التحديات والصعوبات،
فإن التغيير يبدأ بخطوة واحدة""",
        font_size=40,
        color=COLORS["white"],
        bg_color=COLORS["red"],
        size=size
    ))
    
    # المشهد 8: شارك
    slides.append(create_text_image(
        "شارك... انتخب... غيّر",
        font_size=60,
        color=COLORS["gold"],
        bg_color=COLORS["black"],
        size=size
    ))
    
    # المشهد 9: مستقبل
    slides.append(create_text_image(
        """من أجل عراق أفضل، عراق تستحقه الأجيال القادمة
من أجل بناء مؤسسات الدولة وتعزيز الديمقراطية""",
        font_size=30,
        color=COLORS["white"],
        bg_color=COLORS["green"],
        size=size
    ))
    
    # المشهد 10: معاً
    slides.append(create_text_image(
        """معاً نحو انتخابات نزيهة وشفافة
تعبر عن إرادة الشعب العراقي الحر""",
        font_size=35,
        color=COLORS["black"],
        bg_color=COLORS["white"],
        size=size
    ))
    
    # المشهد 11: الختام
    slides.append(create_text_image(
        """صوتك... قوة
صوتك... أمانة
صوتك... وطن

شارك في الانتخابات البرلمانية""",
        font_size=45,
        color=COLORS["gold"],
        bg_color=COLORS["black"],
        size=size
    ))
    
    return slides

def save_as_gif(slides, output_file, duration=2000):
    """حفظ الشرائح كملف GIF متحرك"""
    
    slides[0].save(
        output_file,
        save_all=True,
        append_images=slides[1:],
        duration=duration,  # مدة كل شريحة بالميلي ثانية
        loop=0  # 0 = تكرار لا نهائي
    )
    
    return output_file

def main():
    """الدالة الرئيسية لإنشاء عرض الشرائح"""
    
    print("بدء إنشاء عرض شرائح التوعية الانتخابية...")
    
    try:
        # إنشاء الشرائح
        slides = create_election_slides()
        
        # حفظ كملف GIF
        output_path = save_as_gif(slides, OUTPUT_FILE)
        
        print(f"تم إنشاء عرض الشرائح بنجاح وحفظه على سطح المكتب: {output_path}")
        
        # فتح الملف
        if sys.platform == "win32":
            os.system(f'start "{output_path}"')
        elif sys.platform == "darwin":  # macOS
            os.system(f'open "{output_path}"')
        else:  # Linux
            os.system(f'xdg-open "{output_path}"')
        
        return output_path
    
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء عرض الشرائح: {str(e)}")
        print("تأكد من تثبيت المكتبات اللازمة باستخدام الأمر:")
        print("pip install pillow numpy")
        return None

if __name__ == "__main__":
    main() 