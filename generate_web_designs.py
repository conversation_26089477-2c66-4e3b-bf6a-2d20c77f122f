#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Generate all web-based Imam <PERSON> condolence design variations
Creates HTML files for each color scheme and text effect combination
"""

import os

def create_design_html(theme_name, theme_config, effect_type):
    """Create HTML file for a specific design variation."""
    
    # Arabic text content
    arabic_texts = {
        'bismillah': 'بسم الله الرحمن الرحيم',
        'condolence_intro': 'بمناسبة ذكرى استشهاد',
        'imam_name': 'الإمام محمد الجواد عليه السلام',
        'date': 'التاسع والعشرون من ذي القعدة',
        'condolence': 'أحيا الله ذكراكم وأعظم أجوركم',
        'salawat': 'اللهم صل على محمد وآل محمد'
    }
    
    # Navigation links
    shadow_file = f"{theme_name}-shadow.html"
    outline_file = f"{theme_name}-outline.html"
    
    # Next design in sequence
    next_designs = {
        'classic': 'elegant',
        'elegant': 'traditional', 
        'traditional': 'royal',
        'royal': 'classic'
    }
    
    next_theme = next_designs[theme_name]
    next_file = f"{next_theme}-shadow.html"
    
    # Effect-specific styles and classes
    if effect_type == 'shadow':
        effect_class = 'enhanced-shadow'
        effect_styles = '''
        /* Enhanced shadow effects */
        .enhanced-shadow {
            text-shadow: 
                3px 3px 6px rgba(0, 0, 0, 0.8),
                0 0 10px rgba(''' + theme_config['primary_rgb'] + ''', 0.3);
        }

        .enhanced-shadow.secondary {
            text-shadow: 
                3px 3px 6px rgba(0, 0, 0, 0.8),
                0 0 8px rgba(255, 255, 255, 0.2);
        }
        
        /* Add subtle animation */
        .text-element {
            animation: fadeInUp 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }'''
        
        other_version = outline_file
        other_version_text = 'نسخة الحدود'
        
    else:  # outline
        effect_class = 'enhanced-outline'
        effect_styles = '''
        /* Enhanced outline effects */
        .enhanced-outline {
            text-shadow: 
                -2px -2px 0 #000,
                2px -2px 0 #000,
                -2px 2px 0 #000,
                2px 2px 0 #000,
                -2px 0 0 #000,
                2px 0 0 #000,
                0 -2px 0 #000,
                0 2px 0 #000,
                -3px -3px 0 #000,
                3px -3px 0 #000,
                -3px 3px 0 #000,
                3px 3px 0 #000;
        }

        .enhanced-outline.secondary {
            text-shadow: 
                -2px -2px 0 #333,
                2px -2px 0 #333,
                -2px 2px 0 #333,
                2px 2px 0 #333,
                -2px 0 0 #333,
                2px 0 0 #333,
                0 -2px 0 #333,
                0 2px 0 #333;
        }
        
        /* Add subtle animation */
        .text-element {
            animation: slideInRight 0.8s ease-out forwards;
            opacity: 0;
            transform: translateX(-30px);
        }

        @keyframes slideInRight {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }'''
        
        other_version = shadow_file
        other_version_text = 'نسخة الظلال'

    # Animation delays
    animation_delays = '''
        .text-element:nth-child(1) { animation-delay: 0.2s; }
        .text-element:nth-child(2) { animation-delay: 0.4s; }
        .text-element:nth-child(3) { animation-delay: 0.6s; }
        .text-element:nth-child(4) { animation-delay: 0.8s; }
        .text-element:nth-child(5) { animation-delay: 1.0s; }
        .text-element:nth-child(6) { animation-delay: 1.2s; }'''

    # Generate HTML content
    html_content = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم العزاء {theme_config['arabic_name']} - الإمام الجواد عليه السلام</title>
    <meta name="description" content="تصميم العزاء {theme_config['arabic_name']} لإحياء ذكرى استشهاد الإمام محمد الجواد عليه السلام">
    <link rel="stylesheet" href="styles/common.css">
    <style>
        body {{
            background: {theme_config['body_bg']};
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }}

        .design-container {{
            background: {theme_config['container_bg']};
        }}

        {effect_styles}

        {animation_delays}

        /* Responsive font adjustments */
        @media (max-width: 480px) {{
            .imam-name {{
                font-size: clamp(1.3rem, 4vmin, 2.5rem);
                line-height: 1.1;
            }}
            
            .condolence-phrase {{
                font-size: clamp(1.1rem, 3.5vmin, 2rem);
                line-height: 1.2;
            }}
        }}
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navigation">
        <a href="index.html">← العودة للرئيسية</a>
        <a href="{other_version}">{other_version_text}</a>
    </nav>

    <!-- Main Design Container -->
    <div class="design-container {theme_name}-theme">
        <!-- Corner Decorations -->
        <div class="corner-decoration top-left"></div>
        <div class="corner-decoration top-right"></div>
        <div class="corner-decoration bottom-left"></div>
        <div class="corner-decoration bottom-right"></div>

        <!-- Text Content -->
        <h1 class="text-element bismillah arabic-text {effect_class} secondary">
            {arabic_texts['bismillah']}
        </h1>

        <h2 class="text-element condolence-intro arabic-text {effect_class}">
            {arabic_texts['condolence_intro']}
        </h2>

        <h1 class="text-element imam-name arabic-text {effect_class}">
            {arabic_texts['imam_name']}
        </h1>

        <h3 class="text-element date arabic-text {effect_class} secondary">
            {arabic_texts['date']}
        </h3>

        <h2 class="text-element condolence-phrase arabic-text {effect_class}">
            {arabic_texts['condolence']}
        </h2>

        <h3 class="text-element salawat arabic-text {effect_class} secondary">
            {arabic_texts['salawat']}
        </h3>

        <!-- Decorative Line -->
        <div class="decorative-line"></div>

        <!-- Watermark -->
        <div class="watermark">AliToucan</div>
    </div>

    <script>
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {{
            switch(e.key) {{
                case 'Escape':
                    window.location.href = 'index.html';
                    break;
                case 'ArrowRight':
                    window.location.href = '{next_file}';
                    break;
                case 'ArrowLeft':
                    window.location.href = '{other_version}';
                    break;
                case 's':
                case 'S':
                    if (navigator.share) {{
                        navigator.share({{
                            title: 'تصميم العزاء للإمام الجواد عليه السلام - {theme_config['arabic_name']}',
                            text: 'بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام',
                            url: window.location.href
                        }});
                    }} else {{
                        navigator.clipboard.writeText(window.location.href).then(() => {{
                            alert('تم نسخ الرابط إلى الحافظة');
                        }});
                    }}
                    break;
                case 'p':
                case 'P':
                    window.print();
                    break;
            }}
        }});

        // Add touch gestures for mobile
        let touchStartX = 0;
        let touchStartY = 0;

        document.addEventListener('touchstart', function(e) {{
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        }});

        document.addEventListener('touchend', function(e) {{
            if (!touchStartX || !touchStartY) return;

            let touchEndX = e.changedTouches[0].clientX;
            let touchEndY = e.changedTouches[0].clientY;

            let diffX = touchStartX - touchEndX;
            let diffY = touchStartY - touchEndY;

            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {{
                if (diffX > 0) {{
                    // Swipe left - go to next design
                    window.location.href = '{next_file}';
                }} else {{
                    // Swipe right - go to other version
                    window.location.href = '{other_version}';
                }}
            }}

            touchStartX = 0;
            touchStartY = 0;
        }});

        // Preload next designs
        ['{next_file}', '{other_version}'].forEach(page => {{
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = page;
            document.head.appendChild(link);
        }});

        // Add meta tags for social sharing
        const metaTags = [
            {{ property: 'og:title', content: 'تصميم العزاء للإمام الجواد عليه السلام - {theme_config['arabic_name']}' }},
            {{ property: 'og:description', content: 'بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام - التاسع والعشرون من ذي القعدة' }},
            {{ property: 'og:type', content: 'website' }},
            {{ property: 'og:url', content: window.location.href }}
        ];

        metaTags.forEach(tag => {{
            const meta = document.createElement('meta');
            if (tag.property) meta.setAttribute('property', tag.property);
            if (tag.name) meta.setAttribute('name', tag.name);
            meta.setAttribute('content', tag.content);
            document.head.appendChild(meta);
        }});
    </script>
</body>
</html>'''

    return html_content

def main():
    """Generate all web design variations."""
    
    # Theme configurations
    themes = {
        'elegant': {
            'arabic_name': 'الأنيق الرمادي والفضي',
            'body_bg': '#1a1a1a',
            'container_bg': '#191919',
            'primary_rgb': '200, 200, 200'
        },
        'traditional': {
            'arabic_name': 'التقليدي الأسود والأبيض',
            'body_bg': '#1a1a1a', 
            'container_bg': '#000000',
            'primary_rgb': '255, 255, 255'
        },
        'royal': {
            'arabic_name': 'الملكي الأزرق والذهبي',
            'body_bg': '#0f0f1a',
            'container_bg': '#141428',
            'primary_rgb': '255, 215, 0'
        }
    }
    
    # Create output directory
    output_dir = "imam_jawad_web_designs"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created directory: {output_dir}")
    
    # Generate all design variations
    generated_files = []
    
    for theme_name, theme_config in themes.items():
        for effect_type in ['shadow', 'outline']:
            filename = f"{theme_name}-{effect_type}.html"
            filepath = os.path.join(output_dir, filename)
            
            print(f"Generating {filename}...")
            
            html_content = create_design_html(theme_name, theme_config, effect_type)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            generated_files.append(filename)
            print(f"✓ Created {filename}")
    
    print(f"\n🎉 Successfully generated {len(generated_files)} design files!")
    print("\nGenerated files:")
    for filename in generated_files:
        print(f"  - {filename}")
    
    print(f"\n📁 All files saved in: {os.path.abspath(output_dir)}")
    print("\n🌐 To view the designs:")
    print(f"  1. Open {os.path.join(output_dir, 'index.html')} in a web browser")
    print("  2. Navigate through the different design variations")
    print("  3. Use keyboard shortcuts (Esc, Arrow keys, S for share, P for print)")

if __name__ == "__main__":
    main()
