<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame.fastevent &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.font" href="font.html" />
    <link rel="prev" title="pygame.examples" href="examples.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame.fastevent">
<span id="pygame-fastevent"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame.fastevent</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">pygame module for interacting with events and queues</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.init">pygame.fastevent.init</a></div>
</td>
<td>—</td>
<td>initialize pygame.fastevent</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.get_init">pygame.fastevent.get_init</a></div>
</td>
<td>—</td>
<td>returns True if the fastevent module is currently initialized</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.pump">pygame.fastevent.pump</a></div>
</td>
<td>—</td>
<td>internally process pygame event handlers</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.wait">pygame.fastevent.wait</a></div>
</td>
<td>—</td>
<td>wait for an event</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.poll">pygame.fastevent.poll</a></div>
</td>
<td>—</td>
<td>get an available event</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.get">pygame.fastevent.get</a></div>
</td>
<td>—</td>
<td>get all events from the queue</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="fastevent.html#pygame.fastevent.post">pygame.fastevent.post</a></div>
</td>
<td>—</td>
<td>place an event on the queue</td>
</tr>
</tbody>
</table>
<p>IMPORTANT NOTE: THIS MODULE IS DEPRECATED IN PYGAME 2.2</p>
<p>In older pygame versions before pygame 2, <a class="tooltip reference internal" href="event.html#module-pygame.event" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.event</span></code><span class="tooltip-content">pygame module for interacting with events and queues</span></a> was not well
suited for posting events from different threads. This module served as a
replacement (with less features) for multithreaded use. Now, the usage of this
module is highly discouraged in favour of use of the main <a class="tooltip reference internal" href="event.html#module-pygame.event" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.event</span></code><span class="tooltip-content">pygame module for interacting with events and queues</span></a>
module. This module will be removed in a future pygame version.</p>
<p>Below, the legacy docs of the module is provided</p>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.init">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">initialize pygame.fastevent</span></div>
<div class="line"><span class="signature">init() -&gt; None</span></div>
</div>
<p>Initialize the pygame.fastevent module.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.get_init">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">get_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.get_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">returns True if the fastevent module is currently initialized</span></div>
<div class="line"><span class="signature">get_init() -&gt; bool</span></div>
</div>
<p>Returns True if the pygame.fastevent module is currently initialized.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.pump">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">pump</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.pump" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">internally process pygame event handlers</span></div>
<div class="line"><span class="signature">pump() -&gt; None</span></div>
</div>
<p>For each frame of your game, you will need to make some sort of call to the
event queue. This ensures your program can internally interact with the rest
of the operating system.</p>
<p>This function is not necessary if your program is consistently processing
events on the queue through the other <a class="tooltip reference internal" href="#module-pygame.fastevent" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.fastevent</span></code><span class="tooltip-content">pygame module for interacting with events and queues</span></a> functions.</p>
<p>There are important things that must be dealt with internally in the event
queue. The main window may need to be repainted or respond to the system. If
you fail to make a call to the event queue for too long, the system may
decide your program has locked up.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.wait">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">wait</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.wait" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">wait for an event</span></div>
<div class="line"><span class="signature">wait() -&gt; Event</span></div>
</div>
<p>Returns the current event on the queue. If there are no messages
waiting on the queue, this will not return until one is available.
Sometimes it is important to use this wait to get events from the queue,
it will allow your application to idle when the user isn't doing anything
with it.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.poll">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">poll</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.poll" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get an available event</span></div>
<div class="line"><span class="signature">poll() -&gt; Event</span></div>
</div>
<p>Returns next event on queue. If there is no event waiting on the queue,
this will return an event with type NOEVENT.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.get">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">get</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.get" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">get all events from the queue</span></div>
<div class="line"><span class="signature">get() -&gt; list of Events</span></div>
</div>
<p>This will get all the messages and remove them from the queue.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame.fastevent.post">
<span class="sig-prename descclassname"><span class="pre">pygame.fastevent.</span></span><span class="sig-name descname"><span class="pre">post</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame.fastevent.post" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">place an event on the queue</span></div>
<div class="line"><span class="signature">post(Event) -&gt; None</span></div>
</div>
<p>This will post your own event objects onto the event queue. You can post
any event type you want, but some care must be taken. For example, if you
post a MOUSEBUTTONDOWN event to the queue, it is likely any code receiving
the event will expect the standard MOUSEBUTTONDOWN attributes to be
available, like 'pos' and 'button'.</p>
<p>Because pygame.fastevent.post() may have to wait for the queue to empty,
you can get into a dead lock if you try to append an event on to a full
queue from the thread that processes events. For that reason I do not
recommend using this function in the main thread of an SDL program.</p>
</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\fastevent.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="font.html" title="pygame.font"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="examples.html" title="pygame.examples"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.fastevent</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>