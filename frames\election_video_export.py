"""
أداة لتصدير مخطط الفيديو بتنسيقات مختلفة
للمساعدة في إنتاج فيديو الانتخابات البرلمانية
"""

from video_storyboard import create_election_video_storyboard
import json
from moviepy.editor import ImageClip, VideoClip, CompositeVideoClip, concatenate_videoclips

def export_storyboard_to_json():
    """تصدير المخطط بصيغة JSON"""
    storyboard = create_election_video_storyboard()
    with open("election_video_storyboard.json", "w", encoding="utf-8") as f:
        json.dump(storyboard, f, ensure_ascii=False, indent=4)
    print("تم تصدير المخطط بنجاح إلى election_video_storyboard.json")

def export_storyboard_to_script():
    """تصدير المخطط كنص كامل للراوي"""
    storyboard = create_election_video_storyboard()
    
    script = "# نص فيديو التوعية للانتخابات البرلمانية العراقية\n\n"
    
    for scene in storyboard:
        if "صوت راوٍ" in scene["audio"]:
            narrator_text = scene["audio"].replace("صوت راوٍ: ", "")
            script += narrator_text + "\n\n"
    
    with open("election_video_script.txt", "w", encoding="utf-8") as f:
        f.write(script)
    
    print("تم تصدير نص الراوي بنجاح إلى election_video_script.txt")
    return script

def generate_complete_script():
    """إنشاء نص كامل للفيديو مع الإرشادات الإخراجية"""
    storyboard = create_election_video_storyboard()
    
    complete_script = """
# السيناريو الكامل لفيديو التوعية الانتخابية

"""
    
    for scene in storyboard:
        complete_script += f"## المشهد {scene['scene_number']} ({scene['duration']})\n\n"
        complete_script += f"*المرئيات:* {scene['visual']}\n\n"
        complete_script += f"*الصوت:* {scene['audio']}\n\n"
        complete_script += f"*النص المرئي:* {scene['text']}\n\n"
        complete_script += f"*ملاحظات إخراجية:* {scene['effects']}\n\n"
        complete_script += "---\n\n"
    
    complete_script += """
# ملاحظات إنتاجية عامة

- مدة الفيديو الإجمالية: حوالي 2 دقيقة و40 ثانية
- المستهدفون: جميع المواطنين العراقيين المؤهلين للتصويت
- الرسالة الأساسية: أهمية المشاركة في الانتخابات وتأثيرها على مستقبل البلاد
- أسلوب الإخراج: بصري مؤثر مع رسائل واضحة ومباشرة
- الموسيقى: وطنية، ملهمة، غير صاخبة
- استخدام ألوان العلم العراقي في عناصر التصميم
- إظهار التنوع المجتمعي العراقي
- التأكد من شمول المناطق المختلفة من العراق
"""
    
    with open("election_video_complete_script.md", "w", encoding="utf-8") as f:
        f.write(complete_script)
    
    print("تم إنشاء السيناريو الكامل بنجاح وحفظه في election_video_complete_script.md")
    return complete_script

def print_sample_full_script():
    """طباعة نموذج للنص الكامل للفيديو"""
    print("\n===== نموذج لنص الفيديو الكامل =====\n")
    
    narrator_script = """
بسم الله الرحمن الرحيم

إلى أبناء شعبنا العراقي العظيم،

صوتك أمانة... صوتك مسؤولية... صوتك مستقبل.

إن المشاركة في الانتخابات البرلمانية ليست مجرد حق دستوري، بل هي واجب وطني مقدس.
عندما تدلي بصوتك، فأنت تختار من سيمثلك ويحمل همومك ويعمل على تلبية طموحاتك.

لا تدع الآخرين يقررون مستقبلك نيابة عنك.
لا تترك مقعدك فارغاً في صناعة القرار.

مهما كانت التحديات والصعوبات، فإن التغيير يبدأ بخطوة واحدة.

شارك... انتخب... غيّر.

من أجل عراق أفضل، عراق تستحقه الأجيال القادمة.
من أجل بناء مؤسسات الدولة وتعزيز الديمقراطية.

معاً نحو انتخابات نزيهة وشفافة تعبر عن إرادة الشعب العراقي الحر.

صوتك... قوة
صوتك... أمانة
صوتك... وطن
"""
    
    print(narrator_script)
    
    print("\n===== معلومات إضافية يمكن إضافتها في نهاية الفيديو =====\n")
    
    additional_info = """
- تاريخ الانتخابات: [إدخال التاريخ الفعلي]
- مراكز الاقتراع: من الساعة 7 صباحاً وحتى 6 مساءً
- الوثائق المطلوبة: بطاقة الناخب وإحدى وثائق إثبات الهوية
- للاستعلام: [إدخال أرقام هواتف مفوضية الانتخابات]
- الموقع الإلكتروني: [إدخال الموقع الرسمي]
"""
    
    print(additional_info)

if __name__ == "__main__":
    print("===== أدوات تصدير مخطط فيديو الانتخابات البرلمانية =====\n")
    print("1. عرض نموذج للنص الكامل")
    
    print_sample_full_script()
    
    # تعليق على دوال التصدير
    # export_storyboard_to_json()
    # export_storyboard_to_script()
    # generate_complete_script() 