#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Automatic Imam al-Jawad Video Creator
Creates the condolence video with default settings (no user input required).
"""

import sys
import os
from datetime import datetime

def create_video_automatically():
    """Create video with default settings."""
    print("<PERSON> al-J<PERSON> (AS) Condolence Video Creator - Automatic Mode")
    print("=" * 60)
    
    try:
        from imam_jawad_video_creator import ImamJawadVideoCreator
        
        # Default settings
        aspect_ratio = "1:1"  # Square format for social media
        
        print(f"Creating {aspect_ratio} condolence video...")
        print("Using default settings:")
        print(f"  - Aspect ratio: {aspect_ratio}")
        print(f"  - Output format: GIF")
        print(f"  - Resolution: 1080x1080")
        
        # Create video
        creator = ImamJawadVideoCreator(aspect_ratio=aspect_ratio)
        print(f"✓ Video creator initialized: {creator.resolution[0]}x{creator.resolution[1]}")
        
        # Create frames
        print("\nCreating video frames...")
        frames = creator.create_video_frames(fps=15, include_transitions=True)
        print(f"✓ Created {len(frames)} total frames")
        
        # Save as GIF
        print("\nSaving video...")
        output_file = creator.save_as_gif(fps=8, optimize=True)
        
        if output_file:
            print("\n" + "=" * 60)
            print("✅ VIDEO CREATION COMPLETE!")
            print("=" * 60)
            print(f"✓ Output file: {output_file}")
            print(f"✓ Total frames: {len(frames)}")
            print(f"✓ Duration: {len(frames)/8:.1f} seconds")
            print(f"✓ File size: {os.path.getsize(output_file)/(1024*1024):.1f} MB")
            
            # Also create a 16:9 version
            print("\nCreating widescreen version...")
            creator_wide = ImamJawadVideoCreator(aspect_ratio="16:9")
            frames_wide = creator_wide.create_video_frames(fps=15, include_transitions=True)
            output_file_wide = creator_wide.save_as_gif(fps=8, optimize=True)
            
            if output_file_wide:
                print(f"✓ Widescreen version: {output_file_wide}")
                print(f"✓ File size: {os.path.getsize(output_file_wide)/(1024*1024):.1f} MB")
            
            # Try to open the first file
            try:
                if sys.platform == "win32":
                    os.startfile(output_file)
                    print("✓ Video opened for preview")
                elif sys.platform == "darwin":
                    os.system(f'open "{output_file}"')
                    print("✓ Video opened for preview")
                else:
                    os.system(f'xdg-open "{output_file}"')
                    print("✓ Video opened for preview")
            except Exception:
                print("Note: Could not open video automatically")
            
            print("\n📱 SOCIAL MEDIA READY:")
            print("  - Square version (1:1) for Instagram/Facebook posts")
            print("  - Widescreen version (16:9) for YouTube/general sharing")
            
            print("\n🎬 VIDEO FEATURES:")
            print("  ✓ Authentic Shia Islamic content")
            print("  ✓ Proper Arabic RTL text rendering")
            print("  ✓ Professional mourning design")
            print("  ✓ Smooth animations and transitions")
            print("  ✓ AliToucan branding included")
            print("  ✓ High resolution for quality sharing")
            
            return [output_file, output_file_wide] if output_file_wide else [output_file]
        
        else:
            print("❌ Failed to create video")
            return None
            
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_sample_frames():
    """Create sample frames to show the video content."""
    print("\nCreating sample frames for preview...")
    
    try:
        from imam_jawad_video_creator import ImamJawadVideoCreator
        
        creator = ImamJawadVideoCreator(aspect_ratio="1:1")
        
        # Create sample frames from different scenes
        sample_scenes = ['opening', 'imam_name', 'condolence_phrase', 'closing']
        sample_dir = os.path.join(creator.output_dir, "samples")
        
        if not os.path.exists(sample_dir):
            os.makedirs(sample_dir)
        
        for i, scene_name in enumerate(sample_scenes):
            frame = creator.create_scene_frame(scene_name, 0, 1)
            sample_file = os.path.join(sample_dir, f"sample_{i+1}_{scene_name}.png")
            frame.save(sample_file)
            print(f"✓ Sample {i+1}: {sample_file}")
        
        print(f"✓ Sample frames saved to: {sample_dir}")
        return sample_dir
        
    except Exception as e:
        print(f"❌ Error creating samples: {e}")
        return None

def main():
    """Main function."""
    print("Starting automatic video creation...")
    
    # Create sample frames first
    sample_dir = create_sample_frames()
    
    # Create full videos
    video_files = create_video_automatically()
    
    if video_files:
        print("\n" + "=" * 60)
        print("🎉 SUCCESS! All files created:")
        print("=" * 60)
        
        if sample_dir:
            print(f"📸 Sample frames: {sample_dir}")
        
        for video_file in video_files:
            print(f"🎬 Video: {video_file}")
        
        print("\n💡 NEXT STEPS:")
        print("  1. Review the generated videos")
        print("  2. Share on social media platforms")
        print("  3. For MP4 conversion, see IMAM_JAWAD_VIDEO_README.md")
        
        return True
    else:
        print("\n❌ Video creation failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🤲 May Allah bless the memory of Imam al-Jawad (AS)")
        print("   وَصَلَّى اللهُ عَلَى مُحَمَّدٍ وَآلِهِ الطَّاهِرِينَ")
    else:
        print("\nPlease run the test script first: python test_imam_jawad_video.py")
