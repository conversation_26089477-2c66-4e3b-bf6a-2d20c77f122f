"""
مولد فيديو التوعية الانتخابية
يقوم بإنشاء نسخة مبسطة من الفيديو بناءً على مخطط الفيديو المُعد وحفظه على سطح المكتب
"""

import os
import sys
import moviepy.editor as mp  # type: ignore
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import random
from video_storyboard import create_election_video_storyboard

# المسار للحفظ على سطح المكتب
DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")
# تغيير سطر تعريف ملف الإخراج
OUTPUT_FILE = os.path.join(DESKTOP_PATH, f"فيديو_التوعية_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4")

# الألوان المستخدمة (ألوان العلم العراقي)
COLORS = {
    "red": (206, 17, 38),
    "black": (0, 0, 0),
    "white": (255, 255, 255),
    "green": (0, 131, 66),
    "gold": (255, 215, 0)
}

def create_text_clip(text, font_size=60, color="white", bg_color=None, duration=5, 
                   size=(1280, 720), position=('center', 'center'), font_type="Arial"):
    """إنشاء مقطع نصي بتنسيق معين"""
    
    # إنشاء صورة فارغة
    img = Image.new('RGB', size, bg_color or (0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # تحديد الخط (استخدام خط افتراضي إذا لم يتوفر الخط المطلوب)
    try:
        font = ImageFont.truetype("Arial.ttf", font_size)
    except IOError:
        font = ImageFont.load_default()
    
    # حساب موضع النص
    try:
        text_width, text_height = draw.textsize(text, font=font)
    except AttributeError:
        # استخدام طريقة بديلة إذا كانت طريقة textsize غير متوفرة
        text_width, text_height = draw.textlength(text, font=font), font_size * 1.5
    
    # تحديد موقع النص في الصورة
    if position[0] == 'center':
        x = (size[0] - text_width) // 2
    else:
        x = position[0]
        
    if position[1] == 'center':
        y = (size[1] - text_height) // 2
    else:
        y = position[1]
    
    # رسم النص
    if isinstance(color, str):
        color = COLORS.get(color, (255, 255, 255))
    
    draw.text((x, y), text, font=font, fill=color)
    
    # تحويل الصورة إلى مصفوفة numpy
    img_array = np.array(img)
    
    # إنشاء مقطع فيديو من الصورة
    return mp.ImageClip(img_array).set_duration(duration)

def create_flag_background(size=(1280, 720), duration=5):
    """إنشاء خلفية تشبه العلم العراقي المتموج"""
    
    def make_frame(t):
        img = Image.new('RGB', size, COLORS["white"])
        draw = ImageDraw.Draw(img)
        
        # رسم الأجزاء الثلاثة للعلم
        height = size[1] // 3
        
        # الجزء العلوي (أحمر)
        draw.rectangle([0, 0, size[0], height], fill=COLORS["red"])
        
        # الجزء الوسط (أبيض مع "الله أكبر")
        # الجزء السفلي (أسود)
        draw.rectangle([0, height*2, size[0], size[1]], fill=COLORS["black"])
        
        # إضافة تأثير التموج البسيط للعلم
        wave_effect = int(10 * np.sin(t * 2 + np.linspace(0, 10, size[0])))
        
        # تحويل الصورة إلى مصفوفة numpy وإضافة التموج
        img_array = np.array(img)
        for i in range(size[0]):
            shift = wave_effect[i % len(wave_effect)]
            # التأكد من أن الإزاحة لا تخرج عن حدود الصورة
            if shift > 0:
                img_array[shift:, i] = img_array[:-shift, i]
            elif shift < 0:
                img_array[:shift, i] = img_array[-shift:, i]
                
        return img_array
    
    return mp.VideoClip(make_frame, duration=duration)

def create_simple_animation(text, duration=5, font_size=60, bg_color=(0, 0, 0), 
                           color="white", animation_type="fade"):
    """إنشاء رسوم متحركة بسيطة للنص"""
    
    size = (1280, 720)
    
    if animation_type == "fade":
        clip = create_text_clip(text, font_size, color, bg_color, duration, size)
        return clip.fadein(1).fadeout(1)
    
    elif animation_type == "zoom":
        # تأثير التكبير
        clip = create_text_clip(text, font_size, color, bg_color, duration, size)
        return clip.resize(lambda t: 1 + 0.3 * np.sin(t * np.pi))
    
    elif animation_type == "slide":
        # تأثير الانزلاق
        clip = create_text_clip(text, font_size, color, bg_color, duration, size)
        return clip.set_position(lambda t: ('center', 100 + t * 50))
    
    elif animation_type == "pulse":
        # تأثير النبض
        def make_frame(t):
            pulse = 1 + 0.2 * np.sin(t * 5)
            temp_clip = create_text_clip(text, int(font_size * pulse), color, bg_color, 1, size)
            return temp_clip.get_frame(0)
        
        return mp.VideoClip(make_frame, duration=duration)
    
    else:
        # افتراضي بدون رسوم متحركة
        return create_text_clip(text, font_size, color, bg_color, duration, size)

def generate_election_video():
    """إنشاء فيديو توعية انتخابية بسيط بناءً على المخطط"""
    
    print("بدء إنشاء فيديو التوعية الانتخابية...")
    
    # الحصول على مخطط الفيديو
    storyboard = create_election_video_storyboard()
    clips = []
    
    # إنشاء مقاطع الفيديو لكل مشهد
    for scene in storyboard:
        print(f"إنشاء المشهد {scene['scene_number']}: {scene['text']}")
        
        # تحديد مدة المشهد بناءً على المخطط
        start_time, end_time = scene['duration'].split(" - ")
        start_minutes, start_seconds = map(int, start_time.split(":"))
        end_minutes, end_seconds = map(int, end_time.split(":"))
        
        duration = (end_minutes * 60 + end_seconds) - (start_minutes * 60 + start_seconds)
        
        # اختيار نوع الرسوم المتحركة بناءً على رقم المشهد
        animation_types = ["fade", "zoom", "slide", "pulse"]
        animation_type = animation_types[scene['scene_number'] % len(animation_types)]
        
        # تحديد لون الخلفية والنص
        if scene['scene_number'] % 3 == 0:
            bg_color = COLORS["black"]
            text_color = COLORS["white"]
        elif scene['scene_number'] % 3 == 1:
            bg_color = COLORS["white"]
            text_color = COLORS["green"]
        else:
            bg_color = COLORS["red"]
            text_color = COLORS["white"]
        
        # إنشاء المقطع المناسب
        if scene['scene_number'] == 1:
            # المشهد الأول: خلفية العلم العراقي مع البسملة
            bg = create_flag_background(duration=duration)
            text = create_text_clip(scene['text'], 80, "white", None, duration)
            clip = mp.CompositeVideoClip([bg, text])
        else:
            # المشاهد الأخرى: نص متحرك على خلفية ملونة
            clip = create_simple_animation(
                scene['text'], 
                duration, 
                70 - scene['scene_number'] * 2,  # تقليل حجم الخط تدريجياً
                bg_color, 
                text_color,
                animation_type
            )
            
            # إضافة نص المشهد في الأسفل
            scene_text = f"المشهد {scene['scene_number']}"
            scene_indicator = create_text_clip(
                scene_text, 
                20, 
                "white", 
                None, 
                duration, 
                position=('center', 650)
            )
            clip = mp.CompositeVideoClip([clip, scene_indicator])
        
        # ضبط مدة المقطع
        clip = clip.set_duration(duration)
        clips.append(clip)
    
    # إضافة المشهد الختامي
    final_text = """
    صوتك... قوة
    صوتك... أمانة
    صوتك... وطن

    شارك في الانتخابات البرلمانية
    """
    final_clip = create_simple_animation(final_text, 5, 70, COLORS["black"], COLORS["gold"], "fade")
    clips.append(final_clip)
    
    # دمج جميع المقاطع
    # إضافة رسائل تحذيرية لمن يتخلف عن الانتخابات
    warning_text = TextClip(
        "⚠️ التخلف عن الانتخابات خيانة للوطن والأمة\nستكون عواقبها وخيمة على مستقبل الأجيال القادمة",
        fontsize=32,
        color='red',
        font='Arial',
        method='caption',
        size=(width*0.9, None)
    ).set_duration(5)
    
    # إضافة رسائل تحفيزية قوية
    motivational_messages = [
        "صوتك سلاحك الوحيد لتغيير الواقع المرير",
        "الانتخابات فرصتك الأخيرة لإنقاذ العراق من الهاوية",
        "لا تكن شريكًا في تدمير بلدك بالغياب عن صناديق الاقتراع"
    ]  # تأكد من أن جميع الأسطر لها نفس المسافة البادئة
    
    # ... existing code ...
    
    # تعديل المقطع النهائي لإضافة الرسائل الجديدة
    final_video = mp.concatenate_videoclips([intro, warning_text] + motivational_clips + [outro])
    
    # إضافة الموسيقى (تعليق هذا الجزء لأنه يتطلب ملف موسيقى)
    # audio_path = "path_to_music.mp3"
    # if os.path.exists(audio_path):
    #     audio = mp.AudioFileClip(audio_path)
    #     audio = audio.subclip(0, final_video.duration)
    #     audio = audio.volumex(0.7)  # خفض مستوى الصوت لتجنب التشويش
    #     final_video = final_video.set_audio(audio)
    
    # حفظ الفيديو
    print(f"جاري حفظ الفيديو إلى: {OUTPUT_FILE}")
    final_video.write_videofile(
        OUTPUT_FILE, 
        fps=24, 
        codec='libx264', 
        audio_codec='aac', 
        temp_audiofile='temp-audio.m4a', 
        remove_temp=True
    )
    
    print(f"تم إنشاء الفيديو بنجاح وحفظه على سطح المكتب: {OUTPUT_FILE}")
    
    return OUTPUT_FILE

if __name__ == "__main__":
    try:
        output_path = generate_election_video()
        print(f"تم إنشاء الفيديو بنجاح! المسار: {output_path}")
        
        # فتح الفيديو بعد الإنشاء (اختياري)
        if sys.platform == "win32":
            os.system(f'start "{output_path}"')
        elif sys.platform == "darwin":  # macOS
            os.system(f'open "{output_path}"')
        else:  # Linux
            os.system(f'xdg-open "{output_path}"')
            
    except Exception as e:
        print(f"حدث خطأ أثناء إنشاء الفيديو: {str(e)}")
        print("تأكد من تثبيت المكتبات اللازمة باستخدام الأمر:")
        print("pip install moviepy pillow numpy")