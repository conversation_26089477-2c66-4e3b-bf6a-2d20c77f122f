#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for <PERSON> condolence video creation
Verifies all components work correctly before full video generation.
"""

import sys
import os
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print("✓ PIL (Pillow) imported successfully")
    except ImportError as e:
        print(f"❌ PIL import failed: {e}")
        return False
    
    try:
        from enhanced_arabic_renderer import ArabicTextRenderer
        print("✓ Enhanced Arabic Renderer imported successfully")
    except ImportError as e:
        print(f"❌ Enhanced Arabic Renderer import failed: {e}")
        return False
    
    try:
        from imam_jawad_video_content import ImamJawadContent
        print("✓ Imam Jawad Content imported successfully")
    except ImportError as e:
        print(f"❌ Imam Jawad Content import failed: {e}")
        return False
    
    try:
        from imam_jawad_video_creator import ImamJawadVideoCreator
        print("✓ Imam Jawad Video Creator imported successfully")
    except ImportError as e:
        print(f"❌ Imam Jawad Video Creator import failed: {e}")
        return False
    
    return True

def test_arabic_rendering():
    """Test Arabic text rendering capabilities."""
    print("\nTesting Arabic text rendering...")
    
    try:
        from enhanced_arabic_renderer import ArabicTextRenderer
        renderer = ArabicTextRenderer()
        
        # Test Arabic text processing
        test_text = "بسم الله الرحمن الرحيم"
        processed = renderer.process_arabic_text(test_text)
        print(f"✓ Arabic text processed: {test_text} -> {processed}")
        
        # Test font loading
        font, font_name = renderer.get_font(24)
        print(f"✓ Font loaded: {font_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Arabic rendering test failed: {e}")
        return False

def test_content_loading():
    """Test content loading and structure."""
    print("\nTesting content loading...")
    
    try:
        from imam_jawad_video_content import ImamJawadContent
        content = ImamJawadContent()
        
        scenes = content.get_all_scenes()
        print(f"✓ Loaded {len(scenes)} scenes")
        
        total_duration = content.get_total_duration()
        print(f"✓ Total video duration: {total_duration:.1f} seconds")
        
        # Test a specific scene
        opening_scene = content.get_scene_content('opening')
        if opening_scene:
            print(f"✓ Opening scene: {opening_scene.get('arabic', 'N/A')}")
        
        # Test styles
        styles = content.get_scene_styles()
        print(f"✓ Available styles: {len(styles)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Content loading test failed: {e}")
        return False

def test_single_frame_creation():
    """Test creating a single video frame."""
    print("\nTesting single frame creation...")
    
    try:
        from imam_jawad_video_creator import ImamJawadVideoCreator
        
        # Create video creator
        creator = ImamJawadVideoCreator(aspect_ratio='1:1')
        print(f"✓ Video creator initialized: {creator.resolution}")
        
        # Create a single frame
        frame = creator.create_scene_frame('opening', 0, 1)
        print(f"✓ Frame created: {frame.size}")
        
        # Save test frame
        test_dir = "test_output"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        
        test_file = os.path.join(test_dir, "test_frame.png")
        frame.save(test_file)
        print(f"✓ Test frame saved: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Single frame creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_creation_quick():
    """Test quick video creation with minimal frames."""
    print("\nTesting quick video creation...")
    
    try:
        from imam_jawad_video_creator import ImamJawadVideoCreator
        
        # Create video creator
        creator = ImamJawadVideoCreator(aspect_ratio='1:1')
        
        # Create frames for just the first 3 scenes (quick test)
        scenes = creator.content.get_all_scenes()[:3]
        test_frames = []
        
        for scene_name in scenes:
            # Create just 2 frames per scene for quick test
            scene_frames = creator.create_scene_frames(scene_name, fps=2)
            test_frames.extend(scene_frames[:2])  # Take only first 2 frames
        
        creator.frames = test_frames
        print(f"✓ Created {len(test_frames)} test frames")
        
        # Save as GIF
        timestamp = datetime.now().strftime('%H%M%S')
        test_gif = creator.save_as_gif(f"test_video_{timestamp}.gif", fps=2)
        
        if test_gif:
            print(f"✓ Test video saved: {test_gif}")
            return True
        else:
            print("❌ Failed to save test video")
            return False
        
    except Exception as e:
        print(f"❌ Quick video creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests and provide summary."""
    print("Imam al-Jawad Video Creator - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Arabic Rendering Test", test_arabic_rendering),
        ("Content Loading Test", test_content_loading),
        ("Single Frame Test", test_single_frame_creation),
        ("Quick Video Test", test_video_creation_quick)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Ready to create full video.")
        print("\nTo create the full video, run:")
        print("python imam_jawad_video_creator.py")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
    
    return passed == len(results)

if __name__ == "__main__":
    run_all_tests()
