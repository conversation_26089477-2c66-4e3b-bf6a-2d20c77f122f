#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MP4 Video Creator for Professional Islamic Videos
High-quality MP4 video generation with audio integration using MoviePy and FFmpeg.
"""

import os
import sys
import subprocess
from datetime import datetime
from PIL import Image

# Try to import video processing libraries
try:
    from moviepy.editor import ImageSequenceClip, AudioFileClip, CompositeVideoClip
    from moviepy.video.fx import resize, fadein, fadeout
    MOVIEPY_SUPPORT = True
    print("✓ MoviePy video processing library loaded successfully")
except ImportError as e:
    MOVIEPY_SUPPORT = False
    print(f"⚠️  MoviePy not available: {e}")

class ProfessionalMP4Creator:
    """Professional MP4 video creator with audio integration."""

    def __init__(self, output_dir="professional_video_output"):
        self.output_dir = output_dir
        self._create_output_directory()
        self.ffmpeg_available = self._check_ffmpeg()

    def _create_output_directory(self):
        """Create output directory for videos."""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"✓ Created output directory: {self.output_dir}")

    def _check_ffmpeg(self):
        """Check if FFmpeg is available."""
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
            print("✓ FFmpeg is available")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  FFmpeg not found - some features may be limited")
            return False

    def create_mp4_from_frames(self, frames, output_filename, fps=30, audio_path=None, 
                              duration_per_frame=None, fade_duration=0.5):
        """Create MP4 video from PIL Image frames with optional audio."""
        
        if not MOVIEPY_SUPPORT:
            print("❌ MoviePy not available - cannot create MP4 videos")
            return None

        try:
            # Save frames as temporary image files
            temp_dir = os.path.join(self.output_dir, "temp_frames")
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            frame_paths = []
            for i, frame in enumerate(frames):
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                
                # Ensure frame is in RGB mode for video
                if frame.mode != 'RGB':
                    frame = frame.convert('RGB')
                
                frame.save(frame_path, 'PNG')
                frame_paths.append(frame_path)

            print(f"✓ Saved {len(frame_paths)} frames to temporary directory")

            # Calculate frame duration
            if duration_per_frame:
                frame_duration = duration_per_frame
            else:
                frame_duration = 1.0 / fps

            # Create video clip from image sequence
            video_clip = ImageSequenceClip(frame_paths, fps=fps)
            
            # Apply fade effects
            if fade_duration > 0:
                video_clip = fadein(video_clip, fade_duration)
                video_clip = fadeout(video_clip, fade_duration)

            # Add audio if provided
            final_clip = video_clip
            if audio_path and os.path.exists(audio_path):
                try:
                    audio_clip = AudioFileClip(audio_path)
                    
                    # Adjust audio duration to match video
                    if audio_clip.duration > video_clip.duration:
                        audio_clip = audio_clip.subclip(0, video_clip.duration)
                    elif audio_clip.duration < video_clip.duration:
                        # Loop audio if it's shorter than video
                        loops_needed = int(video_clip.duration / audio_clip.duration) + 1
                        audio_clip = audio_clip.loop(loops_needed).subclip(0, video_clip.duration)
                    
                    final_clip = video_clip.set_audio(audio_clip)
                    print("✓ Audio integrated successfully")
                    
                except Exception as e:
                    print(f"⚠️  Audio integration failed: {e}")
                    final_clip = video_clip

            # Generate output path
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_path = os.path.join(self.output_dir, f"{output_filename}_{timestamp}.mp4")

            # Write video file
            print("🎬 Creating MP4 video...")
            final_clip.write_videofile(
                output_path,
                fps=fps,
                codec='libx264',
                audio_codec='aac' if audio_path else None,
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up temporary frames
            self._cleanup_temp_frames(temp_dir)

            # Get file info
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            print(f"✓ MP4 video created: {os.path.basename(output_path)} ({file_size:.1f} MB)")

            return output_path

        except Exception as e:
            print(f"❌ Error creating MP4 video: {e}")
            return None

    def create_social_media_variants(self, frames, base_filename, audio_path=None):
        """Create multiple video variants optimized for different social media platforms."""
        
        if not frames:
            print("❌ No frames provided")
            return []

        variants = []
        
        # Define platform specifications
        platform_specs = {
            'instagram_square': {
                'resolution': (1080, 1080),
                'fps': 30,
                'max_duration': 60,
                'description': 'Instagram Square Post'
            },
            'instagram_story': {
                'resolution': (1080, 1920),
                'fps': 30,
                'max_duration': 15,
                'description': 'Instagram Story'
            },
            'youtube_standard': {
                'resolution': (1920, 1080),
                'fps': 30,
                'max_duration': 300,
                'description': 'YouTube Standard'
            },
            'facebook_feed': {
                'resolution': (1080, 1350),
                'fps': 30,
                'max_duration': 120,
                'description': 'Facebook Feed'
            },
            'tiktok_vertical': {
                'resolution': (1080, 1920),
                'fps': 30,
                'max_duration': 60,
                'description': 'TikTok Vertical'
            }
        }

        for platform, specs in platform_specs.items():
            try:
                print(f"📱 Creating {specs['description']} variant...")
                
                # Resize frames if needed
                target_resolution = specs['resolution']
                processed_frames = []
                
                for frame in frames:
                    if frame.size != target_resolution:
                        # Resize maintaining aspect ratio
                        resized_frame = self._resize_frame_with_padding(frame, target_resolution)
                        processed_frames.append(resized_frame)
                    else:
                        processed_frames.append(frame)

                # Limit frames based on platform duration
                max_frames = int(specs['max_duration'] * specs['fps'])
                if len(processed_frames) > max_frames:
                    processed_frames = processed_frames[:max_frames]

                # Create video
                output_filename = f"{base_filename}_{platform}"
                video_path = self.create_mp4_from_frames(
                    processed_frames,
                    output_filename,
                    fps=specs['fps'],
                    audio_path=audio_path
                )

                if video_path:
                    variants.append({
                        'platform': platform,
                        'description': specs['description'],
                        'path': video_path,
                        'resolution': target_resolution,
                        'duration': len(processed_frames) / specs['fps']
                    })

            except Exception as e:
                print(f"❌ Error creating {platform} variant: {e}")

        return variants

    def _resize_frame_with_padding(self, frame, target_size):
        """Resize frame maintaining aspect ratio with padding."""
        target_width, target_height = target_size
        
        # Calculate scaling factor
        scale_w = target_width / frame.width
        scale_h = target_height / frame.height
        scale = min(scale_w, scale_h)
        
        # Calculate new size
        new_width = int(frame.width * scale)
        new_height = int(frame.height * scale)
        
        # Resize frame
        resized_frame = frame.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Create padded image
        padded_frame = Image.new('RGB', target_size, (0, 0, 0))
        
        # Center the resized frame
        x_offset = (target_width - new_width) // 2
        y_offset = (target_height - new_height) // 2
        
        padded_frame.paste(resized_frame, (x_offset, y_offset))
        
        return padded_frame

    def _cleanup_temp_frames(self, temp_dir):
        """Clean up temporary frame files."""
        try:
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                print("✓ Temporary frames cleaned up")
        except Exception as e:
            print(f"⚠️  Could not clean up temporary frames: {e}")

    def optimize_for_web(self, video_path, quality='high'):
        """Optimize video for web delivery using FFmpeg."""
        if not self.ffmpeg_available or not os.path.exists(video_path):
            return video_path

        try:
            base_name = os.path.splitext(video_path)[0]
            optimized_path = f"{base_name}_optimized.mp4"

            # Quality settings
            quality_settings = {
                'high': ['-crf', '18', '-preset', 'slow'],
                'medium': ['-crf', '23', '-preset', 'medium'],
                'low': ['-crf', '28', '-preset', 'fast']
            }

            cmd = [
                'ffmpeg', '-i', video_path,
                '-c:v', 'libx264',
                '-c:a', 'aac',
                '-movflags', '+faststart',  # Optimize for web streaming
                *quality_settings.get(quality, quality_settings['medium']),
                '-y',  # Overwrite output file
                optimized_path
            ]

            print("🔧 Optimizing video for web...")
            subprocess.run(cmd, capture_output=True, check=True)

            # Check if optimization was successful
            if os.path.exists(optimized_path):
                original_size = os.path.getsize(video_path) / (1024 * 1024)
                optimized_size = os.path.getsize(optimized_path) / (1024 * 1024)
                
                print(f"✓ Video optimized: {original_size:.1f}MB → {optimized_size:.1f}MB")
                return optimized_path
            else:
                print("⚠️  Optimization failed, returning original")
                return video_path

        except subprocess.CalledProcessError as e:
            print(f"❌ FFmpeg optimization failed: {e}")
            return video_path
        except Exception as e:
            print(f"❌ Optimization error: {e}")
            return video_path

def test_mp4_creator():
    """Test the MP4 video creator."""
    print("Testing MP4 Video Creator...")
    print("=" * 40)

    creator = ProfessionalMP4Creator()
    
    # Create test frames
    test_frames = []
    for i in range(10):
        frame = Image.new('RGB', (800, 600), (i * 25, 100, 200))
        test_frames.append(frame)
    
    print(f"✓ Created {len(test_frames)} test frames")
    
    if MOVIEPY_SUPPORT:
        # Test MP4 creation
        video_path = creator.create_mp4_from_frames(
            test_frames, 
            "test_video", 
            fps=10
        )
        
        if video_path:
            print(f"✓ Test video created: {video_path}")
        else:
            print("❌ Failed to create test video")
    else:
        print("⚠️  MoviePy not available - install moviepy for full functionality")

    print("✓ MP4 creator test completed")

if __name__ == "__main__":
    test_mp4_creator()
