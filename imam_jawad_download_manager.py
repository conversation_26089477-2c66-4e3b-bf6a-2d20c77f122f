#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import shutil
import zipfile
from datetime import datetime

def create_download_package():
    """
    Create a downloadable package of all Imam al-Jawad designs.
    """
    designs_dir = "imam_jawad_designs"

    if not os.path.exists(designs_dir):
        print("Error: No designs found. Please run generate_imam_jawad_designs.py first.")
        return None

    # Create timestamp for unique package name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"imam_jawad_condolence_designs_{timestamp}"

    # Create package directory
    package_dir = f"{package_name}_package"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)

    # Copy all design files
    design_files = []
    for filename in os.listdir(designs_dir):
        if filename.endswith(('.png', '.jpg', '.jpeg')):
            src_path = os.path.join(designs_dir, filename)
            dst_path = os.path.join(package_dir, filename)
            shutil.copy2(src_path, dst_path)
            design_files.append(filename)

    # Create README file
    readme_content = f"""# Imam al-Jawad (AS) Condolence Designs
Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## About
These designs commemorate the martyrdom of Imam Muhammad al-Jawad (AS) on the 29th of Dhul Qi'dah.

## Design Variations Included:
1. **Classic Black & Gold** - Traditional mourning colors with elegant gold text
2. **Elegant Dark Gray & Silver** - Sophisticated modern look
3. **Traditional Black & White** - Classic high-contrast design
4. **Royal Navy & Gold** - Distinguished deep blue with gold accents

## Technical Specifications:
- **Aspect Ratio**: 1:1 (Square format)
- **Resolution**: 1080x1080 pixels
- **Formats**: PNG (transparent background support) and JPG (smaller file size)
- **Quality**: High resolution suitable for social media sharing

## Arabic Content:
All designs include authentic Arabic text based on Shia Islamic sources:
- Bismillah (In the name of Allah)
- Condolence message for Imam al-Jawad (AS)
- Date of martyrdom (29th Dhul Qi'dah)
- Traditional condolence phrase
- Salawat (Prayers upon Prophet and his family)

## Usage:
These designs are suitable for:
- Social media posts (Instagram, Facebook, Twitter)
- WhatsApp status and sharing
- Print materials
- Digital displays
- Community announcements

## Branding:
All designs include "AliToucan" watermark as requested.

## Cultural Notes:
The designs follow Shia Islamic mourning traditions and use respectful terminology appropriate for commemorating the martyrdom of the Imam.

---
Created by: AliToucan Design System
Generated using: Python PIL (Pillow) library
"""

    readme_path = os.path.join(package_dir, "README.txt")
    with open(readme_path, 'w', encoding='utf-8', errors='replace') as f:
        f.write(readme_content)

    # Create ZIP archive
    zip_path = f"{package_name}.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arcname)

    # Calculate package info
    package_size = os.path.getsize(zip_path) / 1024  # Size in KB
    file_count = len(design_files) + 1  # +1 for README

    return {
        'zip_path': zip_path,
        'package_dir': package_dir,
        'file_count': file_count,
        'package_size': package_size,
        'design_files': design_files
    }

def list_available_designs():
    """
    List all available design files with their details.
    """
    designs_dir = "imam_jawad_designs"

    if not os.path.exists(designs_dir):
        print("No designs found. Please run generate_imam_jawad_designs.py first.")
        return []

    design_files = []

    print("Available Imam al-Jawad Condolence Designs:")
    print("=" * 50)

    for filename in sorted(os.listdir(designs_dir)):
        if filename.endswith(('.png', '.jpg', '.jpeg')):
            file_path = os.path.join(designs_dir, filename)
            file_size = os.path.getsize(file_path) / 1024  # KB

            # Determine design type
            if 'classic' in filename:
                design_type = "Classic Black & Gold"
            elif 'elegant' in filename:
                design_type = "Elegant Dark Gray & Silver"
            elif 'traditional' in filename:
                design_type = "Traditional Black & White"
            elif 'royal' in filename:
                design_type = "Royal Navy & Gold"
            else:
                design_type = "Standard Design"

            format_type = filename.split('.')[-1].upper()

            print(f"📄 {filename}")
            print(f"   Type: {design_type}")
            print(f"   Format: {format_type}")
            print(f"   Size: {file_size:.1f} KB")
            print(f"   Path: {os.path.abspath(file_path)}")
            print()

            design_files.append({
                'filename': filename,
                'path': file_path,
                'size': file_size,
                'type': design_type,
                'format': format_type
            })

    return design_files

def open_designs_folder():
    """
    Open the designs folder in file explorer.
    """
    designs_dir = "imam_jawad_designs"

    if not os.path.exists(designs_dir):
        print("No designs folder found.")
        return False

    try:
        # Windows
        os.startfile(os.path.abspath(designs_dir))
        print(f"Opened folder: {os.path.abspath(designs_dir)}")
        return True
    except AttributeError:
        try:
            # macOS
            os.system(f'open "{os.path.abspath(designs_dir)}"')
            return True
        except:
            try:
                # Linux
                os.system(f'xdg-open "{os.path.abspath(designs_dir)}"')
                return True
            except:
                print(f"Please manually open: {os.path.abspath(designs_dir)}")
                return False

def main():
    """
    Main download manager interface.
    """
    print("Imam al-Jawad (AS) Condolence Designs - Download Manager")
    print("=" * 60)

    while True:
        print("\nOptions:")
        print("1. List available designs")
        print("2. Create download package (ZIP)")
        print("3. Open designs folder")
        print("4. Exit")

        choice = input("\nSelect option (1-4): ").strip()

        if choice == "1":
            designs = list_available_designs()
            if designs:
                print(f"\nTotal designs: {len(designs)}")
                total_size = sum(d['size'] for d in designs)
                print(f"Total size: {total_size:.1f} KB")

        elif choice == "2":
            print("\nCreating download package...")
            package_info = create_download_package()

            if package_info:
                print("✓ Download package created successfully!")
                print(f"  📦 Package: {package_info['zip_path']}")
                print(f"  📁 Files: {package_info['file_count']}")
                print(f"  💾 Size: {package_info['package_size']:.1f} KB")
                print(f"  📍 Location: {os.path.abspath(package_info['zip_path'])}")

                # Clean up temporary directory
                if os.path.exists(package_info['package_dir']):
                    shutil.rmtree(package_info['package_dir'])
                    print("  🧹 Temporary files cleaned up")
            else:
                print("❌ Failed to create package")

        elif choice == "3":
            print("\nOpening designs folder...")
            open_designs_folder()

        elif choice == "4":
            print("\nThank you for using the Imam al-Jawad Design Manager!")
            print("May Allah bless the memory of Imam al-Jawad (AS)")
            break

        else:
            print("Invalid option. Please select 1-4.")

if __name__ == "__main__":
    main()
