#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Imam <PERSON> (AS) Condolence Video Creator
Creates a professional condolence video commemorating the martyrdom anniversary
of Imam <PERSON> (peace be upon him) following Shia Islamic traditions.
"""

import os
import sys
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont, ImageSequence
import math

# Import our custom modules
from enhanced_arabic_renderer import ArabicTextRenderer
from imam_jawad_video_content import ImamJawad<PERSON>ontent

class ImamJawadVideoCreator:
    """Professional video creator for <PERSON> condolence content."""

    def __init__(self, aspect_ratio='1:1', resolution=None):
        """Initialize the video creator with specified aspect ratio."""
        self.aspect_ratio = aspect_ratio
        self.content = ImamJawadContent()
        self.arabic_renderer = ArabicTextRenderer()

        # Set resolution based on aspect ratio
        if resolution:
            self.resolution = resolution
        else:
            resolutions = self.content.get_video_metadata()['resolutions']
            self.resolution = resolutions.get(aspect_ratio, (1080, 1080))

        self.width, self.height = self.resolution
        self.frames = []

        # Create output directory
        self.output_dir = "imam_jawad_video_output"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"✓ Created output directory: {self.output_dir}")

    def create_decorative_frame(self, img, style_colors):
        """Add decorative Islamic geometric frame to the image."""
        draw = ImageDraw.Draw(img)

        # Outer border
        border_width = 8
        draw.rectangle([border_width, border_width, self.width-border_width, self.height-border_width],
                      outline=style_colors['primary_color'], width=3)

        # Inner decorative frame
        inner_margin = 60
        draw.rectangle([inner_margin, inner_margin, self.width-inner_margin, self.height-inner_margin],
                      outline=style_colors['accent_color'], width=2)

        # Corner decorations
        corner_offset = 100
        circle_radius = 15
        corners = [
            (corner_offset, corner_offset),
            (self.width - corner_offset, corner_offset),
            (corner_offset, self.height - corner_offset),
            (self.width - corner_offset, self.height - corner_offset)
        ]

        for corner_x, corner_y in corners:
            draw.ellipse([corner_x - circle_radius, corner_y - circle_radius,
                         corner_x + circle_radius, corner_y + circle_radius],
                        outline=style_colors['accent_color'], width=2)

        return img

    def add_watermark(self, img):
        """Add AliToucan watermark to the image."""
        draw = ImageDraw.Draw(img)
        branding = self.content.get_branding_info()

        font, _ = self.arabic_renderer.get_font(branding['watermark_size'])

        # Position watermark at bottom right
        watermark_x = self.width - 150
        watermark_y = self.height - 40

        draw.text((watermark_x, watermark_y), branding['watermark_text'],
                  font=font, fill=branding['watermark_color'])

        return img

    def create_scene_frame(self, scene_name, frame_number=0, total_frames=1):
        """Create a single frame for a scene with animations."""
        scene_data = self.content.get_scene_content(scene_name)
        style_name = scene_data.get('style', 'elegant')
        style_colors = self.content.get_scene_styles()[style_name]

        # Create base image
        img = Image.new('RGB', self.resolution, style_colors['bg_color'])

        # Add decorative frame
        img = self.create_decorative_frame(img, style_colors)

        # Calculate animation progress (0.0 to 1.0)
        progress = frame_number / max(1, total_frames - 1) if total_frames > 1 else 1.0

        # Get fonts
        title_font, _ = self.arabic_renderer.get_font(48)
        main_font, _ = self.arabic_renderer.get_font(36)
        subtitle_font, _ = self.arabic_renderer.get_font(28)

        # Calculate text positions based on aspect ratio
        if self.aspect_ratio == '1:1':
            y_positions = self._get_square_layout()
        else:  # 16:9
            y_positions = self._get_widescreen_layout()

        # Draw main Arabic text
        main_text = scene_data.get('arabic', '')
        if main_text:
            # Add fade-in animation
            alpha = int(255 * min(1.0, progress * 2))
            text_color = (*style_colors['primary_color'], alpha) if len(style_colors['primary_color']) == 3 else style_colors['primary_color']

            self.arabic_renderer.draw_centered_arabic_text(
                ImageDraw.Draw(img), main_text, y_positions['main'],
                self.width, title_font, text_color[:3], shadow=True
            )

        # Draw subtitle if available
        subtitle = scene_data.get('subtitle', '')
        if subtitle:
            alpha = int(255 * min(1.0, (progress - 0.3) * 2)) if progress > 0.3 else 0
            subtitle_color = (*style_colors['secondary_color'], alpha) if len(style_colors['secondary_color']) == 3 else style_colors['secondary_color']

            self.arabic_renderer.draw_centered_arabic_text(
                ImageDraw.Draw(img), subtitle, y_positions['subtitle'],
                self.width, subtitle_font, subtitle_color[:3], shadow=True
            )

        # Add decorative line with animation
        if progress > 0.5:
            line_progress = (progress - 0.5) * 2
            self._add_animated_line(img, style_colors['accent_color'], line_progress)

        # Add watermark
        img = self.add_watermark(img)

        return img

    def _get_square_layout(self):
        """Get layout positions for 1:1 aspect ratio."""
        return {
            'main': self.height // 2 - 50,
            'subtitle': self.height // 2 + 50,
            'decorative_line': self.height - 200
        }

    def _get_widescreen_layout(self):
        """Get layout positions for 16:9 aspect ratio."""
        return {
            'main': self.height // 2 - 30,
            'subtitle': self.height // 2 + 30,
            'decorative_line': self.height - 150
        }

    def _add_animated_line(self, img, color, progress):
        """Add animated decorative line."""
        draw = ImageDraw.Draw(img)

        line_y = self.height - 200 if self.aspect_ratio == '1:1' else self.height - 150
        line_start = self.width // 4
        line_end = 3 * self.width // 4

        # Animate line drawing from center outward
        center = self.width // 2
        half_length = (line_end - line_start) // 2
        animated_length = int(half_length * progress)

        current_start = center - animated_length
        current_end = center + animated_length

        if animated_length > 0:
            draw.line([(current_start, line_y), (current_end, line_y)], fill=color, width=3)

    def create_scene_frames(self, scene_name, fps=30):
        """Create all frames for a scene."""
        scene_data = self.content.get_scene_content(scene_name)
        duration = scene_data.get('duration', 3.0)
        total_frames = int(duration * fps)

        frames = []
        for frame_num in range(total_frames):
            frame = self.create_scene_frame(scene_name, frame_num, total_frames)
            frames.append(frame)

        return frames

    def create_transition_frames(self, from_frame, to_frame, transition_duration=0.5, fps=30):
        """Create smooth transition frames between scenes."""
        transition_frames = int(transition_duration * fps)
        frames = []

        for i in range(transition_frames):
            progress = i / (transition_frames - 1) if transition_frames > 1 else 1.0

            # Simple fade transition
            blended = Image.blend(from_frame, to_frame, progress)
            frames.append(blended)

        return frames

    def create_video_frames(self, fps=30, include_transitions=True):
        """Create all video frames."""
        print("Creating video frames...")
        all_frames = []
        scenes = self.content.get_all_scenes()

        previous_frame = None

        for i, scene_name in enumerate(scenes):
            print(f"  Creating scene {i+1}/{len(scenes)}: {scene_name}")

            # Create scene frames
            scene_frames = self.create_scene_frames(scene_name, fps)

            # Add transition if not the first scene
            if include_transitions and previous_frame is not None and scene_frames:
                transition_frames = self.create_transition_frames(
                    previous_frame, scene_frames[0], 0.3, fps
                )
                all_frames.extend(transition_frames)

            # Add scene frames
            all_frames.extend(scene_frames)

            # Store last frame for next transition
            if scene_frames:
                previous_frame = scene_frames[-1]

        self.frames = all_frames
        print(f"✓ Created {len(all_frames)} total frames")
        return all_frames

    def save_as_gif(self, filename=None, fps=10, optimize=True):
        """Save video as animated GIF."""
        if not self.frames:
            print("⚠️  No frames to save. Create frames first.")
            return None

        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"imam_jawad_condolence_{self.aspect_ratio.replace(':', 'x')}_{timestamp}.gif"

        filepath = os.path.join(self.output_dir, filename)

        # Calculate frame duration in milliseconds
        frame_duration = int(1000 / fps)

        print(f"Saving GIF: {filepath}")
        self.frames[0].save(
            filepath,
            save_all=True,
            append_images=self.frames[1:],
            duration=frame_duration,
            loop=0,
            optimize=optimize
        )

        file_size = os.path.getsize(filepath) / (1024 * 1024)  # MB
        print(f"✓ Saved GIF: {filepath} ({file_size:.1f} MB)")
        return filepath

    def save_frames_as_images(self, prefix="frame"):
        """Save individual frames as PNG images for external video processing."""
        if not self.frames:
            print("⚠️  No frames to save. Create frames first.")
            return []

        frames_dir = os.path.join(self.output_dir, "frames")
        if not os.path.exists(frames_dir):
            os.makedirs(frames_dir)

        saved_files = []
        for i, frame in enumerate(self.frames):
            filename = f"{prefix}_{i:04d}.png"
            filepath = os.path.join(frames_dir, filename)
            frame.save(filepath, "PNG")
            saved_files.append(filepath)

        print(f"✓ Saved {len(saved_files)} frame images to {frames_dir}")
        return saved_files

    def create_mp4_instructions(self):
        """Provide instructions for creating MP4 video from frames."""
        instructions = f"""
To create MP4 video from the generated frames:

1. Install FFmpeg (if not already installed):
   - Windows: Download from https://ffmpeg.org/download.html
   - macOS: brew install ffmpeg
   - Linux: sudo apt install ffmpeg

2. Navigate to the output directory:
   cd "{os.path.abspath(self.output_dir)}"

3. Create MP4 video:
   ffmpeg -framerate 8 -i frames/frame_%04d.png -c:v libx264 -pix_fmt yuv420p -crf 23 imam_jawad_condolence_{self.aspect_ratio.replace(':', 'x')}.mp4

4. For social media optimization:
   ffmpeg -framerate 8 -i frames/frame_%04d.png -c:v libx264 -pix_fmt yuv420p -crf 20 -preset slow -movflags +faststart imam_jawad_condolence_{self.aspect_ratio.replace(':', 'x')}_optimized.mp4

Note: Adjust -framerate value to change video speed (higher = faster)
"""
        return instructions

def main():
    """Main function to create the condolence video."""
    print("Imam al-Jawad (AS) Condolence Video Creator")
    print("=" * 50)

    # Get user preferences
    print("\nSelect aspect ratio:")
    print("1. Square (1:1) - Instagram/Facebook posts")
    print("2. Widescreen (16:9) - YouTube/general video")

    choice = input("Enter choice [1]: ").strip() or "1"
    aspect_ratio = "1:1" if choice == "1" else "16:9"

    print("\nSelect output format:")
    print("1. GIF (animated, smaller file)")
    print("2. PNG frames + MP4 instructions (higher quality)")
    print("3. Both")

    format_choice = input("Enter choice [1]: ").strip() or "1"

    try:
        # Create video
        creator = ImamJawadVideoCreator(aspect_ratio=aspect_ratio)

        print(f"\nCreating {aspect_ratio} condolence video...")
        print(f"Resolution: {creator.resolution[0]}x{creator.resolution[1]}")

        # Create frames
        frames = creator.create_video_frames(fps=15, include_transitions=True)

        output_files = []

        # Save based on user choice
        if format_choice in ["1", "3"]:
            # Save as GIF
            gif_file = creator.save_as_gif(fps=8, optimize=True)
            if gif_file:
                output_files.append(gif_file)

        if format_choice in ["2", "3"]:
            # Save frames for MP4 creation
            frame_files = creator.save_frames_as_images()
            if frame_files:
                output_files.extend(frame_files[:3])  # Show first 3 frame files

                # Show MP4 creation instructions
                print("\n" + "=" * 50)
                print("MP4 Creation Instructions:")
                print(creator.create_mp4_instructions())

        if output_files:
            print("\n" + "=" * 50)
            print("Video Creation Complete!")
            print(f"✓ Total frames created: {len(frames)}")
            print(f"✓ Estimated duration: {len(frames)/8:.1f} seconds")
            print(f"✓ Output directory: {creator.output_dir}")

            # Try to open the first output file
            if format_choice in ["1", "3"] and len(output_files) > 0:
                try:
                    first_file = output_files[0]
                    if sys.platform == "win32":
                        os.startfile(first_file)
                    elif sys.platform == "darwin":
                        os.system(f'open "{first_file}"')
                    else:
                        os.system(f'xdg-open "{first_file}"')
                    print("✓ Video opened for preview")
                except Exception:
                    print("Note: Could not open video automatically")

        return output_files

    except Exception as e:
        print(f"❌ Error creating video: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
