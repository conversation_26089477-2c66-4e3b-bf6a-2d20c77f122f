#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Download Manager for <PERSON> Condolence Designs
Manages both original and enhanced versions with Arabic text improvements.
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_comprehensive_download_package():
    """
    Create a comprehensive package with both original and enhanced designs.
    """
    # Check for both design directories
    original_dir = "imam_jawad_designs"
    enhanced_dir = "enhanced_imam_jawad_designs"
    
    if not os.path.exists(enhanced_dir):
        print("Error: Enhanced designs not found. Please run generate_enhanced_imam_jawad_designs.py first.")
        return None
    
    # Create timestamp for unique package name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"imam_jawad_complete_collection_{timestamp}"
    
    # Create package directory
    package_dir = f"{package_name}_package"
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)
    
    # Create subdirectories
    original_subdir = os.path.join(package_dir, "01_Original_Designs")
    enhanced_subdir = os.path.join(package_dir, "02_Enhanced_Arabic_Designs")
    os.makedirs(original_subdir, exist_ok=True)
    os.makedirs(enhanced_subdir, exist_ok=True)
    
    design_files = []
    
    # Copy original designs if they exist
    if os.path.exists(original_dir):
        for filename in os.listdir(original_dir):
            if filename.endswith(('.png', '.jpg', '.jpeg')):
                src_path = os.path.join(original_dir, filename)
                dst_path = os.path.join(original_subdir, filename)
                shutil.copy2(src_path, dst_path)
                design_files.append(f"01_Original_Designs/{filename}")
    
    # Copy enhanced designs
    for filename in os.listdir(enhanced_dir):
        if filename.endswith(('.png', '.jpg', '.jpeg')):
            src_path = os.path.join(enhanced_dir, filename)
            dst_path = os.path.join(enhanced_subdir, filename)
            shutil.copy2(src_path, dst_path)
            design_files.append(f"02_Enhanced_Arabic_Designs/{filename}")
    
    # Create comprehensive README
    readme_content = f"""# Imam al-Jawad (AS) Condolence Designs - Complete Collection
Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## About This Collection
This comprehensive collection commemorates the martyrdom of Imam Muhammad al-Jawad (AS) 
on the 29th of Dhul Qi'dah. The collection includes both original and enhanced versions 
with superior Arabic text rendering.

## Collection Contents

### 01_Original_Designs/
Basic designs with standard text rendering:
- Classic color schemes
- Standard font handling
- Basic Arabic text support

### 02_Enhanced_Arabic_Designs/ ⭐ RECOMMENDED
Advanced designs with enhanced Arabic text rendering:
- ✅ Proper Arabic RTL (Right-to-Left) text direction
- ✅ Arabic text shaping and ligature support
- ✅ Enhanced font sizes for better readability
- ✅ Text shadows and outlines for improved visibility
- ✅ Superior typography and spacing
- ✅ Higher quality image output
- ✅ Enhanced decorative elements

## Arabic Text Improvements in Enhanced Versions

### Technical Enhancements:
1. **Arabic Text Processing**: Uses arabic-reshaper and python-bidi libraries
2. **Proper RTL Rendering**: Correct right-to-left text direction
3. **Text Shaping**: Proper Arabic letter connections and ligatures
4. **Font Optimization**: Better Arabic font selection and sizing
5. **Visual Effects**: Text shadows and outlines for better contrast

### Typography Improvements:
- Increased font sizes (Title: 52px, Main: 40px, Subtitle: 32px)
- Better line spacing and text positioning
- Enhanced contrast and readability
- Professional text effects (shadows/outlines)

## Design Variations

### Color Schemes:
1. **Classic Black & Gold** - Traditional mourning colors with elegant gold text
2. **Elegant Dark Gray & Silver** - Sophisticated modern look
3. **Traditional Black & White** - Classic high-contrast design
4. **Royal Navy & Gold** - Distinguished deep blue with gold accents

### Text Effects:
- **Shadow Versions**: Text with drop shadows for depth
- **Outline Versions**: Text with outlines for maximum contrast

## Technical Specifications
- **Aspect Ratio**: 1:1 (Square format - 1080x1080 pixels)
- **Formats**: PNG (best quality) and JPG (smaller file size)
- **Quality**: High resolution suitable for social media sharing
- **Arabic Support**: Full RTL text rendering with proper shaping

## Arabic Content (Authentic Shia Sources)
All designs include authentic Arabic text based on Shia Islamic sources:

- **بسم الله الرحمن الرحيم** (Bismillah - In the name of Allah)
- **بمناسبة ذكرى استشهاد** (On the occasion of the martyrdom)
- **الإمام محمد الجواد عليه السلام** (Imam Muhammad al-Jawad, peace be upon him)
- **التاسع والعشرون من ذي القعدة** (29th of Dhul Qi'dah)
- **أحيا الله ذكراكم وأعظم أجوركم** (May Allah revive your remembrance and magnify your reward)
- **اللهم صل على محمد وآل محمد** (O Allah, send blessings upon Muhammad and the family of Muhammad)

## Usage Recommendations

### For Social Media:
- **Instagram/Facebook Posts**: Use PNG versions from Enhanced collection
- **WhatsApp Sharing**: Use JPG versions for smaller file size
- **Twitter**: Either format works well

### For Print:
- Use PNG versions at full resolution
- Enhanced versions recommended for better text clarity

### For Digital Displays:
- Enhanced versions provide superior readability
- Shadow versions work well on light backgrounds
- Outline versions work well on varied backgrounds

## Cultural Notes
- All designs follow Shia Islamic mourning traditions
- Uses respectful terminology appropriate for commemorating the Imam's martyrdom
- Arabic text is authentic and culturally appropriate
- Designed with reverence for Imam al-Jawad (AS) and the Ahlul Bayt

## Branding
All designs include "AliToucan" watermark as requested.

## Technical Requirements for Enhanced Features
The enhanced designs were created using:
- Python PIL (Pillow) library
- arabic-reshaper library for text shaping
- python-bidi library for bidirectional text support

---
**Recommendation**: Use the Enhanced Arabic Designs (02_Enhanced_Arabic_Designs/) 
for the best visual quality and proper Arabic text rendering.

Created by: AliToucan Design System
اللهم صل على محمد وآل محمد
"""
    
    readme_path = os.path.join(package_dir, "README.txt")
    with open(readme_path, 'w', encoding='utf-8', errors='replace') as f:
        f.write(readme_content)
    
    # Create ZIP archive
    zip_path = f"{package_name}.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arcname)
    
    # Calculate package info
    package_size = os.path.getsize(zip_path) / 1024  # Size in KB
    file_count = len(design_files) + 1  # +1 for README
    
    return {
        'zip_path': zip_path,
        'package_dir': package_dir,
        'file_count': file_count,
        'package_size': package_size,
        'design_files': design_files
    }

def compare_designs():
    """Compare original vs enhanced designs."""
    original_dir = "imam_jawad_designs"
    enhanced_dir = "enhanced_imam_jawad_designs"
    
    print("📊 Design Collection Comparison")
    print("=" * 50)
    
    # Count files
    original_count = 0
    enhanced_count = 0
    
    if os.path.exists(original_dir):
        original_count = len([f for f in os.listdir(original_dir) if f.endswith(('.png', '.jpg'))])
    
    if os.path.exists(enhanced_dir):
        enhanced_count = len([f for f in os.listdir(enhanced_dir) if f.endswith(('.png', '.jpg'))])
    
    print(f"📁 Original Designs: {original_count} files")
    print(f"✨ Enhanced Designs: {enhanced_count} files")
    
    if enhanced_count > 0:
        print("\n🎯 Enhanced Features:")
        print("  ✅ Proper Arabic RTL text rendering")
        print("  ✅ Arabic text shaping and ligatures")
        print("  ✅ Larger font sizes (52px/40px/32px)")
        print("  ✅ Text shadows and outlines")
        print("  ✅ Better spacing and typography")
        print("  ✅ Higher quality output (98% JPEG quality)")
        print("  ✅ Enhanced decorative elements")
        
        print("\n💡 Recommendation:")
        print("  Use Enhanced Designs for best Arabic text quality")
    
    return original_count, enhanced_count

def main():
    """Main enhanced download manager interface."""
    print("🕌 Enhanced Imam al-Jawad (AS) Design Download Manager")
    print("=" * 60)
    print("Manages both original and enhanced Arabic text versions")
    print()
    
    while True:
        print("\nOptions:")
        print("1. Compare design collections")
        print("2. Create comprehensive download package")
        print("3. Open enhanced designs folder")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            compare_designs()
        
        elif choice == "2":
            print("\n📦 Creating comprehensive download package...")
            package_info = create_comprehensive_download_package()
            
            if package_info:
                print("✅ Comprehensive package created successfully!")
                print(f"  📦 Package: {package_info['zip_path']}")
                print(f"  📁 Files: {package_info['file_count']}")
                print(f"  💾 Size: {package_info['package_size']:.1f} KB")
                print(f"  📍 Location: {os.path.abspath(package_info['zip_path'])}")
                
                # Clean up temporary directory
                if os.path.exists(package_info['package_dir']):
                    shutil.rmtree(package_info['package_dir'])
                    print("  🧹 Temporary files cleaned up")
                
                print("\n💡 Package includes both original and enhanced versions")
                print("   Recommended: Use Enhanced Arabic Designs folder")
            else:
                print("❌ Failed to create package")
        
        elif choice == "3":
            enhanced_dir = "enhanced_imam_jawad_designs"
            if os.path.exists(enhanced_dir):
                try:
                    os.startfile(os.path.abspath(enhanced_dir))
                    print(f"📂 Opened: {os.path.abspath(enhanced_dir)}")
                except:
                    print(f"📍 Please open manually: {os.path.abspath(enhanced_dir)}")
            else:
                print("❌ Enhanced designs folder not found")
        
        elif choice == "4":
            print("\n🤲 Thank you for using the Enhanced Design Manager!")
            print("May Allah bless the memory of Imam al-Jawad (AS)")
            print("اللهم صل على محمد وآل محمد")
            break
        
        else:
            print("❌ Invalid option. Please select 1-4.")

if __name__ == "__main__":
    main()
