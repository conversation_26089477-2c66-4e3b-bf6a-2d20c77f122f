<!DOCTYPE html>

<html lang="en" data-content_root="../">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="viewport" content="width=device-width, initial-scale=1" />

    <title>pygame._sdl2.controller &#8212; pygame v2.6.1 documentation</title>
    <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
    <link rel="stylesheet" type="text/css" href="../_static/pygame.css?v=a854c6a8" />
    <script src="../_static/documentation_options.js?v=0a414f3d"></script>
    <script src="../_static/doctools.js?v=9a2dae69"></script>
    <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <link rel="icon" href="../_static/pygame.ico"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="pygame.sdl2_video" href="sdl2_video.html" />
    <link rel="prev" title="pygame.scrap" href="scrap.html" /> 
  </head><body>  

    <div class="document">

  <div class="header">
	<div class="flex-container">
	<div class="logo">
	  <a href="https://www.pygame.org/">
	    <img src="../_static/pygame_tiny.png" alt="logo image"/>
	  </a>
	  <h5>pygame documentation</h5>
	</div>
	<div class="pagelinks">
	  <div class="top">
	    <a href="https://www.pygame.org/">Pygame Home</a> ||
	    <a href="../index.html">Help Contents</a> ||
	    <a href="../genindex.html">Reference Index</a>

        <form action="../search.html" method="get" style="display:inline;float:right;">
          <input name="q" value="" type="text">
          <input value="search" type="submit">
        </form>
	  </div>
	  <hr style="color:black;border-bottom:none;border-style: dotted;border-bottom-style:none;">
	  <p class="bottom"><strong>Most useful stuff</strong>:
	    <a href="color.html">Color</a> | 
	    <a href="display.html">display</a> | 
	    <a href="draw.html">draw</a> | 
	    <a href="event.html">event</a> | 
	    <a href="font.html">font</a> | 
	    <a href="image.html">image</a> | 
	    <a href="key.html">key</a> | 
	    <a href="locals.html">locals</a> | 
	    <a href="mixer.html">mixer</a> | 
	    <a href="mouse.html">mouse</a> | 
	    <a href="rect.html">Rect</a> | 
	    <a href="surface.html">Surface</a> | 
	    <a href="time.html">time</a> | 
	    <a href="music.html">music</a> | 
	    <a href="pygame.html">pygame</a>
	  </p>

	  <p class="bottom"><strong>Advanced stuff</strong>:
	    <a href="cursors.html">cursors</a> | 
	    <a href="joystick.html">joystick</a> | 
	    <a href="mask.html">mask</a> | 
	    <a href="sprite.html">sprite</a> | 
	    <a href="transform.html">transform</a> | 
	    <a href="bufferproxy.html">BufferProxy</a> | 
	    <a href="freetype.html">freetype</a> | 
	    <a href="gfxdraw.html">gfxdraw</a> | 
	    <a href="midi.html">midi</a> | 
	    <a href="pixelarray.html">PixelArray</a> | 
	    <a href="pixelcopy.html">pixelcopy</a> | 
	    <a href="sndarray.html">sndarray</a> | 
	    <a href="surfarray.html">surfarray</a> | 
	    <a href="math.html">math</a>
	  </p>

	  <p class="bottom"><strong>Other</strong>:
	    <a href="camera.html">camera</a> | 
	    <a href="sdl2_controller.html#module-pygame._sdl2.controller">controller</a> | 
	    <a href="examples.html">examples</a> | 
	    <a href="fastevent.html">fastevent</a> | 
	    <a href="scrap.html">scrap</a> | 
	    <a href="tests.html">tests</a> | 
	    <a href="touch.html">touch</a> | 
	    <a href="pygame.html#module-pygame.version">version</a>
	  </p>
</div>
</div>
  </div>

      <div class="documentwrapper">
          <div class="body" role="main">
            
<section id="module-pygame._sdl2.controller">
<span id="pygame-sdl2-controller"></span><dl class="definition">
<dt class="title module sig sig-object">
<code class="docutils literal notranslate"><span class="pre">pygame._sdl2.controller</span></code></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Pygame module to work with controllers.</span></div>
</div>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.init">pygame._sdl2.controller.init</a></div>
</td>
<td>—</td>
<td>initialize the controller module</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.quit">pygame._sdl2.controller.quit</a></div>
</td>
<td>—</td>
<td>Uninitialize the controller module.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.get_init">pygame._sdl2.controller.get_init</a></div>
</td>
<td>—</td>
<td>Returns True if the controller module is initialized.</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.set_eventstate">pygame._sdl2.controller.set_eventstate</a></div>
</td>
<td>—</td>
<td>Sets the current state of events related to controllers</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.get_eventstate">pygame._sdl2.controller.get_eventstate</a></div>
</td>
<td>—</td>
<td>Gets the current state of events related to controllers</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.get_count">pygame._sdl2.controller.get_count</a></div>
</td>
<td>—</td>
<td>Get the number of joysticks connected</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.is_controller">pygame._sdl2.controller.is_controller</a></div>
</td>
<td>—</td>
<td>Check if the given joystick is supported by the game controller interface</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.name_forindex">pygame._sdl2.controller.name_forindex</a></div>
</td>
<td>—</td>
<td>Get the name of the controller</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller">pygame._sdl2.controller.Controller</a></div>
</td>
<td>—</td>
<td>Create a new Controller object.</td>
</tr>
</tbody>
</table>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Use import pygame._sdl2.controller before using this module.</p>
</div>
<p>This module offers control over common controller types like the dualshock 4 or
the xbox 360 controllers: They have two analog sticks, two triggers, two shoulder buttons,
a dpad, 4 buttons on the side, 2 (or 3) buttons in the middle.</p>
<p>Pygame uses xbox controllers naming conventions (like a, b, x, y for buttons) but
they always refer to the same buttons. For example <code class="docutils literal notranslate"><span class="pre">CONTROLLER_BUTTON_X</span></code> is
always the leftmost button of the 4 buttons on the right.</p>
<p>Controllers can generate the following events:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">CONTROLLERAXISMOTION</span><span class="p">,</span> <span class="n">CONTROLLERBUTTONDOWN</span><span class="p">,</span> <span class="n">CONTROLLERBUTTONUP</span><span class="p">,</span>
<span class="n">CONTROLLERDEVICEREMAPPED</span><span class="p">,</span> <span class="n">CONTROLLERDEVICEADDED</span><span class="p">,</span> <span class="n">CONTROLLERDEVICEREMOVED</span>
</pre></div>
</div>
<p>Additionally if pygame is built with SDL 2.0.14 or higher the following events can also be generated
(to get the version of sdl pygame is built with use <a class="tooltip reference internal" href="pygame.html#pygame.version.SDL" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.version.SDL()</span></code><span class="tooltip-content">tupled integers of the SDL library version</span></a>):</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">CONTROLLERTOUCHPADDOWN</span><span class="p">,</span> <span class="n">CONTROLLERTOUCHPADMOTION</span><span class="p">,</span> <span class="n">CONTROLLERTOUCHPADUP</span>
</pre></div>
</div>
<p>These events can be enabled/disabled by <a class="tooltip reference internal" href="#pygame._sdl2.controller.set_eventstate" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame._sdl2.controller.set_eventstate()</span></code><span class="tooltip-content">Sets the current state of events related to controllers</span></a>
Note that controllers can generate joystick events as well. This function only toggles
events related to controllers.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>See the <a class="tooltip reference internal" href="joystick.html#module-pygame.joystick" title=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame.joystick</span></code><span class="tooltip-content">Pygame module for interacting with joysticks, gamepads, and trackballs.</span></a> for a more versatile but more advanced api.</p>
</div>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2: </span>This module requires SDL2.</p>
</div>
<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.init">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">initialize the controller module</span></div>
<div class="line"><span class="signature">init() -&gt; None</span></div>
</div>
<p>Initialize the controller module.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.quit">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.quit" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Uninitialize the controller module.</span></div>
<div class="line"><span class="signature">quit() -&gt; None</span></div>
</div>
<p>Uninitialize the controller module.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.get_init">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">get_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.get_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Returns True if the controller module is initialized.</span></div>
<div class="line"><span class="signature">get_init() -&gt; bool</span></div>
</div>
<p>Test if <code class="docutils literal notranslate"><span class="pre">pygame._sdl2.controller.init()</span></code> was called.</p>
<blockquote>
<div></div></blockquote>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.set_eventstate">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">set_eventstate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.set_eventstate" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Sets the current state of events related to controllers</span></div>
<div class="line"><span class="signature">set_eventstate(state) -&gt; None</span></div>
</div>
<p>Enable or disable events connected to controllers.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Controllers can still generate joystick events, which will not be toggled by this function.</p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.2:: </span>Changed return type from int to None</p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.get_eventstate">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">get_eventstate</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.get_eventstate" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Gets the current state of events related to controllers</span></div>
<div class="line"><span class="signature">get_eventstate() -&gt; bool</span></div>
</div>
<p>Returns the current state of events related to controllers, True meaning
events will be posted.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.2.</span></p>
</div>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.get_count">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">get_count</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.get_count" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Get the number of joysticks connected</span></div>
<div class="line"><span class="signature">get_count() -&gt; int</span></div>
</div>
<p>Get the number of joysticks connected.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.is_controller">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">is_controller</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.is_controller" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Check if the given joystick is supported by the game controller interface</span></div>
<div class="line"><span class="signature">is_controller(index) -&gt; bool</span></div>
</div>
<p>Returns True if the index given can be used to create a controller object.</p>
</dd></dl>

<dl class="py function definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.name_forindex">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">name_forindex</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.name_forindex" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Get the name of the controller</span></div>
<div class="line"><span class="signature">name_forindex(index) -&gt; name or None</span></div>
</div>
<p>Returns the name of controller, or None if there's no name or the
index is invalid.</p>
</dd></dl>

<dl class="py class definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller">
<span class="sig-prename descclassname"><span class="pre">pygame._sdl2.controller.</span></span><span class="sig-name descname"><span class="pre">Controller</span></span><a class="headerlink" href="#pygame._sdl2.controller.Controller" title="Link to this definition">¶</a></dt>
<dd><blockquote>
<div><div class="line-block">
<div class="line"><span class="summaryline">Create a new Controller object.</span></div>
<div class="line"><span class="signature">Controller(index) -&gt; Controller</span></div>
</div>
<p>Create a new Controller object. Index should be integer between
0 and <code class="docutils literal notranslate"><span class="pre">pygame._sdl2.controller.get_count()</span></code>. Controllers also
can be created from a <code class="docutils literal notranslate"><span class="pre">pygame.joystick.Joystick</span></code> using
<code class="docutils literal notranslate"><span class="pre">pygame._sdl2.controller.from_joystick</span></code>. Controllers are
initialized on creation.</p>
</div></blockquote>
<table class="toc docutils align-default">
<tbody>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.quit">pygame._sdl2.controller.Controller.quit</a></div>
</td>
<td>—</td>
<td>uninitialize the Controller</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.get_init">pygame._sdl2.controller.Controller.get_init</a></div>
</td>
<td>—</td>
<td>check if the Controller is initialized</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.from_joystick">pygame._sdl2.controller.Controller.from_joystick</a></div>
</td>
<td>—</td>
<td>Create a Controller from a pygame.joystick.Joystick object</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.attached">pygame._sdl2.controller.Controller.attached</a></div>
</td>
<td>—</td>
<td>Check if the Controller has been opened and is currently connected.</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.as_joystick">pygame._sdl2.controller.Controller.as_joystick</a></div>
</td>
<td>—</td>
<td>Returns a pygame.joystick.Joystick() object</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.get_axis">pygame._sdl2.controller.Controller.get_axis</a></div>
</td>
<td>—</td>
<td>Get the current state of a joystick axis</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.get_button">pygame._sdl2.controller.Controller.get_button</a></div>
</td>
<td>—</td>
<td>Get the current state of a button</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.get_mapping">pygame._sdl2.controller.Controller.get_mapping</a></div>
</td>
<td>—</td>
<td>Get the mapping assigned to the controller</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.set_mapping">pygame._sdl2.controller.Controller.set_mapping</a></div>
</td>
<td>—</td>
<td>Assign a mapping to the controller</td>
</tr>
<tr class="row-even"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.rumble">pygame._sdl2.controller.Controller.rumble</a></div>
</td>
<td>—</td>
<td>Start a rumbling effect</td>
</tr>
<tr class="row-odd"><td><div class="line"><a class="toc reference external" href="sdl2_controller.html#pygame._sdl2.controller.Controller.stop_rumble">pygame._sdl2.controller.Controller.stop_rumble</a></div>
</td>
<td>—</td>
<td>Stop any rumble effect playing</td>
</tr>
</tbody>
</table>
<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.quit">
<span class="sig-name descname"><span class="pre">quit</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.quit" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">uninitialize the Controller</span></div>
<div class="line"><span class="signature">quit() -&gt; None</span></div>
</div>
<p>Close a Controller object. After this the pygame event queue will no longer
receive events from the device.</p>
<p>It is safe to call this more than once.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.get_init">
<span class="sig-name descname"><span class="pre">get_init</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.get_init" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">check if the Controller is initialized</span></div>
<div class="line"><span class="signature">get_init() -&gt; bool</span></div>
</div>
<p>Returns True if the Controller object is currently initialised.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.from_joystick">
<em class="property"><span class="pre">static</span><span class="w"> </span></em><span class="sig-name descname"><span class="pre">from_joystick</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.from_joystick" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Create a Controller from a pygame.joystick.Joystick object</span></div>
<div class="line"><span class="signature">from_joystick(joystick) -&gt; Controller</span></div>
</div>
<p>Create a Controller object from a <code class="docutils literal notranslate"><span class="pre">pygame.joystick.Joystick</span></code> object</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.attached">
<span class="sig-name descname"><span class="pre">attached</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.attached" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Check if the Controller has been opened and is currently connected.</span></div>
<div class="line"><span class="signature">attached() -&gt; bool</span></div>
</div>
<p>Returns True if the Controller object is opened and connected.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.as_joystick">
<span class="sig-name descname"><span class="pre">as_joystick</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.as_joystick" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Returns a pygame.joystick.Joystick() object</span></div>
<div class="line"><span class="signature">as_joystick() -&gt; Joystick object</span></div>
</div>
<p>Returns a pygame.joystick.Joystick() object created from this controller's index</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.get_axis">
<span class="sig-name descname"><span class="pre">get_axis</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.get_axis" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Get the current state of a joystick axis</span></div>
<div class="line"><span class="signature">get_axis(axis) -&gt; int</span></div>
</div>
<p>Get the current state of a trigger or joystick axis.
The axis argument must be one of the following constants:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">CONTROLLER_AXIS_LEFTX</span><span class="p">,</span> <span class="n">CONTROLLER_AXIS_LEFTY</span><span class="p">,</span>
<span class="n">CONTROLLER_AXIS_RIGHTX</span><span class="p">,</span> <span class="n">CONTROLLER_AXIS_RIGHTY</span><span class="p">,</span>
<span class="n">CONTROLLER_AXIS_TRIGGERLEFT</span><span class="p">,</span> <span class="n">CONTROLLER_AXIS_TRIGGERRIGHT</span>
</pre></div>
</div>
<p>Joysticks can return a value between -32768 and 32767. Triggers however
can only return a value between 0 and 32768.</p>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.get_button">
<span class="sig-name descname"><span class="pre">get_button</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.get_button" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Get the current state of a button</span></div>
<div class="line"><span class="signature">get_button(button) -&gt; bool</span></div>
</div>
<p>Get the current state of a button, True meaning it is pressed down.
The button argument must be one of the following constants:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">CONTROLLER_BUTTON_A</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_B</span><span class="p">,</span>
<span class="n">CONTROLLER_BUTTON_X</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_Y</span>
<span class="n">CONTROLLER_BUTTON_DPAD_UP</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_DPAD_DOWN</span><span class="p">,</span>
<span class="n">CONTROLLER_BUTTON_DPAD_LEFT</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_DPAD_RIGHT</span><span class="p">,</span>
<span class="n">CONTROLLER_BUTTON_LEFTSHOULDER</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_RIGHTSHOULDER</span><span class="p">,</span>
<span class="n">CONTROLLER_BUTTON_LEFTSTICK</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_RIGHTSTICK</span><span class="p">,</span>
<span class="n">CONTROLLER_BUTTON_BACK</span><span class="p">,</span> <span class="n">CONTROLLER_BUTTON_GUIDE</span><span class="p">,</span>
<span class="n">CONTROLLER_BUTTON_START</span>
</pre></div>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.get_mapping">
<span class="sig-name descname"><span class="pre">get_mapping</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.get_mapping" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Get the mapping assigned to the controller</span></div>
<div class="line"><span class="signature">get_mapping() -&gt; mapping</span></div>
</div>
<p>Returns a dict containing the mapping of the Controller. For more
information see <a class="reference internal" href="#pygame._sdl2.controller.Controller.set_mapping" title="pygame._sdl2.controller.Controller.set_mapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Controller.set_mapping()</span></code></a></p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.2:: </span>Return type changed from <code class="docutils literal notranslate"><span class="pre">str</span></code> to <code class="docutils literal notranslate"><span class="pre">dict</span></code></p>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.set_mapping">
<span class="sig-name descname"><span class="pre">set_mapping</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.set_mapping" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Assign a mapping to the controller</span></div>
<div class="line"><span class="signature">set_mapping(mapping) -&gt; int</span></div>
</div>
<p>Rebind buttons, axes, triggers and dpads. The mapping should be a
dict containing all buttons, hats and axes. The easiest way to get this
is to use the dict returned by <a class="reference internal" href="#pygame._sdl2.controller.Controller.get_mapping" title="pygame._sdl2.controller.Controller.get_mapping"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Controller.get_mapping()</span></code></a>. To edit
this mapping assign a value to the original button. The value of the
dictionary must be a button, hat or axis represented in the following way:</p>
<ul class="simple">
<li><p>For a button use: bX where X is the index of the button.</p></li>
<li><p>For a hat use: hX.Y where X is the index and the Y is the direction (up: 1, right: 2, down: 3, left: 4).</p></li>
<li><p>For an axis use: aX where x is the index of the axis.</p></li>
</ul>
<p>An example of mapping:</p>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">mapping</span> <span class="o">=</span> <span class="n">controller</span><span class="o">.</span><span class="n">get_mapping</span><span class="p">()</span> <span class="c1"># Get current mapping</span>
<span class="n">mapping</span><span class="p">[</span><span class="s2">&quot;a&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;b3&quot;</span> <span class="c1"># Remap button a to y</span>
<span class="n">mapping</span><span class="p">[</span><span class="s2">&quot;y&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="s2">&quot;b0&quot;</span> <span class="c1"># Remap button y to a</span>
<span class="n">controller</span><span class="o">.</span><span class="n">set_mapping</span><span class="p">(</span><span class="n">mapping</span><span class="p">)</span> <span class="c1"># Set the mapping</span>
</pre></div>
</div>
<p>The function will return 1 if a new mapping is added or 0 if an existing one is updated.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.2:: </span>Renamed from <code class="docutils literal notranslate"><span class="pre">add_mapping</span></code> to <code class="docutils literal notranslate"><span class="pre">set_mapping</span></code></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in pygame 2.0.2:: </span>Argument type changed from <code class="docutils literal notranslate"><span class="pre">str</span></code> to <code class="docutils literal notranslate"><span class="pre">dict</span></code></p>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.rumble">
<span class="sig-name descname"><span class="pre">rumble</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.rumble" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Start a rumbling effect</span></div>
<div class="line"><span class="signature">rumble(low_frequency, high_frequency, duration) -&gt; bool</span></div>
</div>
<p>Start a rumble effect on the controller, with the specified strength ranging
from 0 to 1. Duration is length of the effect, in ms. Setting the duration
to 0 will play the effect until another one overwrites it or
<a class="reference internal" href="#pygame._sdl2.controller.Controller.stop_rumble" title="pygame._sdl2.controller.Controller.stop_rumble"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Controller.stop_rumble()</span></code></a> is called. If an effect is already
playing, then it will be overwritten.</p>
<p>Returns True if the rumble was played successfully or False if the
controller does not support it or <a class="tooltip reference internal" href="pygame.html#pygame.version.SDL" title=""><code class="xref py py-meth docutils literal notranslate"><span class="pre">pygame.version.SDL()</span></code><span class="tooltip-content">tupled integers of the SDL library version</span></a> is below 2.0.9.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.2.</span></p>
</div>
</dd></dl>

<dl class="py method definition">
<dt class="sig sig-object py title" id="pygame._sdl2.controller.Controller.stop_rumble">
<span class="sig-name descname"><span class="pre">stop_rumble</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#pygame._sdl2.controller.Controller.stop_rumble" title="Link to this definition">¶</a></dt>
<dd><div class="line-block">
<div class="line"><span class="summaryline">Stop any rumble effect playing</span></div>
<div class="line"><span class="signature">stop_rumble() -&gt; None</span></div>
</div>
<p>Stops any rumble effect playing on the controller. See
<a class="reference internal" href="#pygame._sdl2.controller.Controller.rumble" title="pygame._sdl2.controller.Controller.rumble"><code class="xref py py-meth docutils literal notranslate"><span class="pre">Controller.rumble()</span></code></a> for more information.</p>
<div class="versionadded">
<p><span class="versionmodified added">New in pygame 2.0.2.</span></p>
</div>
</dd></dl>

</dd></dl>

</dd></dl>

</section>


<br /><br />
<hr />
<a href="https://github.com/pygame/pygame/edit/main/docs/reST/ref\sdl2_controller.rst" rel="nofollow">Edit on GitHub</a>
            <div class="clearer"></div>
          </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="Related">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../py-modindex.html" title="Python Module Index"
             >modules</a> |</li>
        <li class="right" >
          <a href="sdl2_video.html" title="pygame.sdl2_video"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="scrap.html" title="pygame.scrap"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../index.html">pygame v2.6.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href=""><code class="xref py py-mod docutils literal notranslate"><span class="pre">pygame._sdl2.controller</span></code></a></li>
    <script type="text/javascript" src="https://www.pygame.org/comment/jquery.plugin.docscomments.js"></script>

      </ul>
    </div>
    <div class="footer" role="contentinfo">
    &#169; Copyright 2000-2023, pygame developers.
    </div>
  </body>
</html>