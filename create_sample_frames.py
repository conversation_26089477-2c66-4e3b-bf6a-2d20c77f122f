#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sample Frame Creator for Enhanced Imam al-<PERSON>d Video System
Creates sample frames to demonstrate the visual capabilities.
"""

import os
from datetime import datetime

def create_sample_frames():
    """Create sample frames showcasing different visual styles."""
    print("🎨 CREATING SAMPLE FRAMES - IMAM AL-JAWAD (AS)")
    print("=" * 50)
    
    try:
        from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
        
        # Create sample frame generator
        creator = ProfessionalEnhancedVideoCreator(
            aspect_ratio='1:1',
            duration_variant='short',
            quality='high'
        )
        
        # Create sample frames for different scenes
        sample_scenes = [
            ('title_sequence', 'Title with Calligraphy'),
            ('opening_bismillah', 'Bismillah with Divine Glow'),
            ('imam_full_name', 'Imam Name with Golden Effects'),
            ('quranic_knowledge', 'Quranic Verse with Light'),
            ('condolence_phrase', 'Condolence with Elegant Style')
        ]
        
        sample_dir = "sample_frames_output"
        if not os.path.exists(sample_dir):
            os.makedirs(sample_dir)
            print(f"✓ Created sample directory: {sample_dir}")
        
        created_samples = []
        
        for scene_name, description in sample_scenes:
            if scene_name in creator.content.content:
                print(f"\n🎨 Creating: {description}")
                
                # Create frame at different animation stages
                for progress, stage in [(0.0, 'start'), (0.5, 'mid'), (1.0, 'end')]:
                    try:
                        frame = creator.create_enhanced_scene_frame(
                            scene_name, int(progress * 30), 30, progress
                        )
                        
                        # Save frame
                        timestamp = datetime.now().strftime('%H%M%S')
                        filename = f"{scene_name}_{stage}_{timestamp}.png"
                        filepath = os.path.join(sample_dir, filename)
                        
                        frame.save(filepath, 'PNG')
                        created_samples.append((filepath, f"{description} - {stage}"))
                        print(f"   ✓ {stage.capitalize()} frame: {filename}")
                        
                    except Exception as e:
                        print(f"   ❌ Error creating {stage} frame: {e}")
        
        return created_samples
        
    except Exception as e:
        print(f"❌ Sample frame creation failed: {e}")
        import traceback
        traceback.print_exc()
        return []

def create_style_comparison():
    """Create frames showing different visual styles."""
    print("\n🎨 CREATING STYLE COMPARISON FRAMES")
    print("=" * 40)
    
    try:
        from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
        
        creator = ProfessionalEnhancedVideoCreator(
            aspect_ratio='1:1',
            duration_variant='short',
            quality='high'
        )
        
        # Test different styles with the same content
        test_content = {
            'arabic': 'الإمام محمد الجواد عليه السلام',
            'english': 'Imam Muhammad al-Jawad (peace be upon him)',
            'duration': 4.0,
            'animation': 'fade_with_calligraphy'
        }
        
        styles_to_test = [
            ('elegant', 'Classic Elegant'),
            ('highlighted_gold', 'Golden Highlight'),
            ('quranic_verse', 'Quranic Style'),
            ('spiritual_green', 'Spiritual Green')
        ]
        
        style_dir = "style_comparison_output"
        if not os.path.exists(style_dir):
            os.makedirs(style_dir)
            print(f"✓ Created style directory: {style_dir}")
        
        created_styles = []
        
        for style_name, description in styles_to_test:
            print(f"\n🎨 Creating: {description}")
            
            try:
                # Temporarily modify content for testing
                test_content['style'] = style_name
                original_content = creator.content.content.get('test_scene')
                creator.content.content['test_scene'] = test_content
                
                # Create frame
                frame = creator.create_enhanced_scene_frame('test_scene', 15, 30, 0.5)
                
                # Save frame
                timestamp = datetime.now().strftime('%H%M%S')
                filename = f"style_{style_name}_{timestamp}.png"
                filepath = os.path.join(style_dir, filename)
                
                frame.save(filepath, 'PNG')
                created_styles.append((filepath, description))
                print(f"   ✓ Style frame: {filename}")
                
                # Restore original content
                if original_content:
                    creator.content.content['test_scene'] = original_content
                else:
                    del creator.content.content['test_scene']
                
            except Exception as e:
                print(f"   ❌ Error creating {description}: {e}")
        
        return created_styles
        
    except Exception as e:
        print(f"❌ Style comparison creation failed: {e}")
        return []

def create_aspect_ratio_comparison():
    """Create frames showing different aspect ratios."""
    print("\n📱 CREATING ASPECT RATIO COMPARISON")
    print("=" * 40)
    
    aspect_ratios = [
        ('1:1', 'Instagram Square'),
        ('16:9', 'YouTube Landscape'),
        ('9:16', 'Instagram Story'),
        ('4:5', 'Facebook Feed')
    ]
    
    aspect_dir = "aspect_ratio_output"
    if not os.path.exists(aspect_dir):
        os.makedirs(aspect_dir)
        print(f"✓ Created aspect ratio directory: {aspect_dir}")
    
    created_aspects = []
    
    for aspect_ratio, description in aspect_ratios:
        print(f"\n📱 Creating: {description} ({aspect_ratio})")
        
        try:
            from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
            
            creator = ProfessionalEnhancedVideoCreator(
                aspect_ratio=aspect_ratio,
                duration_variant='short',
                quality='high'
            )
            
            # Create frame for imam name scene
            frame = creator.create_enhanced_scene_frame('imam_full_name', 15, 30, 0.5)
            
            # Save frame
            timestamp = datetime.now().strftime('%H%M%S')
            filename = f"aspect_{aspect_ratio.replace(':', 'x')}_{timestamp}.png"
            filepath = os.path.join(aspect_dir, filename)
            
            frame.save(filepath, 'PNG')
            created_aspects.append((filepath, f"{description} - {creator.resolution[0]}x{creator.resolution[1]}"))
            print(f"   ✓ Aspect frame: {filename} ({creator.resolution[0]}x{creator.resolution[1]})")
            
        except Exception as e:
            print(f"   ❌ Error creating {description}: {e}")
    
    return created_aspects

def main():
    """Main sample creation function."""
    print("🎨 ENHANCED VIDEO SYSTEM - SAMPLE FRAME SHOWCASE")
    print("=" * 60)
    print("🤲 Demonstrating visual capabilities for Imam al-Jawad (AS)")
    print("=" * 60)
    
    all_samples = []
    
    # Create sample frames
    samples = create_sample_frames()
    all_samples.extend(samples)
    
    # Create style comparison
    styles = create_style_comparison()
    all_samples.extend(styles)
    
    # Create aspect ratio comparison
    aspects = create_aspect_ratio_comparison()
    all_samples.extend(aspects)
    
    # Summary
    if all_samples:
        print("\n" + "=" * 60)
        print("🎉 SAMPLE FRAME SHOWCASE COMPLETED!")
        print("=" * 60)
        
        print(f"\n✓ Created {len(all_samples)} sample frames:")
        for filepath, description in all_samples:
            print(f"   📁 {description}: {os.path.basename(filepath)}")
        
        print(f"\n📂 Sample directories created:")
        print(f"   • sample_frames_output/ - Scene progression samples")
        print(f"   • style_comparison_output/ - Visual style comparisons")
        print(f"   • aspect_ratio_output/ - Different aspect ratio formats")
        
        print("\n🎯 DEMONSTRATED FEATURES:")
        features = [
            "✓ Professional Arabic typography with RTL support",
            "✓ Enhanced Islamic geometric patterns",
            "✓ Sophisticated gradient backgrounds",
            "✓ Advanced animation progression",
            "✓ Multiple visual styles for different content types",
            "✓ Optimized layouts for various aspect ratios",
            "✓ Professional color grading and effects",
            "✓ Elegant watermarking and branding"
        ]
        
        for feature in features:
            print(f"  {feature}")
        
        print("\n🤲 May Allah bless the memory of Imam al-Jawad (AS)")
        print("   Sample frames created with respect and reverence")
        
    else:
        print("\n❌ No sample frames were created")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
