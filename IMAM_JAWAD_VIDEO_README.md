# <PERSON> (AS) Condolence Video Creator

A professional video creation system for commemorating the martyrdom anniversary of Imam <PERSON> (peace be upon him) following authentic Shia Islamic traditions.

## Features

### ✨ Content Features
- **Authentic Shia Content**: Based on reliable Shia Islamic sources
- **Proper Arabic Text**: Enhanced RTL text rendering with proper ligatures
- **Cultural Sensitivity**: Respectful mourning traditions and terminology
- **Historical Accuracy**: Verified dates and information from Shia sources
- **AliToucan Branding**: Integrated branding as requested

### 🎬 Video Features
- **Multiple Aspect Ratios**: 1:1 (Instagram/Facebook) and 16:9 (YouTube)
- **High Resolution**: 1080x1080 and 1920x1080 output
- **Smooth Animations**: Fade-in effects and animated decorative elements
- **Professional Design**: Islamic geometric patterns and mourning colors
- **Multiple Formats**: GIF and PNG frames for MP4 conversion

### 🎨 Visual Design
- **Mourning Colors**: Black, dark blue, and appropriate Islamic colors
- **Arabic Typography**: High-quality Arabic fonts with proper rendering
- **Islamic Decorations**: Geometric patterns and traditional elements
- **Elegant Layouts**: Balanced composition for different aspect ratios

## Installation

### Prerequisites
```bash
# Install required Python packages
pip install -r requirements.txt
```

### Required Dependencies
- `Pillow>=9.0.0` - Image processing
- `arabic-reshaper>=3.0.0` - Arabic text reshaping
- `python-bidi>=0.4.2` - Bidirectional text support

### Optional (for MP4 creation)
- **FFmpeg** - For high-quality MP4 video creation
  - Windows: Download from https://ffmpeg.org/download.html
  - macOS: `brew install ffmpeg`
  - Linux: `sudo apt install ffmpeg`

## Quick Start

### 1. Test the System
```bash
python test_imam_jawad_video.py
```
This will verify all components work correctly.

### 2. Create the Video
```bash
python imam_jawad_video_creator.py
```

### 3. Follow the Interactive Prompts
- Choose aspect ratio (1:1 or 16:9)
- Select output format (GIF, PNG frames, or both)

## File Structure

```
imam_jawad_video_system/
├── imam_jawad_video_creator.py      # Main video creation script
├── imam_jawad_video_content.py      # Content and text management
├── enhanced_arabic_renderer.py      # Arabic text rendering engine
├── test_imam_jawad_video.py         # Test suite
├── requirements.txt                 # Python dependencies
├── IMAM_JAWAD_VIDEO_README.md      # This file
└── imam_jawad_video_output/         # Generated output directory
    ├── frames/                      # Individual PNG frames
    └── *.gif                        # Generated GIF videos
```

## Video Content

The video includes 10 carefully crafted scenes:

1. **Opening**: Bismillah (In the Name of Allah)
2. **Announcement**: Martyrdom anniversary announcement
3. **Imam's Name**: Full name and title with reverence
4. **Date**: 29th Dhul Qi'dah 220 AH
5. **Knowledge**: Highlighting the Imam's scholarly virtues
6. **Piety**: Emphasizing spiritual qualities
7. **Hadith**: Authentic saying about the Imam
8. **Condolence**: Traditional Shia condolence phrase
9. **Prayer**: Salawat (blessings) upon the Prophet and his family
10. **Closing**: Final remembrance and prayer

## Customization

### Content Modification
Edit `imam_jawad_video_content.py` to:
- Modify Arabic text content
- Adjust scene durations
- Change visual styles
- Add new scenes

### Visual Customization
Edit `imam_jawad_video_creator.py` to:
- Modify color schemes
- Adjust font sizes
- Change animation effects
- Alter layout positions

### Style Options
Available visual styles:
- `elegant` - Black/Gold classic design
- `formal` - Dark gray professional look
- `highlighted` - Emphasized golden text
- `memorial` - Subdued mourning colors
- `wisdom` - Blue tones for knowledge scenes
- `spiritual` - Green tones for piety
- `hadith` - Warm tones for religious quotes
- `condolence` - Traditional mourning design
- `prayer` - Purple tones for supplications
- `closing` - Final scene styling

## Output Formats

### GIF Format
- **Pros**: Easy to share, works everywhere, smaller file size
- **Cons**: Limited quality, larger file size for long videos
- **Best for**: Social media posts, quick sharing

### PNG Frames + MP4
- **Pros**: Highest quality, professional output, smaller file size
- **Cons**: Requires FFmpeg for conversion
- **Best for**: Professional use, YouTube, high-quality sharing

### MP4 Creation Commands
After generating PNG frames, use these FFmpeg commands:

```bash
# Basic MP4 creation
ffmpeg -framerate 8 -i frames/frame_%04d.png -c:v libx264 -pix_fmt yuv420p -crf 23 output.mp4

# Optimized for social media
ffmpeg -framerate 8 -i frames/frame_%04d.png -c:v libx264 -pix_fmt yuv420p -crf 20 -preset slow -movflags +faststart output_optimized.mp4
```

## Cultural and Religious Notes

### Shia Islamic Authenticity
- Content based on authentic Shia sources
- Proper Arabic terminology and phrases
- Respectful mourning traditions
- Historical accuracy from reliable references

### Sources Referenced
- Bihar al-Anwar by Allama Majlisi
- Al-Kafi by Sheikh Kulayni
- Manaqib Aal Abi Talib by Ibn Shahr Ashub
- Uyun Akhbar al-Ridha by Sheikh Saduq

### Respectful Usage
- Intended for educational and commemorative purposes
- Maintains dignity and reverence for Ahl al-Bayt
- Follows Shia mourning customs and etiquette

## Troubleshooting

### Common Issues

1. **Arabic text not displaying properly**
   - Install Arabic fonts (Amiri, Scheherazade, Noto Sans Arabic)
   - Verify arabic-reshaper and python-bidi are installed

2. **Video creation fails**
   - Run the test suite first: `python test_imam_jawad_video.py`
   - Check all dependencies are installed

3. **Large file sizes**
   - Use PNG frames + MP4 conversion for better compression
   - Adjust frame rate and quality settings

4. **Font issues**
   - The system automatically falls back to available fonts
   - Install specialized Arabic fonts for best results

### Getting Help
If you encounter issues:
1. Run the test suite to identify problems
2. Check the error messages for specific issues
3. Verify all dependencies are properly installed
4. Ensure you have sufficient disk space for output

## License and Usage

This tool is created for educational and commemorative purposes to honor the memory of Imam Muhammad al-Jawad (peace be upon him). Please use respectfully and in accordance with Islamic principles.

---

**May Allah bless the memory of Imam al-Jawad (AS) and grant peace to his noble soul.**

*Created with respect and reverence for Ahl al-Bayt (peace be upon them)*
