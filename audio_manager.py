#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Islamic Audio Manager for Professional Condolence Videos
Handles Islamic background audio, nasheeds, and Quranic recitation with proper timing.
"""

import os
import sys
import json
from datetime import datetime

# Try to import audio libraries
try:
    from pydub import AudioSegment
    from pydub.generators import Sine
    AUDIO_SUPPORT = True
    print("✓ Audio processing libraries loaded successfully")
except ImportError as e:
    AUDIO_SUPPORT = False
    print(f"⚠️  Audio libraries not available: {e}")

class IslamicAudioManager:
    """Professional Islamic audio manager for condolence videos."""

    def __init__(self):
        self.audio_dir = "audio_assets"
        self.output_dir = "audio_output"
        self._create_directories()
        self.audio_library = self._prepare_audio_library()

    def _create_directories(self):
        """Create necessary directories for audio assets."""
        for directory in [self.audio_dir, self.output_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"✓ Created directory: {directory}")

    def _prepare_audio_library(self):
        """Prepare library of appropriate Islamic audio content."""
        return {
            'background_nasheeds': {
                'gentle_remembrance': {
                    'description': 'Gentle nasheed for remembrance',
                    'mood': 'peaceful',
                    'duration': 60,
                    'volume': 0.3
                },
                'mourning_melody': {
                    'description': 'Solemn melody for mourning',
                    'mood': 'solemn',
                    'duration': 90,
                    'volume': 0.25
                },
                'spiritual_reflection': {
                    'description': 'Spiritual reflection music',
                    'mood': 'contemplative',
                    'duration': 120,
                    'volume': 0.2
                }
            },
            'quranic_recitation': {
                'surah_fatiha': {
                    'description': 'Recitation of Al-Fatiha',
                    'reciter': 'Professional Qari',
                    'duration': 45,
                    'volume': 0.4
                },
                'ayat_knowledge': {
                    'description': 'Verses about knowledge',
                    'reference': 'Quran 20:114',
                    'duration': 30,
                    'volume': 0.35
                }
            },
            'ambient_sounds': {
                'peaceful_silence': {
                    'description': 'Peaceful ambient background',
                    'mood': 'serene',
                    'duration': 180,
                    'volume': 0.1
                }
            }
        }

    def create_gentle_background_tone(self, duration_seconds=60, frequency=220):
        """Create a gentle, respectful background tone."""
        if not AUDIO_SUPPORT:
            print("⚠️  Audio support not available")
            return None

        try:
            # Create a gentle sine wave
            tone = Sine(frequency).to_audio_segment(duration=duration_seconds * 1000)
            
            # Apply fade in and fade out for smooth transitions
            tone = tone.fade_in(2000).fade_out(2000)
            
            # Reduce volume for background use
            tone = tone - 20  # Reduce by 20dB
            
            return tone

        except Exception as e:
            print(f"❌ Error creating background tone: {e}")
            return None

    def create_islamic_ambient_audio(self, duration_seconds=60, style='peaceful'):
        """Create appropriate Islamic ambient audio."""
        if not AUDIO_SUPPORT:
            return None

        try:
            if style == 'peaceful':
                # Create peaceful ambient sound
                base_freq = 220  # A3 note
                harmony_freq = 330  # E4 note
                
                # Create base tone
                base_tone = Sine(base_freq).to_audio_segment(duration=duration_seconds * 1000)
                harmony_tone = Sine(harmony_freq).to_audio_segment(duration=duration_seconds * 1000)
                
                # Mix tones with different volumes
                base_tone = base_tone - 25  # Quieter base
                harmony_tone = harmony_tone - 30  # Even quieter harmony
                
                # Combine tones
                ambient = base_tone.overlay(harmony_tone)
                
            elif style == 'solemn':
                # Create solemn, respectful tone
                freq = 196  # G3 note (lower, more solemn)
                tone = Sine(freq).to_audio_segment(duration=duration_seconds * 1000)
                tone = tone - 22  # Quiet and respectful
                ambient = tone
                
            else:  # contemplative
                # Create contemplative atmosphere
                freq1 = 174  # Lower frequency for contemplation
                freq2 = 261  # C4 note
                
                tone1 = Sine(freq1).to_audio_segment(duration=duration_seconds * 1000)
                tone2 = Sine(freq2).to_audio_segment(duration=duration_seconds * 1000)
                
                tone1 = tone1 - 28
                tone2 = tone2 - 32
                
                ambient = tone1.overlay(tone2)

            # Apply gentle fade effects
            ambient = ambient.fade_in(3000).fade_out(3000)
            
            return ambient

        except Exception as e:
            print(f"❌ Error creating ambient audio: {e}")
            return None

    def create_timed_audio_track(self, video_duration, scene_timings, audio_style='peaceful'):
        """Create a timed audio track that matches video scenes."""
        if not AUDIO_SUPPORT:
            return None

        try:
            # Create base ambient audio
            ambient = self.create_islamic_ambient_audio(video_duration, audio_style)
            if not ambient:
                return None

            # Add subtle volume variations for different scenes
            final_audio = ambient
            
            # Apply scene-specific audio adjustments
            for scene_name, timing in scene_timings.items():
                start_time = timing['start'] * 1000  # Convert to milliseconds
                end_time = timing['end'] * 1000
                
                # Adjust volume based on scene type
                if 'quranic' in scene_name.lower():
                    # Slightly increase volume for Quranic verses
                    scene_audio = final_audio[start_time:end_time] + 3
                elif 'condolence' in scene_name.lower():
                    # Softer for condolence messages
                    scene_audio = final_audio[start_time:end_time] - 2
                elif 'prayer' in scene_name.lower():
                    # Reverent volume for prayers
                    scene_audio = final_audio[start_time:end_time] + 1
                else:
                    # Standard volume
                    scene_audio = final_audio[start_time:end_time]
                
                # Replace the segment
                final_audio = final_audio[:start_time] + scene_audio + final_audio[end_time:]

            return final_audio

        except Exception as e:
            print(f"❌ Error creating timed audio track: {e}")
            return None

    def save_audio_track(self, audio, filename, format='mp3'):
        """Save audio track to file."""
        if not audio or not AUDIO_SUPPORT:
            return None

        try:
            filepath = os.path.join(self.output_dir, f"{filename}.{format}")
            audio.export(filepath, format=format)
            
            file_size = os.path.getsize(filepath) / (1024 * 1024)
            print(f"✓ Audio saved: {filename}.{format} ({file_size:.1f} MB)")
            
            return filepath

        except Exception as e:
            print(f"❌ Error saving audio: {e}")
            return None

    def get_audio_recommendations(self, video_style, duration):
        """Get audio recommendations based on video style and duration."""
        recommendations = {
            'peaceful_remembrance': {
                'style': 'peaceful',
                'description': 'Gentle ambient sound for peaceful remembrance',
                'suitable_for': ['condolence', 'prayer', 'closing']
            },
            'solemn_mourning': {
                'style': 'solemn',
                'description': 'Respectful solemn tone for mourning',
                'suitable_for': ['martyrdom_dates', 'condolence_phrase']
            },
            'contemplative_wisdom': {
                'style': 'contemplative',
                'description': 'Contemplative atmosphere for wisdom and knowledge',
                'suitable_for': ['quranic_verses', 'hadith', 'virtues']
            }
        }

        if duration <= 30:
            return recommendations['peaceful_remembrance']
        elif duration <= 60:
            return recommendations['solemn_mourning']
        else:
            return recommendations['contemplative_wisdom']

def test_audio_manager():
    """Test the Islamic audio manager."""
    print("Testing Islamic Audio Manager...")
    print("=" * 40)

    manager = IslamicAudioManager()
    
    # Test audio creation
    if AUDIO_SUPPORT:
        print("✓ Creating test ambient audio...")
        test_audio = manager.create_islamic_ambient_audio(10, 'peaceful')
        
        if test_audio:
            print(f"✓ Audio created: {len(test_audio)}ms duration")
            
            # Save test audio
            test_file = manager.save_audio_track(test_audio, "test_ambient")
            if test_file:
                print(f"✓ Test audio saved: {test_file}")
        else:
            print("❌ Failed to create test audio")
    else:
        print("⚠️  Audio support not available - install pydub for full functionality")

    # Test recommendations
    recommendation = manager.get_audio_recommendations('peaceful', 45)
    print(f"✓ Audio recommendation: {recommendation['description']}")

    print("✓ Audio manager test completed")

if __name__ == "__main__":
    test_audio_manager()
