import numpy as np
import matplotlib.pyplot as plt

# Define the composition range (wt% Ni)
X = np.linspace(0, 100, 500)

# Liquidus curve equation (quadratic model)
T_liquidus = -0.021 * X**2 + 5.8 * X + 1085

# Solidus curve equation (quadratic model)
T_solidus = 0.0138 * X**2 + 2.3197 * X + 1085

# Create the plot
plt.figure(figsize=(10, 6))
plt.plot(X, T_liquidus, label='Liquidus Line', color='blue')
plt.plot(X, T_solidus, label='Solidus Line', color='red')

# Highlight key points and regions
alloy_Ni = 70  # Composition of the alloy (70 wt% Ni)

# Temperatures from the problem
T_solidus_alloy = 1315  # Temperature where first liquid forms
T_liquidus_alloy = 1345  # Temperature where melting completes

# Find liquid composition at T_solidus_alloy (1315°C)
# Solve T_liquidus = 1315 for X
# -0.021X² + 5.8X + 1085 = 1315 → -0.021X² +5.8X -230 = 0
a, b, c = -0.021, 5.8, -230
discriminant = b**2 - 4*a*c
X_liquid_1315 = (-b + np.sqrt(discriminant)) / (2*a)

# Find solid composition at T_liquidus_alloy (1345°C)
# Solve T_solidus = 1345 for X
# 0.0138X² +2.3197X +1085 =1345 →0.0138X² +2.3197X -260=0
a, b, c = 0.0138, 2.3197, -260
discriminant = b**2 - 4*a*c
X_solid_1345 = (-b + np.sqrt(discriminant)) / (2*a)

# Plot vertical line for the alloy composition
plt.axvline(x=alloy_Ni, color='gray', linestyle='--', linewidth=1)
plt.text(alloy_Ni, 1085, f'{alloy_Ni}% Ni', verticalalignment='bottom')

# Plot horizontal lines for key temperatures
plt.axhline(y=T_solidus_alloy, color='green', linestyle=':', linewidth=1)
plt.axhline(y=T_liquidus_alloy, color='orange', linestyle=':', linewidth=1)

# Annotate phase regions
plt.fill_between(X, T_solidus, T_liquidus, color='lightgray', alpha=0.3, label='L + S')
plt.fill_between(X, T_liquidus, 1500, color='lightblue', alpha=0.2, label='Liquid')
plt.fill_between(X, 0, T_solidus, color='salmon', alpha=0.2, label='Solid')

# Mark key points
plt.scatter(X_liquid_1315, T_solidus_alloy, color='black', zorder=5)
plt.text(X_liquid_1315, T_solidus_alloy-10, f'L: {X_liquid_1315:.1f}% Ni',
         horizontalalignment='right')

plt.scatter(alloy_Ni, T_solidus_alloy, color='black', zorder=5)
plt.text(alloy_Ni, T_solidus_alloy+10, f'S: {alloy_Ni}% Ni',
         horizontalalignment='center')

plt.scatter(X_solid_1345, T_liquidus_alloy, color='black', zorder=5)
plt.text(X_solid_1345, T_liquidus_alloy+10, f'S: {X_solid_1345:.1f}% Ni',
         horizontalalignment='left')

plt.scatter(alloy_Ni, T_liquidus_alloy, color='black', zorder=5)
plt.text(alloy_Ni, T_liquidus_alloy-10, f'L: {alloy_Ni}% Ni',
         horizontalalignment='center')

# Labels and title
plt.xlabel('Composition (wt% Ni)')
plt.ylabel('Temperature (°C)')
plt.title('Cu-Ni Phase Diagram (Approximation)')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.ylim(1000, 1500)

plt.show()