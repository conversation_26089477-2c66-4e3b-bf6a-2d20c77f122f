import json
import os
from PyQt5.QtCore import QPointF, QRectF, Qt
from PyQt5.QtGui import QColor

class ProjectItem:
    """Class representing an item in the project"""
    
    def __init__(self, item_type, properties=None):
        self.item_type = item_type
        self.properties = properties or {}
        
    def to_dict(self):
        """Convert the item to a dictionary for serialization"""
        data = {
            'type': self.item_type,
            'properties': {}
        }
        
        # Convert properties to serializable format
        for key, value in self.properties.items():
            if isinstance(value, QPointF):
                data['properties'][key] = {'x': value.x(), 'y': value.y()}
            elif isinstance(value, QRectF):
                data['properties'][key] = {
                    'x': value.x(),
                    'y': value.y(),
                    'width': value.width(),
                    'height': value.height()
                }
            elif isinstance(value, QColor):
                data['properties'][key] = {
                    'r': value.red(),
                    'g': value.green(),
                    'b': value.blue(),
                    'a': value.alpha()
                }
            elif isinstance(value, Qt.PenStyle):
                data['properties'][key] = int(value)
            else:
                data['properties'][key] = value
                
        return data
        
    @classmethod
    def from_dict(cls, data):
        """Create an item from a dictionary"""
        item = cls(data['type'])
        
        for key, value in data['properties'].items():
            if isinstance(value, dict):
                if 'x' in value and 'y' in value and len(value) == 2:
                    item.properties[key] = QPointF(value['x'], value['y'])
                elif 'x' in value and 'y' in value and 'width' in value and 'height' in value:
                    item.properties[key] = QRectF(value['x'], value['y'], value['width'], value['height'])
                elif 'r' in value and 'g' in value and 'b' in value:
                    alpha = value.get('a', 255)
                    item.properties[key] = QColor(value['r'], value['g'], value['b'], alpha)
                else:
                    item.properties[key] = value
            elif isinstance(value, int) and key == 'line_style':
                item.properties[key] = Qt.PenStyle(value)
            else:
                item.properties[key] = value
                
        return item

class Project:
    """Class representing a project"""
    
    def __init__(self):
        self.name = "Untitled Project"
        self.file_path = None
        self.items = []  # Drawing items
        self.views = {}  # Different views (front, top, side, etc.)
        self.layers_data = None  # Layer data
        self.settings = {}  # Project settings
        self.metadata = {}  # Project metadata
        self.modified = False
        
    def add_item(self, item, view=None):
        """Add an item to the project"""
        if view is None:
            self.items.append(item)
        else:
            if view not in self.views:
                self.views[view] = []
            self.views[view].append(item)
            
        self.modified = True
        
    def remove_item(self, item, view=None):
        """Remove an item from the project"""
        if view is None:
            if item in self.items:
                self.items.remove(item)
                self.modified = True
        else:
            if view in self.views and item in self.views[view]:
                self.views[view].remove(item)
                self.modified = True
                
    def get_items(self, view=None):
        """Get all items in the project or a specific view"""
        if view is None:
            return self.items
        else:
            return self.views.get(view, [])
            
    def clear(self, view=None):
        """Clear all items from the project or a specific view"""
        if view is None:
            self.items = []
            self.views = {}
        else:
            self.views[view] = []
            
        self.modified = True
        
    def save(self):
        """Save the project to a file"""
        if not self.file_path:
            raise ValueError("File path not set")
            
        # Create project data
        data = {
            'name': self.name,
            'items': [item.to_dict() for item in self.items],
            'views': {},
            'layers': self.layers_data,
            'settings': self.settings,
            'metadata': self.metadata
        }
        
        # Add view data
        for view_name, view_items in self.views.items():
            data['views'][view_name] = [item.to_dict() for item in view_items]
            
        # Write to file
        with open(self.file_path, 'w') as f:
            json.dump(data, f, indent=2)
            
        self.modified = False
        
    def load(self, file_path):
        """Load the project from a file"""
        with open(file_path, 'r') as f:
            data = json.load(f)
            
        self.file_path = file_path
        self.name = data.get('name', os.path.basename(file_path))
        
        # Load items
        self.items = []
        for item_data in data.get('items', []):
            self.items.append(ProjectItem.from_dict(item_data))
            
        # Load views
        self.views = {}
        for view_name, view_items in data.get('views', {}).items():
            self.views[view_name] = []
            for item_data in view_items:
                self.views[view_name].append(ProjectItem.from_dict(item_data))
                
        # Load other data
        self.layers_data = data.get('layers', None)
        self.settings = data.get('settings', {})
        self.metadata = data.get('metadata', {})
        
        self.modified = False
        
    def is_modified(self):
        """Check if the project has been modified"""
        return self.modified
        
    def get_name(self):
        """Get the project name"""
        return self.name
        
    def set_name(self, name):
        """Set the project name"""
        self.name = name
        self.modified = True
        
    def get_file_path(self):
        """Get the project file path"""
        return self.file_path
        
    def set_file_path(self, file_path):
        """Set the project file path"""
        self.file_path = file_path
        
    def set_setting(self, key, value):
        """Set a project setting"""
        self.settings[key] = value
        self.modified = True
        
    def get_setting(self, key, default=None):
        """Get a project setting"""
        return self.settings.get(key, default)
        
    def set_metadata(self, key, value):
        """Set project metadata"""
        self.metadata[key] = value
        self.modified = True
        
    def get_metadata(self, key, default=None):
        """Get project metadata"""
        return self.metadata.get(key, default) 