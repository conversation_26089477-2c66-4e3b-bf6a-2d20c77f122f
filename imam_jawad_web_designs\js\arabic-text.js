/**
 * Enhanced Arabic Text Handling for <PERSON> Web Designs
 * Provides additional Arabic text processing and web-specific enhancements
 */

class ArabicTextEnhancer {
    constructor() {
        this.isRTL = true;
        this.arabicFonts = [
            '<PERSON>i',
            'Scheherazade New', 
            'Noto Sans Arabic',
            'Cairo',
            '<PERSON><PERSON><PERSON>',
            'Arial'
        ];
        
        this.init();
    }

    init() {
        // Ensure proper RTL direction
        this.ensureRTLDirection();
        
        // Enhance Arabic text rendering
        this.enhanceArabicText();
        
        // Add font loading detection
        this.detectFontLoading();
        
        // Add accessibility features
        this.addAccessibilityFeatures();
        
        // Add performance optimizations
        this.optimizePerformance();
    }

    ensureRTLDirection() {
        // Set document direction
        document.documentElement.dir = 'rtl';
        document.documentElement.lang = 'ar';
        
        // Ensure all Arabic text elements have proper direction
        const arabicElements = document.querySelectorAll('.arabic-text');
        arabicElements.forEach(element => {
            element.dir = 'rtl';
            element.style.unicodeBidi = 'bidi-override';
            element.style.textAlign = 'center';
        });
    }

    enhanceArabicText() {
        const arabicElements = document.querySelectorAll('.arabic-text');
        
        arabicElements.forEach(element => {
            // Add Arabic-specific CSS properties
            element.style.fontFeatureSettings = '"liga" 1, "calt" 1, "kern" 1';
            element.style.webkitFontSmoothing = 'antialiased';
            element.style.mozOsxFontSmoothing = 'grayscale';
            element.style.textRendering = 'optimizeLegibility';
            
            // Ensure proper word spacing for Arabic
            element.style.wordSpacing = '0.1em';
            
            // Add subtle letter spacing for better readability
            element.style.letterSpacing = '0.02em';
        });
    }

    detectFontLoading() {
        // Check if fonts are loaded
        if ('fonts' in document) {
            document.fonts.ready.then(() => {
                console.log('Arabic fonts loaded successfully');
                this.onFontsLoaded();
            });
            
            // Listen for font loading events
            document.fonts.addEventListener('loadingdone', (event) => {
                console.log('Font loading completed:', event);
                this.onFontsLoaded();
            });
        }
    }

    onFontsLoaded() {
        // Re-measure and adjust text if needed
        const arabicElements = document.querySelectorAll('.arabic-text');
        arabicElements.forEach(element => {
            // Trigger reflow to ensure proper rendering
            element.style.display = 'none';
            element.offsetHeight; // Trigger reflow
            element.style.display = '';
        });
    }

    addAccessibilityFeatures() {
        // Add ARIA labels for screen readers
        const arabicElements = document.querySelectorAll('.arabic-text');
        arabicElements.forEach(element => {
            if (!element.getAttribute('aria-label')) {
                element.setAttribute('aria-label', element.textContent);
            }
            element.setAttribute('lang', 'ar');
        });

        // Add keyboard navigation hints
        const designContainer = document.querySelector('.design-container');
        if (designContainer) {
            designContainer.setAttribute('role', 'img');
            designContainer.setAttribute('aria-label', 'تصميم العزاء للإمام الجواد عليه السلام');
        }

        // Add focus indicators for keyboard navigation
        this.addFocusIndicators();
    }

    addFocusIndicators() {
        const style = document.createElement('style');
        style.textContent = `
            .navigation a:focus {
                outline: 2px solid #FFD700;
                outline-offset: 2px;
                background-color: rgba(255, 215, 0, 0.2);
            }
            
            .design-container:focus {
                outline: 3px solid #FFD700;
                outline-offset: 5px;
            }
        `;
        document.head.appendChild(style);
    }

    optimizePerformance() {
        // Use Intersection Observer for animations
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, { threshold: 0.1 });

            const textElements = document.querySelectorAll('.text-element');
            textElements.forEach(element => {
                observer.observe(element);
            });
        }

        // Preload critical resources
        this.preloadCriticalResources();
    }

    preloadCriticalResources() {
        // Preload Arabic fonts
        const fontPreloads = [
            'https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap',
            'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap'
        ];

        fontPreloads.forEach(fontUrl => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = fontUrl;
            document.head.appendChild(link);
        });
    }

    // Text measurement utilities
    measureArabicText(text, font) {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        context.font = font;
        context.direction = 'rtl';
        return context.measureText(text);
    }

    // Text formatting utilities
    formatArabicText(text) {
        // Remove extra whitespace and normalize
        return text.trim().replace(/\s+/g, ' ');
    }

    // Responsive text sizing
    adjustTextSizeForViewport() {
        const textElements = document.querySelectorAll('.text-element');
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const minDimension = Math.min(viewportWidth, viewportHeight);

        textElements.forEach(element => {
            const currentFontSize = parseFloat(getComputedStyle(element).fontSize);
            const scaleFactor = minDimension / 600; // Base size for 600px viewport
            const newFontSize = Math.max(12, currentFontSize * scaleFactor);
            
            if (minDimension < 400) {
                element.style.fontSize = `${newFontSize * 0.9}px`;
            }
        });
    }

    // Export functionality
    exportAsImage() {
        const designContainer = document.querySelector('.design-container');
        if (!designContainer) return;

        // Use html2canvas if available
        if (typeof html2canvas !== 'undefined') {
            html2canvas(designContainer, {
                backgroundColor: null,
                scale: 2,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = 'imam-jawad-condolence-design.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        } else {
            console.warn('html2canvas library not available for image export');
        }
    }

    // Print optimization
    optimizeForPrint() {
        const printStyles = `
            @media print {
                .design-container {
                    width: 100% !important;
                    height: 100vh !important;
                    max-width: none !important;
                    max-height: none !important;
                    margin: 0 !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;
                }
                
                .navigation {
                    display: none !important;
                }
                
                .text-element {
                    animation: none !important;
                    opacity: 1 !important;
                    transform: none !important;
                }
                
                body {
                    background: white !important;
                }
            }
        `;

        const style = document.createElement('style');
        style.textContent = printStyles;
        document.head.appendChild(style);
    }

    // Social sharing
    shareDesign() {
        const title = 'تصميم العزاء للإمام الجواد عليه السلام';
        const text = 'بمناسبة ذكرى استشهاد الإمام محمد الجواد عليه السلام - التاسع والعشرون من ذي القعدة';
        const url = window.location.href;

        if (navigator.share) {
            navigator.share({ title, text, url })
                .then(() => console.log('Shared successfully'))
                .catch(err => console.log('Error sharing:', err));
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(url).then(() => {
                this.showNotification('تم نسخ الرابط إلى الحافظة');
            });
        }
    }

    // Notification system
    showNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-family: 'Tajawal', Arial, sans-serif;
            direction: rtl;
            animation: slideInRight 0.3s ease-out;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const arabicEnhancer = new ArabicTextEnhancer();
    
    // Make it globally available
    window.arabicEnhancer = arabicEnhancer;
    
    // Add resize listener for responsive adjustments
    window.addEventListener('resize', () => {
        arabicEnhancer.adjustTextSizeForViewport();
    });
    
    // Add print listener
    window.addEventListener('beforeprint', () => {
        arabicEnhancer.optimizeForPrint();
    });
});

// Add CSS animations for notifications
const notificationStyles = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;

const style = document.createElement('style');
style.textContent = notificationStyles;
document.head.appendChild(style);
