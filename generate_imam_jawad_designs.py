#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os

def create_imam_jawad_design(background_color=(0, 0, 0), primary_color=(255, 215, 0), design_name="imam_jawad_condolence"):
    """
    Create a condolence design for <PERSON> (AS) martyrdom.
    """
    size = 1080  # 1:1 aspect ratio
    secondary_color = (255, 255, 255)  # White
    accent_color = (139, 69, 19)  # Bronze
    
    # Create image
    img = Image.new('RGB', (size, size), background_color)
    draw = ImageDraw.Draw(img)
    
    # Font setup
    font_paths = [
        "C:\\Windows\\Fonts\\arial.ttf",
        "C:\\Windows\\Fonts\\calibri.ttf", 
        "C:\\Windows\\Fonts\\tahoma.ttf"
    ]
    
    # Try to load fonts
    title_font = main_font = subtitle_font = watermark_font = None
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                title_font = ImageFont.truetype(font_path, 48)
                main_font = ImageFont.truetype(font_path, 36)
                subtitle_font = ImageFont.truetype(font_path, 28)
                watermark_font = ImageFont.truetype(font_path, 20)
                break
            except Exception:
                continue
    
    # Fallback to default font
    if title_font is None:
        title_font = main_font = subtitle_font = watermark_font = ImageFont.load_default()
        print("Warning: Using default font. Arabic may not display properly.")
    
    # Arabic text content (authentic Shia Islamic phrases)
    texts = {
        'bismillah': 'بسم الله الرحمن الرحيم',
        'condolence_intro': 'بمناسبة ذكرى استشهاد',
        'imam_name': 'الإمام محمد الجواد عليه السلام',
        'date': 'التاسع والعشرون من ذي القعدة',
        'condolence': 'أحيا الله ذكراكم وأعظم أجوركم',
        'salawat': 'اللهم صل على محمد وآل محمد',
        'watermark': 'AliToucan'
    }
    
    # Layout positions
    y_positions = {
        'bismillah': 120,
        'condolence_intro': 220,
        'imam_name': 300,
        'date': 420,
        'condolence': 520,
        'salawat': 620,
        'line': 680
    }
    
    # Helper function for centered text
    def draw_centered_text(text, y_pos, font, color):
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        x_pos = (size - text_width) // 2
        draw.text((x_pos, y_pos), text, font=font, fill=color)
    
    # Draw decorative borders
    border_width = 8
    draw.rectangle([border_width, border_width, size-border_width, size-border_width], 
                  outline=primary_color, width=3)
    
    inner_margin = 60
    draw.rectangle([inner_margin, inner_margin, size-inner_margin, size-inner_margin], 
                  outline=accent_color, width=2)
    
    # Draw text elements
    draw_centered_text(texts['bismillah'], y_positions['bismillah'], subtitle_font, secondary_color)
    draw_centered_text(texts['condolence_intro'], y_positions['condolence_intro'], main_font, primary_color)
    draw_centered_text(texts['imam_name'], y_positions['imam_name'], title_font, primary_color)
    draw_centered_text(texts['date'], y_positions['date'], subtitle_font, secondary_color)
    draw_centered_text(texts['condolence'], y_positions['condolence'], main_font, primary_color)
    draw_centered_text(texts['salawat'], y_positions['salawat'], subtitle_font, secondary_color)
    
    # Draw decorative line
    line_y = y_positions['line']
    line_start = size // 4
    line_end = 3 * size // 4
    draw.line([(line_start, line_y), (line_end, line_y)], fill=accent_color, width=3)
    
    # Add corner decorations
    corner_offset = 100
    circle_radius = 15
    corners = [
        (corner_offset, corner_offset),
        (size - corner_offset, corner_offset),
        (corner_offset, size - corner_offset),
        (size - corner_offset, size - corner_offset)
    ]
    
    for corner_x, corner_y in corners:
        draw.ellipse([corner_x - circle_radius, corner_y - circle_radius,
                     corner_x + circle_radius, corner_y + circle_radius],
                    outline=accent_color, width=2)
    
    # Add watermark
    watermark_x = size - 150
    watermark_y = size - 40
    draw.text((watermark_x, watermark_y), texts['watermark'], 
              font=watermark_font, fill=(128, 128, 128))
    
    return img

def save_design(img, filename):
    """
    Save design in multiple formats.
    """
    output_dir = "imam_jawad_designs"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    saved_files = []
    
    # Save as PNG
    png_path = os.path.join(output_dir, f"{filename}.png")
    img.save(png_path, "PNG")
    saved_files.append(png_path)
    
    # Save as JPG
    jpg_path = os.path.join(output_dir, f"{filename}.jpg")
    rgb_img = Image.new('RGB', img.size, (0, 0, 0))
    rgb_img.paste(img)
    rgb_img.save(jpg_path, "JPEG", quality=95)
    saved_files.append(jpg_path)
    
    return saved_files

def main():
    """
    Generate multiple design variations automatically.
    """
    print("Generating Imam al-Jawad (AS) Condolence Designs")
    print("=" * 50)
    
    # Design variations
    designs = [
        {
            'name': 'imam_jawad_classic',
            'bg_color': (0, 0, 0),
            'primary_color': (255, 215, 0),
            'description': 'Classic Black & Gold'
        },
        {
            'name': 'imam_jawad_elegant',
            'bg_color': (25, 25, 25),
            'primary_color': (200, 200, 200),
            'description': 'Elegant Dark Gray & Silver'
        },
        {
            'name': 'imam_jawad_traditional',
            'bg_color': (0, 0, 0),
            'primary_color': (255, 255, 255),
            'description': 'Traditional Black & White'
        },
        {
            'name': 'imam_jawad_royal',
            'bg_color': (20, 20, 40),
            'primary_color': (255, 215, 0),
            'description': 'Royal Navy & Gold'
        }
    ]
    
    all_files = []
    
    for design in designs:
        print(f"\nCreating {design['description']}...")
        
        img = create_imam_jawad_design(
            background_color=design['bg_color'],
            primary_color=design['primary_color'],
            design_name=design['name']
        )
        
        saved_files = save_design(img, design['name'])
        all_files.extend(saved_files)
        
        for file_path in saved_files:
            file_size = os.path.getsize(file_path) / 1024
            print(f"  ✓ {file_path} ({file_size:.1f} KB)")
    
    print("\n" + "=" * 50)
    print("All Designs Created Successfully!")
    print(f"Total files: {len(all_files)}")
    
    print("\nDesign Features:")
    print("  ✓ 1:1 aspect ratio (1080x1080)")
    print("  ✓ Authentic Arabic condolence text")
    print("  ✓ Shia Islamic terminology")
    print("  ✓ Multiple color variations")
    print("  ✓ Islamic decorative elements")
    print("  ✓ AliToucan branding")
    print("  ✓ High resolution for social media")
    
    print(f"\nFiles saved in: {os.path.abspath('imam_jawad_designs')}")
    
    # Try to open first design
    try:
        if all_files:
            first_design = Image.open(all_files[0])
            first_design.show()
            print(f"\n  ✓ Preview opened: {all_files[0]}")
    except Exception:
        print("\n  Note: Preview not available")

if __name__ == "__main__":
    main()
