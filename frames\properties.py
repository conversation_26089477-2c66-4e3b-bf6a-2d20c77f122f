from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                           QSpinBox, QDoubleSpinBox, QComboBox, QColorDialog,
                           QPushButton, QLineEdit, QGroupBox, QScrollArea,
                           QCheckBox, QFormLayout)
from PyQt5.QtGui import QColor, QFont, QPalette
from PyQt5.QtCore import Qt, pyqtSignal

class ColorButton(QPushButton):
    """Custom button that shows a color and opens a color dialog when clicked"""
    
    colorChanged = pyqtSignal(QColor)
    
    def __init__(self, color=None, parent=None):
        super().__init__(parent)
        self.color = color or QColor(0, 0, 0)
        self.setMinimumSize(30, 20)
        self.clicked.connect(self.open_color_dialog)
        self.update_color()
        
    def update_color(self):
        """Update the button's background to show the current color"""
        self.setStyleSheet(f"background-color: rgba({self.color.red()}, {self.color.green()}, {self.color.blue()}, {self.color.alpha()}); border: 1px solid black;")
        
    def open_color_dialog(self):
        """Open a color dialog and update the color if accepted"""
        color = QColorDialog.getColor(self.color, self.parent(), options=QColorDialog.ShowAlphaChannel)
        if color.isValid():
            self.color = color
            self.update_color()
            self.colorChanged.emit(self.color)
            
    def set_color(self, color):
        """Set the button's color"""
        if color != self.color:
            self.color = color
            self.update_color()
            
    def get_color(self):
        """Get the button's color"""
        return self.color

class PropertyGroup(QGroupBox):
    """Base class for property groups in the properties panel"""
    
    propertyChanged = pyqtSignal(str, object)
    
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the UI for this property group"""
        self.layout = QFormLayout()
        self.setLayout(self.layout)
        
    def update_for_item(self, item):
        """Update the properties panel for the given item"""
        pass
        
    def connect_signals(self):
        """Connect signals for property changes"""
        pass
        
    def reset(self):
        """Reset all property controls"""
        pass

class GeometryProperties(PropertyGroup):
    """Property group for geometric properties (position, size, etc.)"""
    
    def __init__(self, parent=None):
        super().__init__("Geometry", parent)
        
    def setup_ui(self):
        """Set up the UI for this property group"""
        super().setup_ui()
        
        # Position controls
        self.position_x = QDoubleSpinBox()
        self.position_x.setRange(-10000, 10000)
        self.position_x.setDecimals(2)
        self.position_x.setSingleStep(1)
        
        self.position_y = QDoubleSpinBox()
        self.position_y.setRange(-10000, 10000)
        self.position_y.setDecimals(2)
        self.position_y.setSingleStep(1)
        
        position_layout = QHBoxLayout()
        position_layout.addWidget(self.position_x)
        position_layout.addWidget(self.position_y)
        
        self.layout.addRow("Position (X, Y):", position_layout)
        
        # Size controls (for items that have size)
        self.width = QDoubleSpinBox()
        self.width.setRange(0, 10000)
        self.width.setDecimals(2)
        self.width.setSingleStep(1)
        
        self.height = QDoubleSpinBox()
        self.height.setRange(0, 10000)
        self.height.setDecimals(2)
        self.height.setSingleStep(1)
        
        size_layout = QHBoxLayout()
        size_layout.addWidget(self.width)
        size_layout.addWidget(self.height)
        
        self.layout.addRow("Size (W, H):", size_layout)
        
        # Rotation control
        self.rotation = QDoubleSpinBox()
        self.rotation.setRange(-360, 360)
        self.rotation.setDecimals(1)
        self.rotation.setSingleStep(1)
        
        self.layout.addRow("Rotation:", self.rotation)
        
        self.connect_signals()
        
    def connect_signals(self):
        """Connect signals for property changes"""
        self.position_x.valueChanged.connect(lambda v: self.propertyChanged.emit("position_x", v))
        self.position_y.valueChanged.connect(lambda v: self.propertyChanged.emit("position_y", v))
        self.width.valueChanged.connect(lambda v: self.propertyChanged.emit("width", v))
        self.height.valueChanged.connect(lambda v: self.propertyChanged.emit("height", v))
        self.rotation.valueChanged.connect(lambda v: self.propertyChanged.emit("rotation", v))
        
    def update_for_item(self, item):
        """Update the properties panel for the given item"""
        if not item:
            self.reset()
            return
            
        # Block signals during update
        self.position_x.blockSignals(True)
        self.position_y.blockSignals(True)
        self.width.blockSignals(True)
        self.height.blockSignals(True)
        self.rotation.blockSignals(True)
        
        # Update position
        if hasattr(item, "position"):
            self.position_x.setValue(item.position.x())
            self.position_y.setValue(item.position.y())
        elif hasattr(item, "center"):
            self.position_x.setValue(item.center.x())
            self.position_y.setValue(item.center.y())
        elif hasattr(item, "start_point"):
            self.position_x.setValue(item.start_point.x())
            self.position_y.setValue(item.start_point.y())
            
        # Update size
        if item.item_type == "rectangle":
            rect = item.get_bounding_rect()
            self.width.setValue(rect.width())
            self.height.setValue(rect.height())
        elif item.item_type == "circle":
            self.width.setValue(item.radius * 2)
            self.height.setValue(item.radius * 2)
        elif item.item_type == "line":
            # Calculate length and set as width
            dx = item.end_point.x() - item.start_point.x()
            dy = item.end_point.y() - item.start_point.y()
            length = (dx ** 2 + dy ** 2) ** 0.5
            self.width.setValue(length)
            self.height.setValue(0)
            
        # Update rotation
        rotation = item.get_property("angle", 0)
        self.rotation.setValue(rotation)
        
        # Unblock signals
        self.position_x.blockSignals(False)
        self.position_y.blockSignals(False)
        self.width.blockSignals(False)
        self.height.blockSignals(False)
        self.rotation.blockSignals(False)
        
    def reset(self):
        """Reset all property controls"""
        self.position_x.setValue(0)
        self.position_y.setValue(0)
        self.width.setValue(0)
        self.height.setValue(0)
        self.rotation.setValue(0)

class StrokeProperties(PropertyGroup):
    """Property group for stroke properties (color, width, style)"""
    
    def __init__(self, parent=None):
        super().__init__("Stroke", parent)
        
    def setup_ui(self):
        """Set up the UI for this property group"""
        super().setup_ui()
        
        # Stroke color
        self.stroke_color = ColorButton()
        self.layout.addRow("Color:", self.stroke_color)
        
        # Stroke width
        self.stroke_width = QSpinBox()
        self.stroke_width.setRange(1, 20)
        self.stroke_width.setSingleStep(1)
        self.layout.addRow("Width:", self.stroke_width)
        
        # Stroke style
        self.stroke_style = QComboBox()
        self.stroke_style.addItem("Solid", Qt.SolidLine)
        self.stroke_style.addItem("Dash", Qt.DashLine)
        self.stroke_style.addItem("Dot", Qt.DotLine)
        self.stroke_style.addItem("Dash Dot", Qt.DashDotLine)
        self.stroke_style.addItem("Dash Dot Dot", Qt.DashDotDotLine)
        self.layout.addRow("Style:", self.stroke_style)
        
        self.connect_signals()
        
    def connect_signals(self):
        """Connect signals for property changes"""
        self.stroke_color.colorChanged.connect(lambda c: self.propertyChanged.emit("stroke_color", c))
        self.stroke_width.valueChanged.connect(lambda v: self.propertyChanged.emit("stroke_width", v))
        self.stroke_style.currentIndexChanged.connect(
            lambda i: self.propertyChanged.emit("stroke_style", self.stroke_style.itemData(i))
        )
        
    def update_for_item(self, item):
        """Update the properties panel for the given item"""
        if not item:
            self.reset()
            return
            
        # Block signals during update
        self.stroke_color.blockSignals(True)
        self.stroke_width.blockSignals(True)
        self.stroke_style.blockSignals(True)
        
        # Update stroke color
        color = item.get_property("line_color", QColor(0, 0, 0))
        self.stroke_color.set_color(color)
        
        # Update stroke width
        width = item.get_property("line_width", 1)
        self.stroke_width.setValue(width)
        
        # Update stroke style
        style = item.get_property("line_style", Qt.SolidLine)
        index = self.stroke_style.findData(style)
        if index >= 0:
            self.stroke_style.setCurrentIndex(index)
            
        # Unblock signals
        self.stroke_color.blockSignals(False)
        self.stroke_width.blockSignals(False)
        self.stroke_style.blockSignals(False)
        
    def reset(self):
        """Reset all property controls"""
        self.stroke_color.set_color(QColor(0, 0, 0))
        self.stroke_width.setValue(1)
        self.stroke_style.setCurrentIndex(0)  # Solid line

class FillProperties(PropertyGroup):
    """Property group for fill properties (color, pattern)"""
    
    def __init__(self, parent=None):
        super().__init__("Fill", parent)
        
    def setup_ui(self):
        """Set up the UI for this property group"""
        super().setup_ui()
        
        # Fill color
        self.fill_color = ColorButton()
        self.layout.addRow("Color:", self.fill_color)
        
        # Use fill checkbox
        self.use_fill = QCheckBox("Use fill")
        self.layout.addRow("", self.use_fill)
        
        # Fill pattern (would be more options in a real application)
        self.fill_pattern = QComboBox()
        self.fill_pattern.addItem("Solid")
        self.fill_pattern.addItem("Horizontal")
        self.fill_pattern.addItem("Vertical")
        self.fill_pattern.addItem("Cross")
        self.fill_pattern.addItem("Diagonal")
        self.layout.addRow("Pattern:", self.fill_pattern)
        
        self.connect_signals()
        
    def connect_signals(self):
        """Connect signals for property changes"""
        self.fill_color.colorChanged.connect(lambda c: self.propertyChanged.emit("fill_color", c))
        self.use_fill.toggled.connect(lambda c: self.handle_use_fill_changed(c))
        self.fill_pattern.currentIndexChanged.connect(
            lambda i: self.propertyChanged.emit("fill_pattern", self.fill_pattern.currentText())
        )
        
    def handle_use_fill_changed(self, checked):
        """Handle changes to the 'Use fill' checkbox"""
        self.propertyChanged.emit("use_fill", checked)
        
        # Update fill color transparency
        color = self.fill_color.get_color()
        if checked:
            if color.alpha() == 0:
                color.setAlpha(255)
                self.fill_color.set_color(color)
                self.propertyChanged.emit("fill_color", color)
        else:
            if color.alpha() > 0:
                color.setAlpha(0)
                self.fill_color.set_color(color)
                self.propertyChanged.emit("fill_color", color)
        
    def update_for_item(self, item):
        """Update the properties panel for the given item"""
        if not item:
            self.reset()
            return
            
        # Block signals during update
        self.fill_color.blockSignals(True)
        self.use_fill.blockSignals(True)
        self.fill_pattern.blockSignals(True)
        
        # Update fill color
        color = item.get_property("fill_color", QColor(255, 255, 255, 0))
        self.fill_color.set_color(color)
        
        # Update use fill checkbox
        self.use_fill.setChecked(color.alpha() > 0)
        
        # Update fill pattern
        pattern = item.get_property("fill_pattern", "Solid")
        index = self.fill_pattern.findText(pattern)
        if index >= 0:
            self.fill_pattern.setCurrentIndex(index)
            
        # Unblock signals
        self.fill_color.blockSignals(False)
        self.use_fill.blockSignals(False)
        self.fill_pattern.blockSignals(False)
        
    def reset(self):
        """Reset all property controls"""
        self.fill_color.set_color(QColor(255, 255, 255, 0))
        self.use_fill.setChecked(False)
        self.fill_pattern.setCurrentIndex(0)  # Solid

class TextProperties(PropertyGroup):
    """Property group for text properties"""
    
    def __init__(self, parent=None):
        super().__init__("Text", parent)
        
    def setup_ui(self):
        """Set up the UI for this property group"""
        super().setup_ui()
        
        # Text content
        self.text_content = QLineEdit()
        self.layout.addRow("Text:", self.text_content)
        
        # Font family
        self.font_family = QComboBox()
        self.font_family.addItems(["Arial", "Times New Roman", "Courier New", "Verdana", "Tahoma"])
        self.layout.addRow("Font:", self.font_family)
        
        # Font size
        self.font_size = QSpinBox()
        self.font_size.setRange(6, 72)
        self.font_size.setValue(12)
        self.layout.addRow("Size:", self.font_size)
        
        # Bold and italic
        self.font_bold = QCheckBox("Bold")
        self.font_italic = QCheckBox("Italic")
        
        style_layout = QHBoxLayout()
        style_layout.addWidget(self.font_bold)
        style_layout.addWidget(self.font_italic)
        self.layout.addRow("Style:", style_layout)
        
        # Text color
        self.text_color = ColorButton()
        self.layout.addRow("Color:", self.text_color)
        
        self.connect_signals()
        
    def connect_signals(self):
        """Connect signals for property changes"""
        self.text_content.textChanged.connect(lambda t: self.propertyChanged.emit("text", t))
        self.font_family.currentIndexChanged.connect(
            lambda i: self.propertyChanged.emit("font_family", self.font_family.currentText())
        )
        self.font_size.valueChanged.connect(lambda v: self.propertyChanged.emit("font_size", v))
        self.font_bold.toggled.connect(lambda c: self.propertyChanged.emit("font_bold", c))
        self.font_italic.toggled.connect(lambda c: self.propertyChanged.emit("font_italic", c))
        self.text_color.colorChanged.connect(lambda c: self.propertyChanged.emit("text_color", c))
        
    def update_for_item(self, item):
        """Update the properties panel for the given item"""
        if not item or item.item_type != "text":
            self.setVisible(False)
            return
            
        self.setVisible(True)
        
        # Block signals during update
        self.text_content.blockSignals(True)
        self.font_family.blockSignals(True)
        self.font_size.blockSignals(True)
        self.font_bold.blockSignals(True)
        self.font_italic.blockSignals(True)
        self.text_color.blockSignals(True)
        
        # Update text content
        self.text_content.setText(item.text)
        
        # Update font family
        font_family = item.get_property("font_family", "Arial")
        index = self.font_family.findText(font_family)
        if index >= 0:
            self.font_family.setCurrentIndex(index)
            
        # Update font size
        self.font_size.setValue(item.get_property("font_size", 12))
        
        # Update bold and italic
        self.font_bold.setChecked(item.get_property("font_bold", False))
        self.font_italic.setChecked(item.get_property("font_italic", False))
        
        # Update text color
        self.text_color.set_color(item.get_property("text_color", QColor(0, 0, 0)))
        
        # Unblock signals
        self.text_content.blockSignals(False)
        self.font_family.blockSignals(False)
        self.font_size.blockSignals(False)
        self.font_bold.blockSignals(False)
        self.font_italic.blockSignals(False)
        self.text_color.blockSignals(False)
        
    def reset(self):
        """Reset all property controls"""
        self.text_content.setText("")
        self.font_family.setCurrentIndex(0)
        self.font_size.setValue(12)
        self.font_bold.setChecked(False)
        self.font_italic.setChecked(False)
        self.text_color.set_color(QColor(0, 0, 0))

class PropertiesPanel(QScrollArea):
    """Panel for editing properties of selected drawing items"""
    
    property_changed = pyqtSignal(str, object)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_item = None
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the UI for the properties panel"""
        # Main widget and layout
        self.main_widget = QWidget()
        self.main_layout = QVBoxLayout(self.main_widget)
        
        # Create property groups
        self.geometry_props = GeometryProperties()
        self.stroke_props = StrokeProperties()
        self.fill_props = FillProperties()
        self.text_props = TextProperties()
        
        # Add property groups to layout
        self.main_layout.addWidget(self.geometry_props)
        self.main_layout.addWidget(self.stroke_props)
        self.main_layout.addWidget(self.fill_props)
        self.main_layout.addWidget(self.text_props)
        
        # Add stretch at the end
        self.main_layout.addStretch()
        
        # Set widget properties
        self.setWidget(self.main_widget)
        self.setWidgetResizable(True)
        self.setMinimumWidth(250)
        
        # Connect signals
        self.connect_signals()
        
    def connect_signals(self):
        """Connect signals for property changes"""
        self.geometry_props.propertyChanged.connect(self.on_property_changed)
        self.stroke_props.propertyChanged.connect(self.on_property_changed)
        self.fill_props.propertyChanged.connect(self.on_property_changed)
        self.text_props.propertyChanged.connect(self.on_property_changed)
        
    def on_property_changed(self, name, value):
        """Handle property changes from any property group"""
        # Map property names to the actual property names used in drawing items
        property_mapping = {
            "position_x": "position_x",
            "position_y": "position_y",
            "width": "width",
            "height": "height",
            "rotation": "angle",
            "stroke_color": "line_color",
            "stroke_width": "line_width",
            "stroke_style": "line_style",
            "fill_color": "fill_color",
            "fill_pattern": "fill_pattern",
            "text": "text",
            "font_family": "font_family",
            "font_size": "font_size",
            "font_bold": "font_bold",
            "font_italic": "font_italic",
            "text_color": "text_color"
        }
        
        # Emit the property changed signal with the mapped property name
        mapped_name = property_mapping.get(name, name)
        self.property_changed.emit(mapped_name, value)
        
    def update_for_item(self, item):
        """Update all property groups for the given item"""
        self.current_item = item
        
        self.geometry_props.update_for_item(item)
        self.stroke_props.update_for_item(item)
        self.fill_props.update_for_item(item)
        self.text_props.update_for_item(item)
        
    def reset(self):
        """Reset all property groups"""
        self.current_item = None
        
        self.geometry_props.reset()
        self.stroke_props.reset()
        self.fill_props.reset()
        self.text_props.reset() 