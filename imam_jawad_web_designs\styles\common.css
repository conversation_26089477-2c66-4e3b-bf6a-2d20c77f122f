/* Common CSS for <PERSON> (AS) Condolence Web Designs */
/* Enhanced Arabic text rendering and responsive design */

/* Import Arabic fonts from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&family=Noto+Sans+Arabic:wght@400;500;600;700&family=Cairo:wght@400;500;600;700&display=swap');

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Amiri', 'Noto Sans Arabic', 'Cairo', Arial, sans-serif;
    line-height: 1.6;
    direction: rtl;
    text-align: center;
    overflow-x: hidden;
}

/* Arabic Text Optimization */
.arabic-text {
    font-family: 'Amiri', 'Scheherazade New', 'Noto Sans Arabic', 'Cairo', Arial, sans-serif;
    direction: rtl;
    unicode-bidi: bidi-override;
    text-align: center;
    font-feature-settings: "liga" 1, "calt" 1, "kern" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Main Container - 1:1 Aspect Ratio */
.design-container {
    width: 100vmin;
    height: 100vmin;
    max-width: 600px;
    max-height: 600px;
    min-width: 300px;
    min-height: 300px;
    margin: 20px auto;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 5%;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .design-container {
        width: 95vmin;
        height: 95vmin;
        margin: 10px auto;
        padding: 4%;
    }
}

@media (max-width: 480px) {
    .design-container {
        width: 98vmin;
        height: 98vmin;
        margin: 5px auto;
        padding: 3%;
    }
}

/* Decorative Borders */
.design-container::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 3px solid;
    border-radius: 5px;
    pointer-events: none;
    z-index: 1;
}

.design-container::after {
    content: '';
    position: absolute;
    top: 50px;
    left: 50px;
    right: 50px;
    bottom: 50px;
    border: 2px solid;
    border-radius: 3px;
    pointer-events: none;
    z-index: 1;
}

/* Text Elements */
.text-element {
    position: relative;
    z-index: 2;
    margin: 0;
    padding: 0;
    white-space: nowrap;
    overflow: visible;
}

/* Font Sizes - Responsive */
.bismillah {
    font-size: clamp(1.2rem, 3.5vmin, 2rem);
    font-weight: 400;
    margin-bottom: 1rem;
}

.condolence-intro {
    font-size: clamp(1.4rem, 4vmin, 2.5rem);
    font-weight: 500;
    margin-bottom: 1rem;
}

.imam-name {
    font-size: clamp(1.6rem, 4.5vmin, 3rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.date {
    font-size: clamp(1.2rem, 3.5vmin, 2rem);
    font-weight: 400;
    margin-bottom: 1rem;
}

.condolence-phrase {
    font-size: clamp(1.4rem, 4vmin, 2.5rem);
    font-weight: 500;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.salawat {
    font-size: clamp(1.2rem, 3.5vmin, 2rem);
    font-weight: 400;
    margin-bottom: 1rem;
}

/* Decorative Line */
.decorative-line {
    width: 40%;
    height: 3px;
    margin: 1rem auto;
    border-radius: 2px;
    position: relative;
    z-index: 2;
}

.decorative-line::before,
.decorative-line::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.decorative-line::before {
    left: -20px;
}

.decorative-line::after {
    right: -20px;
}

/* Corner Decorations */
.corner-decoration {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 2px solid;
    border-radius: 50%;
    z-index: 2;
}

.corner-decoration::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 15px;
    height: 15px;
    border: 1px solid;
    border-radius: 50%;
}

.corner-decoration.top-left {
    top: 80px;
    left: 80px;
}

.corner-decoration.top-right {
    top: 80px;
    right: 80px;
}

.corner-decoration.bottom-left {
    bottom: 80px;
    left: 80px;
}

.corner-decoration.bottom-right {
    bottom: 80px;
    right: 80px;
}

/* Watermark */
.watermark {
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: clamp(0.8rem, 2vmin, 1.2rem);
    opacity: 0.6;
    font-family: Arial, sans-serif;
    direction: ltr;
    z-index: 2;
}

/* Text Effects */
.text-shadow {
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
}

.text-outline {
    text-shadow: 
        -2px -2px 0 #000,
        2px -2px 0 #000,
        -2px 2px 0 #000,
        2px 2px 0 #000,
        -2px 0 0 #000,
        2px 0 0 #000,
        0 -2px 0 #000,
        0 2px 0 #000;
}

/* Color Schemes */
.classic-theme {
    background-color: #000000;
    color: #FFD700;
}

.classic-theme .text-element.secondary {
    color: #FFFFFF;
}

.classic-theme::before {
    border-color: #FFD700;
}

.classic-theme::after {
    border-color: #8B4513;
}

.classic-theme .decorative-line {
    background-color: #8B4513;
}

.classic-theme .decorative-line::before,
.classic-theme .decorative-line::after {
    background-color: #FFD700;
}

.classic-theme .corner-decoration {
    border-color: #8B4513;
}

.classic-theme .corner-decoration::after {
    border-color: #8B4513;
}

.elegant-theme {
    background-color: #191919;
    color: #C8C8C8;
}

.elegant-theme .text-element.secondary {
    color: #FFFFFF;
}

.elegant-theme::before {
    border-color: #C8C8C8;
}

.elegant-theme::after {
    border-color: #8B4513;
}

.elegant-theme .decorative-line {
    background-color: #8B4513;
}

.elegant-theme .decorative-line::before,
.elegant-theme .decorative-line::after {
    background-color: #C8C8C8;
}

.elegant-theme .corner-decoration {
    border-color: #8B4513;
}

.elegant-theme .corner-decoration::after {
    border-color: #8B4513;
}

.traditional-theme {
    background-color: #000000;
    color: #FFFFFF;
}

.traditional-theme .text-element.secondary {
    color: #FFFFFF;
}

.traditional-theme::before {
    border-color: #FFFFFF;
}

.traditional-theme::after {
    border-color: #8B4513;
}

.traditional-theme .decorative-line {
    background-color: #8B4513;
}

.traditional-theme .decorative-line::before,
.traditional-theme .decorative-line::after {
    background-color: #FFFFFF;
}

.traditional-theme .corner-decoration {
    border-color: #8B4513;
}

.traditional-theme .corner-decoration::after {
    border-color: #8B4513;
}

.royal-theme {
    background-color: #141428;
    color: #FFD700;
}

.royal-theme .text-element.secondary {
    color: #FFFFFF;
}

.royal-theme::before {
    border-color: #FFD700;
}

.royal-theme::after {
    border-color: #8B4513;
}

.royal-theme .decorative-line {
    background-color: #8B4513;
}

.royal-theme .decorative-line::before,
.royal-theme .decorative-line::after {
    background-color: #FFD700;
}

.royal-theme .corner-decoration {
    border-color: #8B4513;
}

.royal-theme .corner-decoration::after {
    border-color: #8B4513;
}

/* Navigation Styles */
.navigation {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: 5px;
}

.navigation a {
    color: white;
    text-decoration: none;
    margin: 0 10px;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.navigation a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Print Styles */
@media print {
    .navigation {
        display: none;
    }
    
    .design-container {
        width: 100%;
        height: 100vh;
        max-width: none;
        max-height: none;
        margin: 0;
        box-shadow: none;
        border-radius: 0;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .text-shadow {
        text-shadow: 2px 2px 0 #000, -2px -2px 0 #000, 2px -2px 0 #000, -2px 2px 0 #000;
    }
}
