import numpy as np
from PIL import Image, ImageDraw
import math

def create_geometric_pattern(size, complexity=5, color=(0, 0, 50)):
    """Create an Islamic geometric pattern background"""
    # Create a new image with a dark background
    img = Image.new('RGB', size, color)
    draw = ImageDraw.Draw(img)
    
    # Calculate pattern parameters
    width, height = size
    center_x, center_y = width // 2, height // 2
    max_radius = min(width, height) // 2
    
    # Draw concentric circles
    for i in range(complexity):
        radius = max_radius * (i + 1) / complexity
        draw.ellipse(
            [center_x - radius, center_y - radius,
             center_x + radius, center_y + radius],
            outline=(50, 50, 100),
            width=2
        )
    
    # Draw geometric lines
    for angle in range(0, 360, 45):
        rad = math.radians(angle)
        x1 = center_x + max_radius * math.cos(rad)
        y1 = center_y + max_radius * math.sin(rad)
        x2 = center_x - max_radius * math.cos(rad)
        y2 = center_y - max_radius * math.sin(rad)
        draw.line([(x1, y1), (x2, y2)], fill=(50, 50, 100), width=2)
    
    return img

def create_gradient_background(size, start_color=(0, 0, 0), end_color=(0, 0, 50)):
    """Create a gradient background"""
    img = Image.new('RGB', size)
    draw = ImageDraw.Draw(img)
    
    for y in range(size[1]):
        # Calculate gradient color
        ratio = y / size[1]
        r = int(start_color[0] * (1 - ratio) + end_color[0] * ratio)
        g = int(start_color[1] * (1 - ratio) + end_color[1] * ratio)
        b = int(start_color[2] * (1 - ratio) + end_color[2] * ratio)
        
        # Draw horizontal line with gradient color
        draw.line([(0, y), (size[0], y)], fill=(r, g, b))
    
    return img

def create_islamic_frame(size, border_width=20, color=(50, 50, 100)):
    """Create an Islamic-style frame"""
    img = Image.new('RGB', size, (0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw outer border
    draw.rectangle(
        [border_width, border_width,
         size[0] - border_width, size[1] - border_width],
        outline=color,
        width=2
    )
    
    # Draw corner decorations
    corner_size = border_width * 2
    for x, y in [(0, 0), (size[0] - corner_size, 0),
                 (0, size[1] - corner_size), (size[0] - corner_size, size[1] - corner_size)]:
        draw.rectangle(
            [x, y, x + corner_size, y + corner_size],
            outline=color,
            width=2
        )
    
    return img

def combine_patterns(size, pattern_type='geometric'):
    """Combine different patterns to create a complete background"""
    if pattern_type == 'geometric':
        base = create_geometric_pattern(size)
    else:
        base = create_gradient_background(size)
    
    # Add frame
    frame = create_islamic_frame(size)
    
    # Combine patterns
    result = Image.alpha_composite(base.convert('RGBA'), frame.convert('RGBA'))
    return result.convert('RGB') 