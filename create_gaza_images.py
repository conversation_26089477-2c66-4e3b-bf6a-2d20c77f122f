#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os

# List of phrases
PHRASES = [
    "صمت العالم أمام معاناة غزة هو شهادة وفاة للضمير الإنساني",
    "عندما يصبح الخبز حلماً والأمان ذكرى، أين العدالة الدولية؟",
    "أطفال غزة يحملون أثقال العالم على أكتافهم الصغيرة",
    "في غزة، الإنسانية تُختبر والعالم يفشل في الامتحان",
    "عندما تتحول المساعدات الإنسانية إلى منّة، تموت كرامة الشعوب",
    "غزة تنزف والعالم يتفرج... أي عدالة هذه؟",
    "الصمت العالمي أمام مأساة غزة جريمة لا تُغتفر",
    "عندما يُحرم الأطفال من طفولتهم، تفقد الإنسانية معناها",
    "في عالم الشعارات الفارغة، غزة تدفع ثمن النفاق الدولي",
    "أين وعود العالم عندما يحمل الأطفال أعباء البقاء على قيد الحياة؟"
]

def add_arabic_text_to_image(image_path, output_path, text, position='bottom'):
    """
    Add Arabic text to an image with a semi-transparent background.
    
    Args:
        image_path (str): Path to the source image
        output_path (str): Path to save the resulting image
        text (str): Arabic text to add to the image
        position (str): Position of the text ('top', 'bottom', 'center')
    """
    try:
        # Open the image
        img = Image.open(image_path)
        width, height = img.size
        
        # Try to find a font that supports Arabic
        font_size = int(width / 20)  # Scale font size based on image width
        font = None
        
        # Common paths for fonts that might support Arabic
        possible_fonts = [
            "arial.ttf",  # Windows
            "C:\\Windows\\Fonts\\arial.ttf",  # Windows explicit path
            "C:\\Windows\\Fonts\\arialbd.ttf",  # Windows Arial Bold
            "C:\\Windows\\Fonts\\calibri.ttf",  # Windows Calibri
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            "/System/Library/Fonts/Arial Unicode.ttf",  # macOS
            "/usr/share/fonts/truetype/freefont/FreeSans.ttf",  # Some Linux
        ]
        
        for font_path in possible_fonts:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, font_size)
                    break
                except Exception:
                    continue
        
        if font is None:
            # If no suitable font found, use default (may not support Arabic well)
            font = ImageFont.load_default()
            print("Warning: Using default font which may not support Arabic properly.")
        
        # Calculate text position
        padding = int(width * 0.05)  # 5% padding
        
        # Create a semi-transparent background for the text
        # First, estimate text size (this is approximate for Arabic)
        text_width = len(text) * font_size * 0.6  # Approximate width
        text_height = font_size * 2  # Give some extra height
        
        if position == 'top':
            text_position = (padding, padding)
            rect_position = (0, 0, width, text_height + padding * 2)
        elif position == 'center':
            text_position = (padding, (height - text_height) // 2)
            rect_position = (0, (height - text_height) // 2 - padding, 
                            width, (height + text_height) // 2 + padding)
        else:  # bottom
            text_position = (padding, height - text_height - padding)
            rect_position = (0, height - text_height - padding * 2, 
                            width, height)
        
        # Draw semi-transparent background
        overlay = Image.new('RGBA', img.size, (0, 0, 0, 0))
        overlay_draw = ImageDraw.Draw(overlay)
        overlay_draw.rectangle(rect_position, fill=(0, 0, 0, 128))  # Semi-transparent black
        
        # Composite the overlay onto the original image
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        img = Image.alpha_composite(img, overlay)
        draw = ImageDraw.Draw(img)
        
        # Draw text
        draw.text(text_position, text, font=font, fill=(255, 255, 255, 255))
        
        # Convert back to RGB if needed and save
        if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
            img = img.convert('RGB')
        
        img.save(output_path)
        print(f"Image saved to {output_path}")
        return True
    
    except Exception as e:
        print(f"Error processing image: {e}")
        return False

def main():
    # Ask for the image path
    image_path = input("Enter the path to your image (e.g., gaza_image.jpg): ")
    
    # Validate image path
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found.")
        return
    
    # Create output directory
    output_dir = "gaza_images_with_text"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Ask for text position
    position = input("Enter text position (top, center, bottom) [default: bottom]: ").lower()
    if position not in ['top', 'center', 'bottom']:
        position = 'bottom'
    
    # Process each phrase
    for i, phrase in enumerate(PHRASES):
        output_path = os.path.join(output_dir, f"gaza_image_{i+1}.jpg")
        print(f"\nAdding text {i+1}/{len(PHRASES)}:")
        print(f"Phrase: {phrase}")
        
        success = add_arabic_text_to_image(image_path, output_path, phrase, position)
        if success:
            print(f"✓ Successfully created image {i+1}")
        else:
            print(f"✗ Failed to create image {i+1}")
    
    print(f"\nAll images have been saved to the '{output_dir}' directory.")

if __name__ == "__main__":
    main()
