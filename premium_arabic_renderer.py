#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Premium Arabic Text Renderer for Professional Islamic Videos
Enhanced typography with premium fonts, advanced effects, and professional rendering.
"""

from PIL import Image, ImageDraw, ImageFont, ImageFilter
import os
import sys
import requests
import numpy as np

# Try to import Arabic text processing libraries
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
    print("✓ Arabic text processing libraries loaded successfully")
except ImportError as e:
    ARABIC_SUPPORT = False
    print(f"⚠️  Arabic libraries not available: {e}")

class PremiumArabicRenderer:
    """Premium Arabic text renderer with professional typography and effects."""

    def __init__(self):
        self.premium_fonts = self._download_premium_fonts()
        self.system_fonts = self._find_system_fonts()
        self.font_cache = {}

    def _download_premium_fonts(self):
        """Download premium Islamic fonts if not available."""
        fonts_dir = "fonts"
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)

        premium_fonts = {
            'Amiri-Regular.ttf': 'https://github.com/aliftype/amiri/releases/download/0.117/Amiri-0.117.zip',
            'Scheherazade-Regular.ttf': 'https://software.sil.org/downloads/r/scheherazade/Scheherazade-2.100.zip',
            'NotoSansArabic-Regular.ttf': 'https://fonts.google.com/download?family=Noto%20Sans%20Arabic'
        }

        available_fonts = []

        # Check for existing premium fonts
        for font_name in premium_fonts.keys():
            font_path = os.path.join(fonts_dir, font_name)
            if os.path.exists(font_path):
                available_fonts.append(font_path)
                print(f"✓ Found premium font: {font_name}")

        # If no premium fonts found, use system fonts
        if not available_fonts:
            print("ℹ️  Premium fonts not found, using system fonts")

        return available_fonts

    def _find_system_fonts(self):
        """Find high-quality system fonts that support Arabic."""
        system_font_paths = [
            # Windows fonts
            "C:\\Windows\\Fonts\\arial.ttf",
            "C:\\Windows\\Fonts\\calibri.ttf",
            "C:\\Windows\\Fonts\\tahoma.ttf",
            "C:\\Windows\\Fonts\\segoeui.ttf",
            "C:\\Windows\\Fonts\\times.ttf",
            "C:\\Windows\\Fonts\\trebuc.ttf",
            "C:\\Windows\\Fonts\\georgia.ttf",

            # Specialized Arabic fonts (if installed)
            "C:\\Windows\\Fonts\\TraditionalArabic.ttf",
            "C:\\Windows\\Fonts\\SimplifiedArabic.ttf",
            "C:\\Windows\\Fonts\\ArabicTypesetting.ttf",

            # Linux fonts
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSansArabic-Regular.ttf",

            # macOS fonts
            "/System/Library/Fonts/Arial Unicode.ttf",
            "/System/Library/Fonts/GeezaPro.ttc",
            "/System/Library/Fonts/Baghdad.ttf"
        ]

        available_fonts = []
        for font_path in system_font_paths:
            if os.path.exists(font_path):
                try:
                    test_font = ImageFont.truetype(font_path, 20)
                    available_fonts.append(font_path)
                    print(f"✓ Found system font: {os.path.basename(font_path)}")
                except Exception:
                    continue

        return available_fonts

    def get_best_font(self, size, style='regular'):
        """Get the best available font for the given size and style."""
        cache_key = f"{size}_{style}"
        if cache_key in self.font_cache:
            return self.font_cache[cache_key]

        # Priority: Premium fonts > Specialized Arabic > System fonts
        font_candidates = self.premium_fonts + self.system_fonts

        # Style-specific font preferences
        style_preferences = {
            'title': ['Amiri', 'TraditionalArabic', 'georgia', 'times'],
            'elegant': ['Scheherazade', 'ArabicTypesetting', 'calibri'],
            'modern': ['NotoSansArabic', 'segoeui', 'arial'],
            'regular': ['arial', 'calibri', 'tahoma']
        }

        preferred_fonts = style_preferences.get(style, style_preferences['regular'])

        # Try preferred fonts first
        for pref in preferred_fonts:
            for font_path in font_candidates:
                if pref.lower() in os.path.basename(font_path).lower():
                    try:
                        font = ImageFont.truetype(font_path, size)
                        font_info = (font, os.path.basename(font_path))
                        self.font_cache[cache_key] = font_info
                        return font_info
                    except Exception:
                        continue

        # Fallback to any available font
        for font_path in font_candidates:
            try:
                font = ImageFont.truetype(font_path, size)
                font_info = (font, os.path.basename(font_path))
                self.font_cache[cache_key] = font_info
                return font_info
            except Exception:
                continue

        # Ultimate fallback
        font = ImageFont.load_default()
        font_info = (font, "default")
        self.font_cache[cache_key] = font_info
        return font_info

    def process_arabic_text(self, text):
        """Process Arabic text with enhanced reshaping and bidirectional support."""
        if not ARABIC_SUPPORT:
            return text

        try:
            # Enhanced reshaper configuration
            reshaper = arabic_reshaper.ArabicReshaper(
                configuration={
                    'delete_harakat': False,
                    'support_zwj': True,
                    'support_zwnj': True,
                    'use_unshaped_instead_of_isolated': False,
                    'shift_harakat_position': True,
                    'support_ligatures': True
                }
            )

            reshaped_text = reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text

        except Exception as e:
            print(f"⚠️  Arabic processing error: {e}")
            return text

    def create_gradient_background(self, size, colors, direction='vertical'):
        """Create a gradient background."""
        width, height = size
        gradient = Image.new('RGB', size)

        if direction == 'vertical':
            for y in range(height):
                ratio = y / height
                r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
                g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
                b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)

                for x in range(width):
                    gradient.putpixel((x, y), (r, g, b))
        else:  # horizontal
            for x in range(width):
                ratio = x / width
                r = int(colors[0][0] * (1 - ratio) + colors[1][0] * ratio)
                g = int(colors[0][1] * (1 - ratio) + colors[1][1] * ratio)
                b = int(colors[0][2] * (1 - ratio) + colors[1][2] * ratio)

                for y in range(height):
                    gradient.putpixel((x, y), (r, g, b))

        return gradient

    def add_text_glow(self, img, position, text, font, color, glow_radius=3):
        """Add a glowing effect to text."""
        # Create a larger image for the glow
        glow_img = Image.new('RGBA', (img.width + glow_radius*4, img.height + glow_radius*4), (0, 0, 0, 0))
        glow_draw = ImageDraw.Draw(glow_img)

        # Adjust position for the larger canvas
        glow_pos = (position[0] + glow_radius*2, position[1] + glow_radius*2)

        # Draw text multiple times with slight offsets for glow
        processed_text = self.process_arabic_text(text)
        glow_color = (*color, 100)  # Semi-transparent

        for offset in range(1, glow_radius + 1):
            for dx in range(-offset, offset + 1):
                for dy in range(-offset, offset + 1):
                    if dx*dx + dy*dy <= offset*offset:
                        glow_draw.text((glow_pos[0] + dx, glow_pos[1] + dy),
                                     processed_text, font=font, fill=glow_color)

        # Apply blur to create glow effect
        glow_img = glow_img.filter(ImageFilter.GaussianBlur(radius=glow_radius/2))

        # Paste glow onto original image
        img.paste(glow_img, (-glow_radius*2, -glow_radius*2), glow_img)

        # Draw the main text on top
        draw = ImageDraw.Draw(img)
        draw.text(position, processed_text, font=font, fill=color)

        return img

    def draw_ornate_border(self, img, style='classic', color=(255, 215, 0)):
        """Draw ornate Islamic geometric borders."""
        draw = ImageDraw.Draw(img)
        width, height = img.size

        if style == 'classic':
            # Simple elegant border
            border_width = 8
            draw.rectangle([border_width, border_width, width-border_width, height-border_width],
                          outline=color, width=3)

            # Inner decorative frame
            inner_margin = 60
            draw.rectangle([inner_margin, inner_margin, width-inner_margin, height-inner_margin],
                          outline=color, width=2)

        elif style == 'ornate':
            # Complex geometric pattern
            self._draw_geometric_pattern(draw, width, height, color)

        elif style == 'golden':
            # Golden ornamental border
            self._draw_golden_ornaments(draw, width, height, color)

        return img

    def _draw_geometric_pattern(self, draw, width, height, color):
        """Draw Islamic geometric patterns."""
        # Outer border
        border_width = 12
        draw.rectangle([border_width, border_width, width-border_width, height-border_width],
                      outline=color, width=4)

        # Corner ornaments
        corner_size = 80
        corners = [(border_width, border_width),
                  (width-border_width-corner_size, border_width),
                  (border_width, height-border_width-corner_size),
                  (width-border_width-corner_size, height-border_width-corner_size)]

        for corner_x, corner_y in corners:
            # Draw decorative corner elements
            for i in range(3):
                offset = i * 15
                draw.arc([corner_x + offset, corner_y + offset,
                         corner_x + corner_size - offset, corner_y + corner_size - offset],
                        0, 90, fill=color, width=2)

    def _draw_golden_ornaments(self, draw, width, height, color):
        """Draw golden ornamental decorations."""
        # Multiple border layers
        for i in range(3):
            offset = i * 8 + 8
            draw.rectangle([offset, offset, width-offset, height-offset],
                          outline=color, width=2)

        # Decorative elements at midpoints
        mid_x, mid_y = width // 2, height // 2
        ornament_size = 30

        # Top, bottom, left, right ornaments
        positions = [(mid_x, 40), (mid_x, height-40), (40, mid_y), (width-40, mid_y)]

        for x, y in positions:
            draw.ellipse([x-ornament_size//2, y-ornament_size//2,
                         x+ornament_size//2, y+ornament_size//2],
                        outline=color, width=3)

    def draw_enhanced_text(self, img, position, text, font, color, style_effects=None):
        """Draw text with enhanced effects based on style."""
        if style_effects is None:
            style_effects = {}

        if style_effects.get('text_glow'):
            img = self.add_text_glow(img, position, text, font, color,
                                   glow_radius=style_effects.get('glow_radius', 3))
        elif style_effects.get('text_shadow'):
            img = self.draw_text_with_shadow(img, position, text, font, color,
                                     shadow_offset=style_effects.get('shadow_offset', (2, 2)))
        elif style_effects.get('divine_glow'):
            img = self.add_text_glow(img, position, text, font, color, glow_radius=5)
        else:
            # Standard text rendering
            draw = ImageDraw.Draw(img)
            processed_text = self.process_arabic_text(text)
            draw.text(position, processed_text, font=font, fill=color)

        return img

    def draw_text_with_shadow(self, img, position, text, font, color, shadow_offset=(2, 2), shadow_color=(0, 0, 0)):
        """Draw text with shadow effect."""
        draw = ImageDraw.Draw(img)
        processed_text = self.process_arabic_text(text)

        # Draw shadow
        shadow_pos = (position[0] + shadow_offset[0], position[1] + shadow_offset[1])
        draw.text(shadow_pos, processed_text, font=font, fill=shadow_color)

        # Draw main text
        draw.text(position, processed_text, font=font, fill=color)

        return img

    def get_text_dimensions(self, text, font):
        """Get accurate text dimensions for layout calculations."""
        temp_img = Image.new('RGB', (1, 1))
        temp_draw = ImageDraw.Draw(temp_img)
        processed_text = self.process_arabic_text(text)
        bbox = temp_draw.textbbox((0, 0), processed_text, font=font)
        return bbox[2] - bbox[0], bbox[3] - bbox[1], processed_text

    def draw_centered_text(self, img, text, y_position, font, color, style_effects=None):
        """Draw centered text with enhanced effects."""
        width, height, processed_text = self.get_text_dimensions(text, font)
        x_position = (img.width - width) // 2
        position = (x_position, y_position)

        return self.draw_enhanced_text(img, position, text, font, color, style_effects)

def test_premium_renderer():
    """Test the premium Arabic renderer."""
    print("Testing Premium Arabic Renderer...")
    print("=" * 40)

    renderer = PremiumArabicRenderer()

    # Test font loading
    font, font_name = renderer.get_best_font(36, 'title')
    print(f"✓ Best title font: {font_name}")

    # Test text processing
    test_text = "بسم الله الرحمن الرحيم"
    processed = renderer.process_arabic_text(test_text)
    print(f"✓ Text processed: {test_text}")

    # Test gradient creation
    gradient = renderer.create_gradient_background((400, 300), [(0, 0, 0), (50, 50, 100)])
    print(f"✓ Gradient created: {gradient.size}")

    print("✓ Premium renderer test completed")

if __name__ == "__main__":
    test_premium_renderer()
