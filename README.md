# Gaza Image Text Overlay

This project contains Python scripts to add Arabic text overlays to images related to the humanitarian crisis in Gaza.

## Prerequisites

Before using these scripts, you need to have Python installed along with the Pillow library. If you don't have Pillow installed, you can install it using pip:

```
pip install Pillow
```

## Available Scripts

### 1. create_gaza_images.py

This is the main script that creates multiple images with different Arabic phrases. It will:
- Ask for the path to your source image
- Create a directory called "gaza_images_with_text"
- Generate 10 different images with different Arabic phrases
- Each image will have a semi-transparent background behind the text for better readability

**Usage:**
```
python create_gaza_images.py
```

### 2. add_arabic_text.py

This script allows you to add a single custom Arabic text to an image.

**Usage:**
```
python add_arabic_text.py <image_path> <text> [position] [output_path]
```

**Parameters:**
- `image_path`: Path to the source image
- `text`: Arabic text to add to the image
- `position` (optional): Position of the text ('top', 'center', or 'bottom', default: 'bottom')
- `output_path` (optional): Path to save the resulting image (default: 'output_image.jpg')

**Example:**
```
python add_arabic_text.py gaza_image.jpg "صمت العالم أمام معاناة غزة" bottom gaza_with_text.jpg
```

### 3. gaza_image_text.py

This is a more advanced script with additional customization options. It's not meant to be used directly but can be modified for more specific needs.

## Font Support

The scripts will try to find a suitable font on your system that supports Arabic characters. Common fonts that support Arabic include:
- Arial (Windows)
- DejaVu Sans (Linux)
- Arial Unicode (macOS)

If no suitable font is found, the script will use the default font, which may not display Arabic text correctly.

## Phrases Included

The scripts include the following phrases in Arabic:

1. "صمت العالم أمام معاناة غزة هو شهادة وفاة للضمير الإنساني"
   (The world's silence in the face of Gaza's suffering is a death certificate for human conscience)

2. "عندما يصبح الخبز حلماً والأمان ذكرى، أين العدالة الدولية؟"
   (When bread becomes a dream and safety a memory, where is international justice?)

3. "أطفال غزة يحملون أثقال العالم على أكتافهم الصغيرة"
   (Gaza's children carry the weight of the world on their small shoulders)

4. "في غزة، الإنسانية تُختبر والعالم يفشل في الامتحان"
   (In Gaza, humanity is tested and the world fails the exam)

5. "عندما تتحول المساعدات الإنسانية إلى منّة، تموت كرامة الشعوب"
   (When humanitarian aid becomes a favor, the dignity of people dies)

6. "غزة تنزف والعالم يتفرج... أي عدالة هذه؟"
   (Gaza bleeds while the world watches... what justice is this?)

7. "الصمت العالمي أمام مأساة غزة جريمة لا تُغتفر"
   (The global silence in the face of Gaza's tragedy is an unforgivable crime)

8. "عندما يُحرم الأطفال من طفولتهم، تفقد الإنسانية معناها"
   (When children are deprived of their childhood, humanity loses its meaning)

9. "في عالم الشعارات الفارغة، غزة تدفع ثمن النفاق الدولي"
   (In a world of empty slogans, Gaza pays the price of international hypocrisy)

10. "أين وعود العالم عندما يحمل الأطفال أعباء البقاء على قيد الحياة؟"
    (Where are the world's promises when children carry the burden of survival?)

## Customization

If you want to add your own phrases, you can edit the `PHRASES` list in the `create_gaza_images.py` file.
