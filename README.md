# <PERSON> Commemoration Video Generator

This project creates a professional condolence video commemorating the martyrdom of Imam <PERSON> (peace be upon him). The video includes Arabic text with proper RTL rendering, English subtitles, and smooth animations.

## Features

- Professional mourning design with Islamic elements
- Arabic text with proper RTL rendering
- English subtitles for accessibility
- Smooth text animations and transitions
- High-quality video output (1080x1080)
- Background audio support

## Requirements

- Python 3.8 or higher
- Required Python packages (install using `pip install -r requirements.txt`):
  - moviepy
  - Pillow
  - arabic-reshaper
  - python-bidi
  - numpy
  - pygame

## Installation

1. Clone this repository
2. Install the required packages:
   ```bash
   pip install -r requirements.txt
   ```
3. Download and install the Amiri font (for Arabic text):
   - Windows: Download from Google Fonts and install
   - Linux: `sudo apt-get install fonts-amiri`
   - macOS: Download from Google Fonts and install

## Usage

1. Place your background audio file (if any) in the project directory
2. Run the script:
   ```bash
   python imam_jawad_video.py
   ```
3. The output video will be saved as `imam_jawad_commemoration.mp4`

## Customization

You can modify the following in `imam_jawad_video.py`:
- Video duration and resolution
- Text content and translations
- Font sizes and colors
- Animation timings
- Background design

## Output

The script generates an MP4 video file with the following specifications:
- Resolution: 1080x1080
- Frame rate: 30 FPS
- Format: MP4 (H.264)
- Audio: AAC (if background audio is provided)

## Notes

- Make sure you have sufficient disk space for video processing
- The script requires a system with decent processing power for video rendering
- Arabic text rendering requires the Amiri font to be installed on your system
