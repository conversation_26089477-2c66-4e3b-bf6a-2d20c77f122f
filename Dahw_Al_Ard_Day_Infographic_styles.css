/* Additional styles for Dahw Al-Ard Day Infographic */

/* Islamic Geometric Pattern Background */
.geometric-pattern {
    background-color: #f8f5f0;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><g fill="none" stroke="%231d6a4b" stroke-width="0.5"><path d="M0,50 L100,50 M50,0 L50,100 M0,0 L100,100 M0,100 L100,0"/><circle cx="50" cy="50" r="40"/><circle cx="50" cy="50" r="20"/></g></svg>');
    background-size: 100px 100px;
    opacity: 0.1;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

/* Enhanced <PERSON><PERSON> Illustration */
.kaaba-container {
    position: relative;
    width: 100%;
    height: 350px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.kaaba {
    width: 150px;
    height: 150px;
    background-color: #222;
    border: 5px solid #d4af37;
    position: relative;
    z-index: 2;
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
}

.kaaba::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 60px;
    background-color: #d4af37;
    opacity: 0.7;
}

.earth-spreading {
    position: absolute;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(29, 106, 75, 0.8) 0%, transparent 70%);
    animation: pulse 4s infinite alternate;
    z-index: 1;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    100% {
        transform: scale(1.2);
        opacity: 0.5;
    }
}

.earth-layers {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.earth-layer {
    position: absolute;
    border-radius: 50%;
    border: 2px dashed rgba(29, 106, 75, 0.3);
}

.layer-1 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.layer-2 {
    width: 300px;
    height: 300px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.layer-3 {
    width: 400px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Quranic Verse Styling */
.quran-verse {
    background-color: #f0f7f4;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    position: relative;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.quran-verse::before {
    content: "﴾";
    font-size: 30px;
    color: #1d6a4b;
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.quran-verse::after {
    content: "﴿";
    font-size: 30px;
    color: #1d6a4b;
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
}

.verse-arabic {
    font-family: 'Scheherazade New', serif;
    font-size: 28px;
    text-align: center;
    margin-bottom: 10px;
    line-height: 1.8;
    color: #0d3d2d;
}

.verse-translation {
    font-style: italic;
    text-align: center;
    color: #555;
}

.verse-reference {
    text-align: right;
    font-size: 14px;
    color: #1d6a4b;
    margin-top: 10px;
}

/* Enhanced Timeline */
.enhanced-timeline {
    position: relative;
    max-width: 1200px;
    margin: 40px auto;
    padding: 20px 0;
}

.timeline-line {
    position: absolute;
    width: 6px;
    background-color: #1d6a4b;
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
}

.enhanced-timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    margin-bottom: 30px;
}

.enhanced-timeline-item::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: #fff;
    border: 4px solid #1d6a4b;
    border-radius: 50%;
    top: 15px;
    z-index: 1;
    box-shadow: 0 0 0 4px rgba(29, 106, 75, 0.2);
}

.timeline-left {
    left: 0;
}

.timeline-right {
    left: 50%;
}

.timeline-left::after {
    right: -12px;
}

.timeline-right::after {
    left: -12px;
}

.enhanced-timeline-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.enhanced-timeline-content::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

.timeline-left .enhanced-timeline-content::before {
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent #fff;
    right: -10px;
    top: 15px;
}

.timeline-right .enhanced-timeline-content::before {
    border-width: 10px 10px 10px 0;
    border-color: transparent #fff transparent transparent;
    left: -10px;
    top: 15px;
}

.timeline-date {
    display: inline-block;
    padding: 5px 10px;
    background-color: #1d6a4b;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 10px;
}

/* Recommended Acts Icons */
.act-icon-container {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(29, 106, 75, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 15px;
}

.act-icon {
    font-size: 30px;
    color: #1d6a4b;
}

/* Cultural Practices Section */
.practices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.practice-item {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.practice-item:hover {
    transform: translateY(-5px);
}

.practice-icon {
    font-size: 30px;
    margin-bottom: 15px;
    color: #1d6a4b;
    text-align: center;
}

.practice-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #1d6a4b;
    text-align: center;
}

.practice-description {
    font-size: 14px;
    text-align: center;
}

/* Responsive Adjustments */
@media screen and (max-width: 768px) {
    .timeline-line {
        left: 31px;
    }
    
    .enhanced-timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }
    
    .enhanced-timeline-item::after {
        left: 21px;
    }
    
    .timeline-left::after, .timeline-right::after {
        left: 21px;
    }
    
    .timeline-right {
        left: 0;
    }
    
    .timeline-left .enhanced-timeline-content::before {
        border-width: 10px 10px 10px 0;
        border-color: transparent #fff transparent transparent;
        right: auto;
        left: -10px;
    }
}
