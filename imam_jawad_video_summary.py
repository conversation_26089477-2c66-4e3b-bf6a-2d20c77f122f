#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
<PERSON> Video Creation Summary
Provides a comprehensive summary of the created condolence video system.
"""

import os
import sys
from datetime import datetime

def get_file_info(filepath):
    """Get file information including size and modification time."""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        mtime = os.path.getmtime(filepath)
        return {
            'exists': True,
            'size_mb': size / (1024 * 1024),
            'size_kb': size / 1024,
            'modified': datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
        }
    return {'exists': False}

def analyze_output_directory():
    """Analyze the output directory and provide summary."""
    output_dir = "imam_jawad_video_output"
    
    if not os.path.exists(output_dir):
        return None
    
    files = []
    total_size = 0
    
    for root, dirs, filenames in os.walk(output_dir):
        for filename in filenames:
            filepath = os.path.join(root, filename)
            info = get_file_info(filepath)
            if info['exists']:
                files.append({
                    'path': filepath,
                    'name': filename,
                    'type': os.path.splitext(filename)[1].lower(),
                    'info': info
                })
                total_size += info['size_mb']
    
    return {
        'directory': output_dir,
        'files': files,
        'total_files': len(files),
        'total_size_mb': total_size
    }

def print_video_content_summary():
    """Print summary of video content."""
    try:
        from imam_jawad_video_content import ImamJawadContent
        content = ImamJawadContent()
        
        print("📖 VIDEO CONTENT SUMMARY")
        print("=" * 50)
        
        scenes = content.get_all_scenes()
        total_duration = content.get_total_duration()
        
        print(f"Total Scenes: {len(scenes)}")
        print(f"Total Duration: {total_duration:.1f} seconds")
        print(f"Average Scene Duration: {total_duration/len(scenes):.1f} seconds")
        
        print("\n📋 SCENE BREAKDOWN:")
        for i, scene_name in enumerate(scenes, 1):
            scene_data = content.get_scene_content(scene_name)
            arabic_text = scene_data.get('arabic', 'N/A')
            duration = scene_data.get('duration', 0)
            style = scene_data.get('style', 'default')
            
            print(f"  {i:2d}. {scene_name.replace('_', ' ').title()}")
            print(f"      Arabic: {arabic_text}")
            print(f"      Duration: {duration}s | Style: {style}")
        
        print("\n🎨 VISUAL STYLES:")
        styles = content.get_scene_styles()
        for style_name, colors in styles.items():
            bg = colors['bg_color']
            primary = colors['primary_color']
            print(f"  • {style_name}: BG{bg} + Primary{primary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Could not load content summary: {e}")
        return False

def print_technical_specifications():
    """Print technical specifications."""
    print("\n⚙️ TECHNICAL SPECIFICATIONS")
    print("=" * 50)
    
    print("📐 ASPECT RATIOS & RESOLUTIONS:")
    print("  • Square (1:1): 1080x1080 pixels")
    print("  • Widescreen (16:9): 1920x1080 pixels")
    
    print("\n🎬 VIDEO SPECIFICATIONS:")
    print("  • Frame Rate: 15 FPS (creation) → 8 FPS (output)")
    print("  • Format: Animated GIF")
    print("  • Color Depth: 24-bit RGB")
    print("  • Compression: Optimized GIF")
    print("  • Loop: Infinite")
    
    print("\n🔤 TEXT RENDERING:")
    print("  • Arabic RTL Support: ✓")
    print("  • Bidirectional Text: ✓")
    print("  • Ligature Support: ✓")
    print("  • Font Fallback: ✓")
    print("  • Text Effects: Shadow & Outline")
    
    print("\n🎨 VISUAL FEATURES:")
    print("  • Islamic Geometric Patterns: ✓")
    print("  • Mourning Color Schemes: ✓")
    print("  • Smooth Animations: ✓")
    print("  • Fade Transitions: ✓")
    print("  • Decorative Elements: ✓")
    print("  • AliToucan Branding: ✓")

def print_usage_instructions():
    """Print usage instructions."""
    print("\n📱 SOCIAL MEDIA USAGE")
    print("=" * 50)
    
    print("🟦 INSTAGRAM/FACEBOOK:")
    print("  • Use: Square version (1:1)")
    print("  • Format: GIF or convert to MP4")
    print("  • Optimal: Stories, Posts, Reels")
    
    print("\n🟥 YOUTUBE/GENERAL:")
    print("  • Use: Widescreen version (16:9)")
    print("  • Format: Convert to MP4 for best quality")
    print("  • Optimal: YouTube Shorts, General sharing")
    
    print("\n🎬 MP4 CONVERSION:")
    print("  • Install FFmpeg first")
    print("  • Use provided commands in README")
    print("  • Better compression and quality")
    
    print("\n📤 SHARING GUIDELINES:")
    print("  • Respectful Islamic content")
    print("  • Educational/commemorative purpose")
    print("  • Credit AliToucan when sharing")
    print("  • Maintain dignity of Ahl al-Bayt")

def main():
    """Main summary function."""
    print("🕌 IMAM AL-JAWAD (AS) CONDOLENCE VIDEO")
    print("📹 CREATION SUMMARY REPORT")
    print("=" * 60)
    print(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Analyze output
    output_analysis = analyze_output_directory()
    
    if output_analysis:
        print("\n📁 OUTPUT FILES ANALYSIS")
        print("=" * 50)
        print(f"Output Directory: {output_analysis['directory']}")
        print(f"Total Files: {output_analysis['total_files']}")
        print(f"Total Size: {output_analysis['total_size_mb']:.1f} MB")
        
        print("\n📄 FILE DETAILS:")
        videos = []
        samples = []
        others = []
        
        for file_info in output_analysis['files']:
            if file_info['type'] == '.gif' and 'test' not in file_info['name']:
                videos.append(file_info)
            elif file_info['type'] == '.png':
                samples.append(file_info)
            else:
                others.append(file_info)
        
        if videos:
            print("\n  🎬 MAIN VIDEOS:")
            for video in videos:
                aspect = "Square (1:1)" if "1x1" in video['name'] else "Widescreen (16:9)"
                print(f"    • {video['name']}")
                print(f"      Format: {aspect}")
                print(f"      Size: {video['info']['size_mb']:.1f} MB")
                print(f"      Created: {video['info']['modified']}")
        
        if samples:
            print(f"\n  🖼️ SAMPLE FRAMES: {len(samples)} files")
            for sample in samples[:3]:  # Show first 3
                print(f"    • {sample['name']} ({sample['info']['size_kb']:.0f} KB)")
            if len(samples) > 3:
                print(f"    • ... and {len(samples)-3} more")
        
        if others:
            print(f"\n  📋 OTHER FILES: {len(others)} files")
    
    else:
        print("\n❌ No output directory found. Run the video creator first.")
        print("   python create_imam_jawad_video_auto.py")
        return False
    
    # Content summary
    print_video_content_summary()
    
    # Technical specs
    print_technical_specifications()
    
    # Usage instructions
    print_usage_instructions()
    
    print("\n" + "=" * 60)
    print("🎯 QUICK START COMMANDS")
    print("=" * 60)
    print("🧪 Test System:")
    print("   python test_imam_jawad_video.py")
    print("\n🎬 Create Video (Interactive):")
    print("   python imam_jawad_video_creator.py")
    print("\n🚀 Create Video (Automatic):")
    print("   python create_imam_jawad_video_auto.py")
    print("\n📖 View This Summary:")
    print("   python imam_jawad_video_summary.py")
    
    print("\n" + "=" * 60)
    print("🤲 RELIGIOUS ACKNOWLEDGMENT")
    print("=" * 60)
    print("This video commemorates the martyrdom of:")
    print("الإمام محمد الجواد عليه السلام")
    print("Imam Muhammad al-Jawad (peace be upon him)")
    print("The 9th Imam of Ahl al-Bayt")
    print("Martyred: 29th Dhul Qi'dah 220 AH")
    print("\nMay Allah bless his noble soul and grant him peace.")
    print("اللهم صل على محمد وآل محمد")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    main()
