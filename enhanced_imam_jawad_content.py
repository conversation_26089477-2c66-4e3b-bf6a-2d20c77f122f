#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Imam <PERSON> (AS) Condolence Video Content
Professional-grade content with authentic Shia sources, English subtitles,
additional hadith, Quranic verses, and multiple duration options.
"""

from datetime import datetime
import json

class EnhancedImamJawadContent:
    """Enhanced content manager with professional features and authentic Shia sources."""

    def __init__(self):
        self.content = self._prepare_enhanced_content()
        self.quranic_verses = self._prepare_quranic_verses()
        self.authentic_hadith = self._prepare_authentic_hadith()
        self.historical_dates = self._prepare_historical_dates()

    def _prepare_enhanced_content(self):
        """Prepare enhanced content with English subtitles and professional structure."""

        return {
            # Title Sequence
            'title_sequence': {
                'arabic': 'ذكرى استشهاد الإمام محمد الجواد',
                'english': 'Martyrdom Anniversary of Imam <PERSON>',
                'subtitle_arabic': 'عليه السلام',
                'subtitle_english': '(Peace be upon him)',
                'duration': 4.0,
                'style': 'title_elegant',
                'animation': 'fade_with_calligraphy'
            },

            # Opening with Bismillah
            'opening_bismillah': {
                'arabic': 'بسم الله الرحمن الرحيم',
                'english': 'In the Name of Allah, the Most Gracious, the Most Merciful',
                'duration': 3.5,
                'style': 'elegant',
                'animation': 'gentle_fade'
            },

            # Condolence announcement
            'condolence_announcement': {
                'arabic': 'بمناسبة ذكرى استشهاد',
                'english': 'On the occasion of the martyrdom anniversary',
                'subtitle_arabic': 'نتقدم بأحر التعازي',
                'subtitle_english': 'We extend our heartfelt condolences',
                'duration': 4.0,
                'style': 'elegant',
                'animation': 'slide_up'
            },

            # Imam's full name and titles
            'imam_full_name': {
                'arabic': 'الإمام محمد الجواد عليه السلام',
                'english': 'Imam Muhammad al-Jawad (peace be upon him)',
                'subtitle_arabic': 'التاسع من أئمة أهل البيت الأطهار',
                'subtitle_english': 'The Ninth Imam of the Pure Ahl al-Bayt',
                'duration': 5.0,
                'style': 'highlighted_gold',
                'animation': 'zoom_with_glow'
            },

            # Historical dates
            'martyrdom_dates': {
                'arabic': 'التاسع والعشرون من ذي القعدة',
                'english': '29th of Dhul Qi\'dah',
                'subtitle_arabic': 'سنة ٢٢٠ هجرية - ٨٣٥ ميلادية',
                'subtitle_english': '220 AH - 835 CE',
                'duration': 4.5,
                'style': 'memorial_dates',
                'animation': 'typewriter_effect'
            },

            # Quranic verse about knowledge
            'quranic_knowledge': {
                'arabic': 'وَقُل رَّبِّ زِدْنِي عِلْمًا',
                'english': 'And say: My Lord, increase me in knowledge',
                'subtitle_arabic': 'سورة طه - آية ١١٤',
                'subtitle_english': 'Quran 20:114',
                'duration': 4.5,
                'style': 'quranic_verse',
                'animation': 'divine_glow'
            },

            # Imam's virtues - Knowledge
            'virtues_knowledge': {
                'arabic': 'إمام العلم والحكمة',
                'english': 'Imam of Knowledge and Wisdom',
                'subtitle_arabic': 'أجاب على المسائل وهو في التاسعة من عمره',
                'subtitle_english': 'He answered scholarly questions at the age of nine',
                'duration': 4.5,
                'style': 'wisdom_blue',
                'animation': 'wisdom_sparkle'
            },

            # Authentic Hadith from Imam al-Ridha
            'hadith_ridha': {
                'arabic': 'قال الإمام الرضا عليه السلام:',
                'english': 'Imam al-Ridha (peace be upon him) said:',
                'main_text_arabic': 'إن ابني هذا يُولد عالماً',
                'main_text_english': 'This son of mine is born learned',
                'source_arabic': 'عيون أخبار الرضا - الشيخ الصدوق',
                'source_english': 'Uyun Akhbar al-Ridha - Sheikh Saduq',
                'duration': 5.5,
                'style': 'hadith_golden',
                'animation': 'sacred_text'
            },

            # Imam's piety and worship
            'virtues_piety': {
                'arabic': 'إمام التقوى والورع',
                'english': 'Imam of Piety and Righteousness',
                'subtitle_arabic': 'كان مثالاً في العبادة والزهد',
                'subtitle_english': 'He was an exemplar in worship and asceticism',
                'duration': 4.5,
                'style': 'spiritual_green',
                'animation': 'spiritual_aura'
            },

            # Another authentic hadith
            'hadith_knowledge': {
                'arabic': 'عن الإمام الجواد عليه السلام:',
                'english': 'Imam al-Jawad (peace be upon him) said:',
                'main_text_arabic': 'العالم إذا لم يعمل بعلمه كان كالجاهل',
                'main_text_english': 'A scholar who does not act upon his knowledge is like an ignorant person',
                'source_arabic': 'بحار الأنوار - العلامة المجلسي',
                'source_english': 'Bihar al-Anwar - Allama Majlisi',
                'duration': 5.5,
                'style': 'hadith_wisdom',
                'animation': 'knowledge_flow'
            },

            # Condolence phrase
            'condolence_phrase': {
                'arabic': 'أحيا الله ذكراكم وأعظم أجوركم',
                'english': 'May Allah revive your remembrance and magnify your reward',
                'subtitle_arabic': 'بمصاب أهل البيت عليهم السلام',
                'subtitle_english': 'In the calamity of Ahl al-Bayt (peace be upon them)',
                'duration': 4.5,
                'style': 'condolence_white',
                'animation': 'heartfelt_fade'
            },

            # Salawat and prayer
            'salawat_prayer': {
                'arabic': 'اللهم صل على محمد وآل محمد',
                'english': 'O Allah, send blessings upon Muhammad and the family of Muhammad',
                'subtitle_arabic': 'وعجل فرجهم الشريف',
                'subtitle_english': 'And hasten their noble reappearance',
                'duration': 4.5,
                'style': 'prayer_purple',
                'animation': 'divine_blessing'
            },

            # Closing prayer
            'closing_prayer': {
                'arabic': 'رحم الله من قرأ الفاتحة',
                'english': 'May Allah have mercy on those who recite Al-Fatiha',
                'subtitle_arabic': 'لروح الإمام محمد الجواد عليه السلام',
                'subtitle_english': 'For the soul of Imam Muhammad al-Jawad (peace be upon him)',
                'duration': 4.5,
                'style': 'closing_golden',
                'animation': 'peaceful_fade'
            },

            # Credits sequence
            'credits': {
                'arabic': 'المصادر الشيعية المعتمدة',
                'english': 'Based on Authentic Shia Sources',
                'sources': [
                    'بحار الأنوار - العلامة المجلسي',
                    'عيون أخبار الرضا - الشيخ الصدوق',
                    'الكافي - الشيخ الكليني',
                    'مناقب آل أبي طالب - ابن شهر آشوب'
                ],
                'sources_english': [
                    'Bihar al-Anwar - Allama Majlisi',
                    'Uyun Akhbar al-Ridha - Sheikh Saduq',
                    'Al-Kafi - Sheikh Kulayni',
                    'Manaqib Aal Abi Talib - Ibn Shahr Ashub'
                ],
                'duration': 5.0,
                'style': 'credits_elegant',
                'animation': 'scroll_credits'
            }
        }

    def _prepare_quranic_verses(self):
        """Prepare relevant Quranic verses."""
        return {
            'knowledge': {
                'arabic': 'وَقُل رَّبِّ زِدْنِي عِلْمًا',
                'english': 'And say: My Lord, increase me in knowledge',
                'reference': 'Quran 20:114',
                'reference_arabic': 'سورة طه - آية ١١٤'
            },
            'guidance': {
                'arabic': 'وَجَعَلْنَا مِنْهُمْ أَئِمَّةً يَهْدُونَ بِأَمْرِنَا',
                'english': 'And We made from among them leaders guiding by Our command',
                'reference': 'Quran 21:73',
                'reference_arabic': 'سورة الأنبياء - آية ٧٣'
            },
            'patience': {
                'arabic': 'وَاصْبِرْ وَمَا صَبْرُكَ إِلَّا بِاللَّهِ',
                'english': 'And be patient, and your patience is not but through Allah',
                'reference': 'Quran 16:127',
                'reference_arabic': 'سورة النحل - آية ١٢٧'
            }
        }

    def _prepare_authentic_hadith(self):
        """Prepare authentic hadith from reliable Shia sources."""
        return {
            'birth_prophecy': {
                'arabic': 'إن ابني هذا يُولد عالماً',
                'english': 'This son of mine is born learned',
                'narrator': 'الإمام الرضا عليه السلام',
                'narrator_english': 'Imam al-Ridha (peace be upon him)',
                'source': 'عيون أخبار الرضا',
                'source_english': 'Uyun Akhbar al-Ridha'
            },
            'knowledge_action': {
                'arabic': 'العالم إذا لم يعمل بعلمه كان كالجاهل',
                'english': 'A scholar who does not act upon his knowledge is like an ignorant person',
                'narrator': 'الإمام الجواد عليه السلام',
                'narrator_english': 'Imam al-Jawad (peace be upon him)',
                'source': 'بحار الأنوار',
                'source_english': 'Bihar al-Anwar'
            },
            'worship': {
                'arabic': 'كان أعبد أهل زمانه',
                'english': 'He was the most devoted worshipper of his time',
                'narrator': 'المؤرخون الشيعة',
                'narrator_english': 'Shia Historians',
                'source': 'مناقب آل أبي طالب',
                'source_english': 'Manaqib Aal Abi Talib'
            }
        }

    def _prepare_historical_dates(self):
        """Prepare accurate historical dates."""
        return {
            'birth': {
                'hijri': '١٠ رجب ١٩٥ هـ',
                'hijri_english': '10 Rajab 195 AH',
                'gregorian': '٨١١ ميلادية',
                'gregorian_english': '811 CE',
                'location_arabic': 'المدينة المنورة',
                'location_english': 'Medina'
            },
            'martyrdom': {
                'hijri': '٢٩ ذو القعدة ٢٢٠ هـ',
                'hijri_english': '29 Dhul Qi\'dah 220 AH',
                'gregorian': '٨٣٥ ميلادية',
                'gregorian_english': '835 CE',
                'location_arabic': 'بغداد',
                'location_english': 'Baghdad'
            },
            'age_at_martyrdom': {
                'arabic': '٢٥ سنة',
                'english': '25 years old'
            }
        }

    def get_duration_variants(self):
        """Get different duration variants for the video."""
        return {
            'short': {  # 30 seconds
                'scenes': ['title_sequence', 'imam_full_name', 'martyrdom_dates',
                          'condolence_phrase', 'salawat_prayer'],
                'total_duration': 30
            },
            'medium': {  # 60 seconds
                'scenes': ['title_sequence', 'opening_bismillah', 'imam_full_name',
                          'martyrdom_dates', 'virtues_knowledge', 'hadith_ridha',
                          'condolence_phrase', 'salawat_prayer', 'closing_prayer'],
                'total_duration': 60
            },
            'full': {  # 90+ seconds
                'scenes': list(self.content.keys()),
                'total_duration': 90
            }
        }

    def get_aspect_ratio_configs(self):
        """Get configurations for different aspect ratios."""
        return {
            '1:1': {'resolution': (1080, 1080), 'name': 'Square'},
            '16:9': {'resolution': (1920, 1080), 'name': 'Widescreen'},
            '9:16': {'resolution': (1080, 1920), 'name': 'Vertical (Stories)'},
            '4:5': {'resolution': (1080, 1350), 'name': 'Facebook'},
            '2:3': {'resolution': (1080, 1620), 'name': 'Pinterest'}
        }

    def get_enhanced_styles(self):
        """Get enhanced visual styles with gradients and textures."""
        return {
            'title_elegant': {
                'bg_gradient': [(0, 0, 0), (20, 20, 40)],
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (139, 69, 19),
                'text_shadow': True,
                'border_style': 'ornate'
            },
            'elegant': {
                'bg_gradient': [(0, 0, 0), (10, 10, 10)],
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (139, 69, 19),
                'text_shadow': True,
                'border_style': 'classic'
            },
            'highlighted_gold': {
                'bg_gradient': [(0, 0, 0), (25, 15, 0)],
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (255, 165, 0),
                'text_glow': True,
                'border_style': 'golden'
            },
            'quranic_verse': {
                'bg_gradient': [(10, 20, 40), (20, 40, 80)],
                'primary_color': (255, 255, 255),
                'secondary_color': (200, 220, 255),
                'accent_color': (100, 149, 237),
                'divine_glow': True,
                'border_style': 'divine'
            },
            'wisdom_blue': {
                'bg_gradient': [(0, 20, 40), (10, 30, 60)],
                'primary_color': (255, 215, 0),
                'secondary_color': (200, 200, 255),
                'accent_color': (100, 149, 237),
                'text_shadow': True,
                'border_style': 'wisdom'
            },
            'spiritual_green': {
                'bg_gradient': [(0, 25, 0), (10, 40, 10)],
                'primary_color': (255, 255, 255),
                'secondary_color': (144, 238, 144),
                'accent_color': (34, 139, 34),
                'spiritual_aura': True,
                'border_style': 'natural'
            },
            'hadith_golden': {
                'bg_gradient': [(40, 20, 0), (60, 30, 0)],
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 228, 181),
                'accent_color': (205, 133, 63),
                'text_glow': True,
                'border_style': 'manuscript'
            },
            'memorial_dates': {
                'bg_gradient': [(20, 20, 20), (40, 40, 40)],
                'primary_color': (180, 180, 180),
                'secondary_color': (255, 255, 255),
                'accent_color': (139, 69, 19),
                'text_shadow': True,
                'border_style': 'memorial'
            }
        }

if __name__ == "__main__":
    # Test the enhanced content
    content = EnhancedImamJawadContent()
    print("Enhanced Imam al-Jawad Content Loaded Successfully")
    print(f"Total scenes: {len(content.content)}")
    print(f"Quranic verses: {len(content.quranic_verses)}")
    print(f"Authentic hadith: {len(content.authentic_hadith)}")
    print(f"Duration variants: {list(content.get_duration_variants().keys())}")
    print(f"Aspect ratios: {list(content.get_aspect_ratio_configs().keys())}")
