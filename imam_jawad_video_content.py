#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Imam <PERSON> (AS) Condolence Video Content
Contains authentic Shia Islamic content for the martyrdom commemoration video.
Based on reliable Shia sources and traditions.
"""

from datetime import datetime

class ImamJawadContent:
    """Content manager for <PERSON> condolence video with authentic Shia sources."""
    
    def __init__(self):
        self.content = self._prepare_content()
        
    def _prepare_content(self):
        """Prepare authentic content based on Shia Islamic sources."""
        
        return {
            # Scene 1: Opening with Bismillah
            'opening': {
                'arabic': 'بسم الله الرحمن الرحيم',
                'translation': 'In the Name of Allah, the Most Gracious, the Most Merciful',
                'duration': 3.0,
                'style': 'elegant'
            },
            
            # Scene 2: Condolence announcement
            'condolence_announcement': {
                'arabic': 'بمناسبة ذكرى استشهاد',
                'translation': 'On the occasion of the martyrdom anniversary',
                'duration': 3.0,
                'style': 'formal'
            },
            
            # Scene 3: <PERSON>'s name and title
            'imam_name': {
                'arabic': 'الإما<PERSON> محمد الجواد عليه السلام',
                'translation': 'Imam <PERSON> al-Jawad (peace be upon him)',
                'subtitle': 'التاسع من أئمة أهل البيت عليهم السلام',
                'subtitle_translation': 'The Ninth Imam of Ahl al-Bayt (peace be upon them)',
                'duration': 4.0,
                'style': 'highlighted'
            },
            
            # Scene 4: Martyrdom date
            'martyrdom_date': {
                'arabic': 'التاسع والعشرون من ذي القعدة',
                'translation': '29th of Dhul Qi\'dah',
                'subtitle': 'سنة ٢٢٠ للهجرة',
                'subtitle_translation': '220 AH',
                'duration': 3.5,
                'style': 'memorial'
            },
            
            # Scene 5: Imam's virtues - Knowledge
            'virtues_knowledge': {
                'arabic': 'إمام العلم والحكمة',
                'translation': 'Imam of Knowledge and Wisdom',
                'subtitle': 'أجاب على المسائل وهو في التاسعة من عمره',
                'subtitle_translation': 'He answered scholarly questions at the age of nine',
                'duration': 4.0,
                'style': 'wisdom'
            },
            
            # Scene 6: Imam's virtues - Piety
            'virtues_piety': {
                'arabic': 'إمام التقوى والورع',
                'translation': 'Imam of Piety and Righteousness',
                'subtitle': 'كان مثالاً في العبادة والزهد',
                'subtitle_translation': 'He was an example in worship and asceticism',
                'duration': 4.0,
                'style': 'spiritual'
            },
            
            # Scene 7: Hadith about the Imam
            'hadith': {
                'arabic': 'قال الإمام الرضا عليه السلام:',
                'translation': 'Imam al-Ridha (peace be upon him) said:',
                'main_text': 'إن ابني هذا يُولد عالماً',
                'main_translation': 'This son of mine is born learned',
                'duration': 4.5,
                'style': 'hadith'
            },
            
            # Scene 8: Condolence phrase
            'condolence_phrase': {
                'arabic': 'أحيا الله ذكراكم وأعظم أجوركم',
                'translation': 'May Allah revive your remembrance and magnify your reward',
                'subtitle': 'بمصاب أهل البيت عليهم السلام',
                'subtitle_translation': 'In the calamity of Ahl al-Bayt (peace be upon them)',
                'duration': 4.0,
                'style': 'condolence'
            },
            
            # Scene 9: Prayer for the Imam
            'salawat': {
                'arabic': 'اللهم صل على محمد وآل محمد',
                'translation': 'O Allah, send blessings upon Muhammad and the family of Muhammad',
                'subtitle': 'وعجل فرجهم الشريف',
                'subtitle_translation': 'And hasten their noble reappearance',
                'duration': 4.0,
                'style': 'prayer'
            },
            
            # Scene 10: Closing
            'closing': {
                'arabic': 'رحم الله من قرأ الفاتحة',
                'translation': 'May Allah have mercy on those who recite Al-Fatiha',
                'subtitle': 'لروح الإمام محمد الجواد عليه السلام',
                'subtitle_translation': 'For the soul of Imam Muhammad al-Jawad (peace be upon him)',
                'duration': 4.0,
                'style': 'closing'
            }
        }
    
    def get_scene_content(self, scene_name):
        """Get content for a specific scene."""
        return self.content.get(scene_name, {})
    
    def get_all_scenes(self):
        """Get all scene names in order."""
        return list(self.content.keys())
    
    def get_total_duration(self):
        """Calculate total video duration."""
        return sum(scene.get('duration', 3.0) for scene in self.content.values())
    
    def get_scene_styles(self):
        """Get available visual styles for scenes."""
        return {
            'elegant': {
                'bg_color': (0, 0, 0),
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (139, 69, 19)
            },
            'formal': {
                'bg_color': (25, 25, 25),
                'primary_color': (200, 200, 200),
                'secondary_color': (255, 255, 255),
                'accent_color': (100, 100, 100)
            },
            'highlighted': {
                'bg_color': (0, 0, 0),
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (255, 165, 0)
            },
            'memorial': {
                'bg_color': (20, 20, 20),
                'primary_color': (180, 180, 180),
                'secondary_color': (255, 255, 255),
                'accent_color': (139, 69, 19)
            },
            'wisdom': {
                'bg_color': (0, 20, 40),
                'primary_color': (255, 215, 0),
                'secondary_color': (200, 200, 255),
                'accent_color': (100, 149, 237)
            },
            'spiritual': {
                'bg_color': (0, 25, 0),
                'primary_color': (255, 255, 255),
                'secondary_color': (144, 238, 144),
                'accent_color': (34, 139, 34)
            },
            'hadith': {
                'bg_color': (40, 20, 0),
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 228, 181),
                'accent_color': (205, 133, 63)
            },
            'condolence': {
                'bg_color': (0, 0, 0),
                'primary_color': (255, 255, 255),
                'secondary_color': (192, 192, 192),
                'accent_color': (139, 69, 19)
            },
            'prayer': {
                'bg_color': (25, 0, 25),
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (147, 112, 219)
            },
            'closing': {
                'bg_color': (0, 0, 0),
                'primary_color': (255, 215, 0),
                'secondary_color': (255, 255, 255),
                'accent_color': (139, 69, 19)
            }
        }
    
    def get_branding_info(self):
        """Get AliToucan branding information."""
        return {
            'watermark_text': 'AliToucan',
            'watermark_color': (128, 128, 128),
            'watermark_position': 'bottom_right',
            'watermark_size': 20
        }
    
    def get_video_metadata(self):
        """Get video metadata and specifications."""
        return {
            'title': 'ذكرى استشهاد الإمام محمد الجواد عليه السلام',
            'title_en': 'Martyrdom Anniversary of Imam Muhammad al-Jawad (AS)',
            'description': 'فيديو تعزية بمناسبة ذكرى استشهاد الإمام التاسع محمد الجواد عليه السلام',
            'description_en': 'Condolence video for the martyrdom anniversary of the 9th Imam Muhammad al-Jawad (AS)',
            'tags': ['إمام_الجواد', 'أهل_البيت', 'شيعة', 'تعزية', 'ذكرى_استشهاد'],
            'tags_en': ['Imam_Jawad', 'Ahl_al_Bayt', 'Shia', 'Condolence', 'Martyrdom_Anniversary'],
            'duration': self.get_total_duration(),
            'aspect_ratios': ['1:1', '16:9'],
            'resolutions': {
                '1:1': (1080, 1080),
                '16:9': (1920, 1080)
            }
        }

# Historical and religious notes based on Shia sources
HISTORICAL_NOTES = """
Historical Information about Imam Muhammad al-Jawad (AS):

1. Birth: 10th Rajab 195 AH in Medina
2. Martyrdom: 29th Dhul Qi'dah 220 AH in Baghdad
3. Age at martyrdom: 25 years
4. Father: Imam Ali al-Ridha (AS)
5. Mother: Sabika (also known as Khayzuran)
6. Children: Imam Ali al-Hadi (AS) and others
7. Known for: Exceptional knowledge from childhood, piety, and wisdom
8. Cause of martyrdom: Poisoned by the Abbasid Caliph al-Mu'tasim

Sources: 
- Bihar al-Anwar by Allama Majlisi
- Al-Kafi by Sheikh Kulayni
- Manaqib Aal Abi Talib by Ibn Shahr Ashub
- Uyun Akhbar al-Ridha by Sheikh Saduq
"""

if __name__ == "__main__":
    # Test the content
    content = ImamJawadContent()
    print("Imam al-Jawad Video Content Loaded Successfully")
    print(f"Total scenes: {len(content.get_all_scenes())}")
    print(f"Total duration: {content.get_total_duration():.1f} seconds")
    print("\nScenes:")
    for scene in content.get_all_scenes():
        scene_data = content.get_scene_content(scene)
        print(f"  - {scene}: {scene_data.get('arabic', 'N/A')} ({scene_data.get('duration', 0):.1f}s)")
