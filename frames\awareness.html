<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خور عبدالله عراقي - كل الدلائل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f5f5f5;
        }
        
        .message-container {
            aspect-ratio: 1/1;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            position: relative;
            overflow: hidden;
        }
        
        .message-container::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l-5.373 5.373L48 0l-1.373 1.373L43.254 0l-3.627 3.627L36 0l-1.373 1.373L31.254 0l-3.627 3.627L24 0l-1.373 1.373L19.254 0l-3.627 3.627L12 0l-1.373 1.373L7.254 0 3.627 3.627 0 0v60l3.627-3.627L0 54.627l5.373-5.373L0 48l1.373-1.373L0 43.254l3.627-3.627L0 36l1.373-1.373L0 31.254l3.627-3.627L0 24l1.373-1.373L0 19.254l3.627-3.627L0 12l1.373-1.373L0 7.254 3.627 3.627 0 0h60z' fill='%231e40af' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E");
            opacity: 0.3;
            animation: rotate 60s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .evidence-item {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .evidence-item:hover {
            transform: translateX(-5px);
        }
        
        .evidence-item::before {
            content: "✓";
            position: absolute;
            right: -1.5rem;
            color: #10b981;
            font-weight: bold;
        }
        
        .iraqi-flag {
            position: absolute;
            bottom: 1rem;
            left: 1rem;
            width: 80px;
            height: 50px;
            background: linear-gradient(to bottom, 
                #ce1126 0%, #ce1126 33%, 
                #ffffff 33%, #ffffff 66%, 
                #000000 66%, #000000 100%);
            border: 2px solid white;
            box-shadow: 0 0 10px rgba(0,0,0,0.3);
        }
        
        .iraqi-flag::after {
            content: "الله أكبر";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-lg">
        <div class="message-container rounded-2xl p-8 text-white mx-auto">
            <div class="relative z-10 h-full flex flex-col">
                <div class="text-center mb-6">
                    <h1 class="text-3xl font-bold mb-2">خور عبدالله</h1>
                    <div class="w-24 h-1 bg-green-400 mx-auto"></div>
                </div>
                
                <div class="flex-1 flex flex-col justify-center">
                    <h2 class="text-2xl font-bold mb-6 text-center">كل الدلائل تؤكد عراقية خور عبدالله</h2>
                    
                    <div class="space-y-4 text-lg">
                        <div class="evidence-item">الخرائط التاريخية منذ العهد العثماني</div>
                        <div class="evidence-item">اتفاقية الحدود العراقية الكويتية 1932</div>
                        <div class="evidence-item">قرارات الأمم المتحدة ذات الصلة</div>
                        <div class="evidence-item">الوثائق الدبلوماسية البريطانية</div>
                        <div class="evidence-item">المراسيم الملكية العراقية</div>
                        <div class="evidence-item">الدراسات الهيدروغرافية</div>
                        <div class="evidence-item">الخرائط العسكرية البريطانية</div>
                        <div class="evidence-item">المراسلات الرسمية بين الدولتين</div>
                    </div>
                </div>
                
                <div class="mt-6 text-center text-sm opacity-80">
                    <p>السيادة العراقية على خور عبدالله ثابتة تاريخياً وقانونياً</p>
                </div>
                
                <div class="iraqi-flag"></div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <button onclick="downloadMessage()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-8 rounded-full transition duration-200 flex items-center space-x-2 mx-auto">
                <i class="fas fa-download"></i>
                <span>حفظ الرسالة</span>
            </button>
        </div>
    </div>

    <script>
        function downloadMessage() {
            // In a real implementation, this would convert the message to an image and download it
            alert("في التطبيق الحقيقي، سيتم حفظ الرسالة كصورة مربعة (1:1)");
        }
    </script>
</body>
</html>