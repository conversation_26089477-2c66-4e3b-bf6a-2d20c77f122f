#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced <PERSON> (AS) Condolence Design Generator
Features improved Arabic text rendering, better typography, and enhanced visual quality.
"""

from PIL import Image, ImageDraw, ImageFont
import os
from enhanced_arabic_renderer import ArabicTextR<PERSON><PERSON>

def create_enhanced_imam_jawad_design(background_color=(0, 0, 0), primary_color=(255, 215, 0),
                                     design_name="enhanced_imam_jawad", text_effects="shadow"):
    """
    Create an enhanced condolence design for <PERSON> (AS) with superior Arabic text rendering.

    Args:
        background_color: RGB tuple for background
        primary_color: RGB tuple for primary text
        design_name: Name for the design
        text_effects: "shadow", "outline", or "none"
    """
    size = 1080  # 1:1 aspect ratio for social media
    secondary_color = (255, 255, 255)  # White
    accent_color = (139, 69, 19)  # Bronze/brown

    # Create image with high quality
    img = Image.new('RGB', (size, size), background_color)
    draw = ImageDraw.Draw(img)

    # Initialize enhanced Arabic renderer
    arabic_renderer = ArabicTextRenderer()

    # Enhanced font sizes for better readability
    font_sizes = {
        'title': 52,      # Increased from 48
        'main': 40,       # Increased from 36
        'subtitle': 32,   # Increased from 28
        'watermark': 22   # Increased from 20
    }

    # Get fonts with Arabic support
    title_font, title_font_name = arabic_renderer.get_font(font_sizes['title'])
    main_font, main_font_name = arabic_renderer.get_font(font_sizes['main'])
    subtitle_font, subtitle_font_name = arabic_renderer.get_font(font_sizes['subtitle'])
    watermark_font, watermark_font_name = arabic_renderer.get_font(font_sizes['watermark'])

    print(f"Using fonts: Title={title_font_name}, Main={main_font_name}, Subtitle={subtitle_font_name}")

    # Authentic Arabic text content for Imam al-Jawad (AS)
    texts = {
        'bismillah': 'بسم الله الرحمن الرحيم',
        'condolence_intro': 'بمناسبة ذكرى استشهاد',
        'imam_name': 'الإمام محمد الجواد عليه السلام',
        'date': 'التاسع والعشرون من ذي القعدة',
        'condolence': 'أحيا الله ذكراكم وأعظم أجوركم',
        'salawat': 'اللهم صل على محمد وآل محمد',
        'watermark': 'AliToucan'
    }

    # Enhanced layout with better spacing
    y_positions = {
        'bismillah': 100,      # Moved up slightly
        'condolence_intro': 200,
        'imam_name': 290,      # More space for larger font
        'date': 420,
        'condolence': 520,
        'salawat': 620,
        'line': 720           # Moved down for better spacing
    }

    # Draw enhanced decorative borders with gradient effect
    border_width = 10  # Increased border width

    # Outer border
    draw.rectangle([border_width, border_width, size-border_width, size-border_width],
                  outline=primary_color, width=4)

    # Inner decorative frame with enhanced styling
    inner_margin = 50  # Reduced for more text space
    draw.rectangle([inner_margin, inner_margin, size-inner_margin, size-inner_margin],
                  outline=accent_color, width=3)

    # Additional decorative elements
    corner_margin = 80
    corner_size = 30
    corners = [
        (corner_margin, corner_margin),
        (size - corner_margin, corner_margin),
        (corner_margin, size - corner_margin),
        (size - corner_margin, size - corner_margin)
    ]

    # Enhanced corner decorations
    for corner_x, corner_y in corners:
        # Outer circle
        draw.ellipse([corner_x - corner_size, corner_y - corner_size,
                     corner_x + corner_size, corner_y + corner_size],
                    outline=primary_color, width=2)
        # Inner circle
        draw.ellipse([corner_x - corner_size//2, corner_y - corner_size//2,
                     corner_x + corner_size//2, corner_y + corner_size//2],
                    outline=accent_color, width=1)

    # Configure text effects
    shadow_config = {
        'shadow': True,
        'shadow_color': (0, 0, 0),  # Black shadow
        'shadow_offset': (3, 3)
    }

    outline_config = {
        'outline': True,
        'outline_color': (0, 0, 0),
        'outline_width': 2
    }

    no_effects = {'shadow': False, 'outline': False}

    effect_config = shadow_config if text_effects == "shadow" else \
                   outline_config if text_effects == "outline" else no_effects

    # Draw enhanced Arabic text with proper RTL rendering
    print("Rendering Arabic text...")

    # Bismillah (In the name of Allah)
    arabic_renderer.draw_centered_arabic_text(
        draw, texts['bismillah'], y_positions['bismillah'], size,
        subtitle_font, secondary_color, **effect_config
    )

    # Main condolence introduction
    arabic_renderer.draw_centered_arabic_text(
        draw, texts['condolence_intro'], y_positions['condolence_intro'], size,
        main_font, primary_color, **effect_config
    )

    # Imam's name (most prominent)
    arabic_renderer.draw_centered_arabic_text(
        draw, texts['imam_name'], y_positions['imam_name'], size,
        title_font, primary_color, **effect_config
    )

    # Date of martyrdom
    arabic_renderer.draw_centered_arabic_text(
        draw, texts['date'], y_positions['date'], size,
        subtitle_font, secondary_color, **effect_config
    )

    # Condolence phrase
    arabic_renderer.draw_centered_arabic_text(
        draw, texts['condolence'], y_positions['condolence'], size,
        main_font, primary_color, **effect_config
    )

    # Salawat (Prayers)
    arabic_renderer.draw_centered_arabic_text(
        draw, texts['salawat'], y_positions['salawat'], size,
        subtitle_font, secondary_color, **effect_config
    )

    # Enhanced decorative line
    line_y = y_positions['line']
    line_start = size // 3
    line_end = 2 * size // 3

    # Main line
    draw.line([(line_start, line_y), (line_end, line_y)], fill=accent_color, width=4)

    # Decorative dots at line ends
    dot_radius = 6
    draw.ellipse([line_start - dot_radius, line_y - dot_radius,
                 line_start + dot_radius, line_y + dot_radius],
                fill=primary_color)
    draw.ellipse([line_end - dot_radius, line_y - dot_radius,
                 line_end + dot_radius, line_y + dot_radius],
                fill=primary_color)

    # Enhanced watermark with better positioning
    watermark_x = size - 180
    watermark_y = size - 50

    # Watermark background for better visibility
    watermark_bg_size = 120
    draw.rectangle([watermark_x - 10, watermark_y - 10,
                   watermark_x + watermark_bg_size, watermark_y + 30],
                  fill=(0, 0, 0, 50))  # Semi-transparent background

    draw.text((watermark_x, watermark_y), texts['watermark'],
              font=watermark_font, fill=(128, 128, 128))

    print("✓ Enhanced design created successfully")
    return img

def save_enhanced_design(img, filename):
    """Save enhanced design with optimized quality settings."""
    output_dir = "enhanced_imam_jawad_designs"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    saved_files = []

    # Save as high-quality PNG
    png_path = os.path.join(output_dir, f"{filename}.png")
    img.save(png_path, "PNG", optimize=True)
    saved_files.append(png_path)

    # Save as high-quality JPG
    jpg_path = os.path.join(output_dir, f"{filename}.jpg")
    rgb_img = Image.new('RGB', img.size, (0, 0, 0))
    rgb_img.paste(img)
    rgb_img.save(jpg_path, "JPEG", quality=98, optimize=True)  # Higher quality
    saved_files.append(jpg_path)

    return saved_files

def main():
    """Generate enhanced Imam al-Jawad condolence designs with superior Arabic rendering."""
    print("🕌 Enhanced Imam al-Jawad (AS) Condolence Design Generator")
    print("=" * 60)
    print("Features: Enhanced Arabic rendering, RTL support, improved typography")
    print()

    # Enhanced design variations with different text effects
    designs = [
        {
            'name': 'enhanced_imam_jawad_shadow_classic',
            'bg_color': (0, 0, 0),
            'primary_color': (255, 215, 0),
            'effects': 'shadow',
            'description': 'Classic Black & Gold with Text Shadows'
        },
        {
            'name': 'enhanced_imam_jawad_outline_elegant',
            'bg_color': (25, 25, 25),
            'primary_color': (200, 200, 200),
            'effects': 'outline',
            'description': 'Elegant Dark Gray & Silver with Text Outlines'
        },
        {
            'name': 'enhanced_imam_jawad_shadow_traditional',
            'bg_color': (0, 0, 0),
            'primary_color': (255, 255, 255),
            'effects': 'shadow',
            'description': 'Traditional Black & White with Shadows'
        },
        {
            'name': 'enhanced_imam_jawad_outline_royal',
            'bg_color': (20, 20, 40),
            'primary_color': (255, 215, 0),
            'effects': 'outline',
            'description': 'Royal Navy & Gold with Text Outlines'
        }
    ]

    all_files = []

    for design in designs:
        print(f"\n🎨 Creating {design['description']}...")

        img = create_enhanced_imam_jawad_design(
            background_color=design['bg_color'],
            primary_color=design['primary_color'],
            design_name=design['name'],
            text_effects=design['effects']
        )

        saved_files = save_enhanced_design(img, design['name'])
        all_files.extend(saved_files)

        for file_path in saved_files:
            file_size = os.path.getsize(file_path) / 1024
            print(f"  ✓ {file_path} ({file_size:.1f} KB)")

    print("\n" + "=" * 60)
    print("🎉 Enhanced Designs Created Successfully!")
    print(f"📁 Total files: {len(all_files)}")

    print("\n✨ Enhanced Features:")
    print("  ✓ Proper Arabic RTL text rendering")
    print("  ✓ Arabic text shaping and ligatures")
    print("  ✓ Enhanced font sizes for better readability")
    print("  ✓ Text shadows and outlines for visibility")
    print("  ✓ Improved spacing and typography")
    print("  ✓ Higher quality image output")
    print("  ✓ Enhanced decorative elements")

    print(f"\n📍 Files saved in: {os.path.abspath('enhanced_imam_jawad_designs')}")

    # Try to open first design
    try:
        if all_files:
            first_design = Image.open(all_files[0])
            first_design.show()
            print(f"\n👁️  Preview opened: {all_files[0]}")
    except Exception:
        print("\n📝 Note: Preview not available")

if __name__ == "__main__":
    main()
