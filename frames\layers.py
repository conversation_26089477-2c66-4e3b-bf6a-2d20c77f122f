from PyQt5.QtGui import QColor

class Layer:
    """Class representing a drawing layer"""
    
    def __init__(self, name, color=None):
        self.name = name
        self.color = color or QColor(0, 0, 0)  # Default to black
        self.visible = True
        self.locked = False
        self.opacity = 1.0
        self.items = []  # List of drawing items on this layer
        self.properties = {}  # Additional layer properties
        
    def add_item(self, item):
        """Add an item to this layer"""
        self.items.append(item)
        item.layer = self
        
    def remove_item(self, item):
        """Remove an item from this layer"""
        if item in self.items:
            self.items.remove(item)
            item.layer = None
            
    def clear(self):
        """Remove all items from this layer"""
        for item in self.items:
            item.layer = None
        self.items = []
        
    def get_items(self):
        """Get all items on this layer"""
        return self.items
        
    def set_visible(self, visible):
        """Set layer visibility"""
        self.visible = visible
        
    def set_locked(self, locked):
        """Set layer locked state"""
        self.locked = locked
        
    def set_opacity(self, opacity):
        """Set layer opacity"""
        self.opacity = max(0.0, min(1.0, opacity))
        
    def set_color(self, color):
        """Set layer color"""
        self.color = color
        
    def set_property(self, name, value):
        """Set a layer property"""
        self.properties[name] = value
        
    def get_property(self, name, default=None):
        """Get a layer property"""
        return self.properties.get(name, default)
        
    def to_dict(self):
        """Convert the layer to a dictionary for serialization"""
        return {
            'name': self.name,
            'color': {
                'r': self.color.red(),
                'g': self.color.green(),
                'b': self.color.blue(),
                'a': self.color.alpha()
            },
            'visible': self.visible,
            'locked': self.locked,
            'opacity': self.opacity,
            'properties': self.properties
        }
        
    @classmethod
    def from_dict(cls, data):
        """Create a layer from a dictionary"""
        layer = cls(data['name'])
        layer.color = QColor(
            data['color']['r'],
            data['color']['g'],
            data['color']['b'],
            data['color']['a']
        )
        layer.visible = data['visible']
        layer.locked = data['locked']
        layer.opacity = data['opacity']
        layer.properties = data['properties']
        return layer

class LayerManager:
    """Class for managing drawing layers"""
    
    def __init__(self):
        self.layers = []
        self.active_layer = None
        
    def add_layer(self, layer):
        """Add a layer to the manager"""
        self.layers.append(layer)
        if not self.active_layer:
            self.active_layer = layer
            
    def remove_layer(self, layer):
        """Remove a layer from the manager"""
        if layer in self.layers:
            self.layers.remove(layer)
            
            # Update active layer if it was removed
            if self.active_layer == layer:
                self.active_layer = self.layers[0] if self.layers else None
                
    def get_layer_by_name(self, name):
        """Get a layer by name"""
        for layer in self.layers:
            if layer.name == name:
                return layer
        return None
        
    def get_layer_at(self, index):
        """Get a layer at the given index"""
        if 0 <= index < len(self.layers):
            return self.layers[index]
        return None
        
    def get_layers(self):
        """Get all layers"""
        return self.layers
        
    def get_visible_layers(self):
        """Get all visible layers"""
        return [layer for layer in self.layers if layer.visible]
        
    def get_layer_count(self):
        """Get the number of layers"""
        return len(self.layers)
        
    def set_active_layer(self, layer):
        """Set the active layer"""
        if layer in self.layers:
            self.active_layer = layer
            
    def get_active_layer(self):
        """Get the active layer"""
        return self.active_layer
        
    def move_layer_up(self, layer):
        """Move a layer up in the stack"""
        if layer in self.layers:
            index = self.layers.index(layer)
            if index > 0:
                self.layers[index], self.layers[index-1] = self.layers[index-1], self.layers[index]
                
    def move_layer_down(self, layer):
        """Move a layer down in the stack"""
        if layer in self.layers:
            index = self.layers.index(layer)
            if index < len(self.layers) - 1:
                self.layers[index], self.layers[index+1] = self.layers[index+1], self.layers[index]
                
    def move_to_top(self, layer):
        """Move a layer to the top of the stack"""
        if layer in self.layers:
            self.layers.remove(layer)
            self.layers.insert(0, layer)
            
    def move_to_bottom(self, layer):
        """Move a layer to the bottom of the stack"""
        if layer in self.layers:
            self.layers.remove(layer)
            self.layers.append(layer)
            
    def clear_layers(self):
        """Remove all layers"""
        self.layers = []
        self.active_layer = None
        
    def to_dict(self):
        """Convert all layers to a dictionary for serialization"""
        return {
            'layers': [layer.to_dict() for layer in self.layers],
            'active_layer': self.layers.index(self.active_layer) if self.active_layer else -1
        }
        
    def load_from_dict(self, data):
        """Load layers from a dictionary"""
        self.clear_layers()
        
        for layer_data in data['layers']:
            layer = Layer.from_dict(layer_data)
            self.add_layer(layer)
            
        active_layer_index = data['active_layer']
        if active_layer_index >= 0 and active_layer_index < len(self.layers):
            self.active_layer = self.layers[active_layer_index]
            
    def load_from_project(self, project):
        """Load layers from a project"""
        if hasattr(project, 'layers_data'):
            self.load_from_dict(project.layers_data) 