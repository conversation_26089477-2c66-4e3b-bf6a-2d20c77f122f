#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for enhanced Arabic rendering
"""

from PIL import Image, ImageDraw
from enhanced_arabic_renderer import ArabicTextRenderer
import os

def create_test_design():
    """Create a simple test design to verify Arabic rendering."""
    print("Creating test design...")
    
    # Create image
    size = 1080
    img = Image.new('RGB', (size, size), (0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Initialize Arabic renderer
    renderer = ArabicTextRenderer()
    
    # Get font
    font, font_name = renderer.get_font(48)
    print(f"Using font: {font_name}")
    
    # Test text
    test_text = "بسم الله الرحمن الرحيم"
    
    # Draw text
    result = renderer.draw_centered_arabic_text(
        draw, test_text, 400, size, font, (255, 255, 255),
        shadow=True, shadow_color=(0, 0, 0, 128)
    )
    
    print(f"Text rendered: {result['processed_text']}")
    print(f"Position: {result['position']}")
    print(f"Dimensions: {result['width']}x{result['height']}")
    
    # Save test image
    output_dir = "test_output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    output_path = os.path.join(output_dir, "test_arabic_rendering.png")
    img.save(output_path)
    print(f"Test image saved: {output_path}")
    
    return img

if __name__ == "__main__":
    create_test_design()
