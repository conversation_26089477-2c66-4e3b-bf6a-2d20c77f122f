<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معركة الحيانية ضد أبو الخصيب</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }
        
        body {
            background-color: #1a1a1a;
            color: white;
            height: 100vh;
            touch-action: manipulation;
        }
        
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 10px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            text-align: center;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #333;
            border-radius: 8px;
        }
        
        .score-board {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.5rem;
            background-color: #333;
            padding: 10px;
            border-radius: 8px;
        }
        
        .game-field {
            flex: 1;
            position: relative;
            border: 2px solid #555;
            border-radius: 8px;
            overflow: hidden;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23396b1e"/><rect x="50" y="0" width="50" height="50" fill="%2344802a"/><rect x="0" y="50" width="50" height="50" fill="%2344802a"/></svg>');
            background-size: 40px 40px;
        }
        
        .player {
            position: absolute;
            width: 40px;
            height: 40px;
            background-color: red;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            will-change: transform;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        
        .player.team-hiyaniya {
            background-color: #ff5722;
        }
        
        .player.team-abukhaseeb {
            background-color: #2196f3;
        }
        
        .player.health-boost {
            box-shadow: 0 0 10px #00ff00;
        }
        
        .player.speed-boost {
            box-shadow: 0 0 10px #ffff00;
        }
        
        .player.shielded {
            box-shadow: 0 0 15px #7986cb;
        }
        
        .bullet {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #ffeb3b;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 5;
            box-shadow: 0 0 5px #ffeb3b;
        }
        
        .explosion {
            position: absolute;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 165, 0, 0.8);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 15;
            animation: explode 0.5s forwards;
        }
        
        @keyframes explode {
            0% {
                width: 10px;
                height: 10px;
                opacity: 1;
            }
            100% {
                width: 60px;
                height: 60px;
                opacity: 0;
            }
        }
        
        .power-up {
            position: absolute;
            width: 25px;
            height: 25px;
            border-radius: 5px;
            transform: translate(-50%, -50%);
            z-index: 8;
            animation: pulse 1.5s infinite;
        }
        
        .power-up.health {
            background-color: #4CAF50;
            box-shadow: 0 0 8px #4CAF50;
        }
        
        .power-up.speed {
            background-color: #FFC107;
            box-shadow: 0 0 8px #FFC107;
        }
        
        .power-up.shield {
            background-color: #7986CB;
            box-shadow: 0 0 8px #7986CB;
        }
        
        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.2); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }
        
        .controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 10px;
            margin-top: 20px;
            height: 150px;
        }
        
        .control-btn {
            background-color: #333;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            user-select: none;
            touch-action: manipulation;
        }
        
        .control-btn:active {
            background-color: #555;
        }
        
        .up {
            grid-column: 2;
            grid-row: 1;
        }
        
        .left {
            grid-column: 1;
            grid-row: 2;
        }
        
        .shoot {
            grid-column: 2;
            grid-row: 2;
            background-color: #f44336;
        }
        
        .right {
            grid-column: 3;
            grid-row: 2;
        }
        
        .down {
            grid-column: 2;
            grid-row: 3;
        }
        
        .game-over {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 100;
        }
        
        .game-over h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            color: gold;
        }
        
        .restart-btn {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.2rem;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="header">
            <h1>معركة الحيانية ضد أبو الخصيب</h1>
        </div>
        
        <div class="score-board">
            <div class="team-hiyaniya">الحيانية: <span id="hiyaniya-score">0</span></div>
            <div class="team-abukhaseeb">أبو الخصيب: <span id="abukhaseeb-score">0</span></div>
        </div>
        
        <div class="game-field" id="game-field">
            <!-- Players and bullets will be added dynamically -->
        </div>
        
        <div class="controls">
            <button class="control-btn up" id="up-btn">⬆️</button>
            <button class="control-btn left" id="left-btn">⬅️</button>
            <button class="control-btn shoot" id="shoot-btn">🔥</button>
            <button class="control-btn right" id="right-btn">➡️</button>
            <button class="control-btn down" id="down-btn">⬇️</button>
        </div>
        
        <div class="game-over" id="game-over">
            <h2 id="winner-text">الفائز: الحيانية</h2>
            <button class="restart-btn" id="restart-btn">إعادة اللعب</button>
        </div>
    </div>

    <script>
        // Game configuration
        const config = {
            playerSpeed: 4,                // Reduced player speed
            aiSpeed: 6,                   // Faster AI speed
            playerBulletSpeed: 6,         // Slower player bullets
            aiBulletSpeed: 9,            // Faster AI bullets
            aiUpdateInterval: 300,        // AI updates more frequently
            aiShootChance: 0.3,          // AI shoots more often
            gameTime: 90,                // Longer game time
            scoreToWin: 15,              // More points to win
            respawnTime: 2000,           // Same respawn time
            playerHealth: 1,             // Player has 1 health
            aiHealth: 2                  // AI has 2 health (stronger)
        };
        
        // Game state
        const gameState = {
            playerPosition: { x: 100, y: 100 },
            playerHealth: config.playerHealth,
            aiPlayers: [],
            bullets: [],
            explosions: [],
            powerUps: [],
            scores: {
                hiyaniya: 0,
                abukhaseeb: 0
            },
            gameRunning: false,
            timeLeft: config.gameTime,
            playerTeam: 'hiyaniya',
            pressedKeys: {
                up: false,
                down: false,
                left: false,
                right: false
            },
            lastShootTime: 0,
            shootCooldown: 500,  // Cooldown between shots for player
            level: 1
        };
        
        // DOM Elements
        const gameField = document.getElementById('game-field');
        const hiyaniyaScoreEl = document.getElementById('hiyaniya-score');
        const abukhaseebScoreEl = document.getElementById('abukhaseeb-score');
        const gameOverEl = document.getElementById('game-over');
        const winnerTextEl = document.getElementById('winner-text');
        const restartBtn = document.getElementById('restart-btn');
        
        // Control buttons
        const upBtn = document.getElementById('up-btn');
        const leftBtn = document.getElementById('left-btn');
        const rightBtn = document.getElementById('right-btn');
        const downBtn = document.getElementById('down-btn');
        const shootBtn = document.getElementById('shoot-btn');
        
        // Game functions
        function initGame() {
            // Reset state
            gameState.playerPosition = { 
                x: gameField.offsetWidth * 0.2, 
                y: gameField.offsetHeight / 2 
            };
            gameState.playerHealth = config.playerHealth;
            gameState.aiPlayers = [];
            gameState.bullets = [];
            gameState.explosions = [];
            gameState.powerUps = [];
            gameState.scores = { hiyaniya: 0, abukhaseeb: 0 };
            gameState.timeLeft = config.gameTime;
            gameState.gameRunning = true;
            gameState.level = 1;
            
            // Clear field
            gameField.innerHTML = '';
            
            // Create player
            createPlayer('player', gameState.playerPosition.x, gameState.playerPosition.y, gameState.playerTeam);
            
            // Create AI players - now 4 instead of 3
            for (let i = 0; i < 4; i++) {
                const x = gameField.offsetWidth * 0.8;
                const y = gameField.offsetHeight * (i + 1) / 5;
                const aiPlayer = {
                    id: `ai-${i}`,
                    position: { x, y },
                    team: 'abukhaseeb',
                    alive: true,
                    health: config.aiHealth,
                    lastShootTime: 0
                };
                gameState.aiPlayers.push(aiPlayer);
                createPlayer(aiPlayer.id, x, y, 'abukhaseeb');
            }
            
            // Update scores display
            updateScores();
            
            // Hide game over screen
            gameOverEl.style.display = 'none';
            
            // Start game loop
            requestAnimationFrame(gameLoop);
            
            // Start AI movement
            setInterval(updateAI, config.aiUpdateInterval);
            
            // Spawn power-ups periodically
            setInterval(spawnPowerUp, 10000);
        }
        
        function createPlayer(id, x, y, team) {
            const player = document.createElement('div');
            player.className = `player team-${team}`;
            player.id = id;
            player.style.left = `${x}px`;
            player.style.top = `${y}px`;
            
            // Add initial to player div
            player.textContent = team === 'hiyaniya' ? 'ح' : 'خ';
            
            // Show health for AI players
            if (id !== 'player' && team === 'abukhaseeb') {
                const aiIndex = parseInt(id.split('-')[1]);
                const ai = gameState.aiPlayers[aiIndex];
                if (ai) {
                    player.setAttribute('data-health', ai.health);
                }
            }
            
            gameField.appendChild(player);
            return player;
        }
        
        function createBullet(x, y, direction, team) {
            const bullet = document.createElement('div');
            bullet.className = 'bullet';
            if (team === 'abukhaseeb') {
                bullet.style.backgroundColor = '#00f5ff';
                bullet.style.boxShadow = '0 0 5px #00f5ff';
            }
            bullet.style.left = `${x}px`;
            bullet.style.top = `${y}px`;
            
            gameState.bullets.push({
                element: bullet,
                position: { x, y },
                direction,
                team,
                speed: team === 'hiyaniya' ? config.playerBulletSpeed : config.aiBulletSpeed
            });
            
            gameField.appendChild(bullet);
            
            // Play sound
            playSound(team === 'hiyaniya' ? 'shoot' : 'enemyShoot');
        }
        
        function updatePlayerPosition() {
            // Get current position
            const playerElement = document.getElementById('player');
            if (!playerElement) return;
            
            // Update based on pressed keys
            if (gameState.pressedKeys.up && gameState.playerPosition.y > 20) {
                gameState.playerPosition.y -= config.playerSpeed;
            }
            if (gameState.pressedKeys.down && gameState.playerPosition.y < gameField.offsetHeight - 20) {
                gameState.playerPosition.y += config.playerSpeed;
            }
            if (gameState.pressedKeys.left && gameState.playerPosition.x > 20) {
                gameState.playerPosition.x -= config.playerSpeed;
            }
            if (gameState.pressedKeys.right && gameState.playerPosition.x < gameField.offsetWidth - 20) {
                gameState.playerPosition.x += config.playerSpeed;
            }
            
            // Update DOM
            playerElement.style.left = `${gameState.playerPosition.x}px`;
            playerElement.style.top = `${gameState.playerPosition.y}px`;
        }
        
        function updateBullets() {
            for (let i = gameState.bullets.length - 1; i >= 0; i--) {
                const bullet = gameState.bullets[i];
                
                // Update position with appropriate speed
                bullet.position.x += bullet.direction.x * bullet.speed;
                bullet.position.y += bullet.direction.y * bullet.speed;
                
                // Update DOM
                bullet.element.style.left = `${bullet.position.x}px`;
                bullet.element.style.top = `${bullet.position.y}px`;
                
                // Check if out of bounds
                if (
                    bullet.position.x < 0 || 
                    bullet.position.x > gameField.offsetWidth || 
                    bullet.position.y < 0 || 
                    bullet.position.y > gameField.offsetHeight
                ) {
                    gameField.removeChild(bullet.element);
                    gameState.bullets.splice(i, 1);
                    continue;
                }
                
                // Check collisions with players
                checkBulletCollisions(bullet, i);
            }
        }
        
        function checkBulletCollisions(bullet, bulletIndex) {
            // Check collision with player
            const playerEl = document.getElementById('player');
            if (playerEl && isColliding(bullet.position, gameState.playerPosition, 20)) {
                if (bullet.team !== gameState.playerTeam) {
                    handlePlayerHit('player', gameState.playerTeam);
                    gameField.removeChild(bullet.element);
                    gameState.bullets.splice(bulletIndex, 1);
                    createExplosion(bullet.position.x, bullet.position.y);
                    return;
                }
            }
            
            // Check collision with AI players
            for (let i = 0; i < gameState.aiPlayers.length; i++) {
                const ai = gameState.aiPlayers[i];
                if (!ai.alive) continue;
                
                if (isColliding(bullet.position, ai.position, 20)) {
                    if (bullet.team !== ai.team) {
                        handleAIHit(i);
                        gameField.removeChild(bullet.element);
                        gameState.bullets.splice(bulletIndex, 1);
                        createExplosion(bullet.position.x, bullet.position.y);
                        return;
                    }
                }
            }
            
            // Check collision with power-ups
            for (let i = 0; i < gameState.powerUps.length; i++) {
                const powerUp = gameState.powerUps[i];
                if (isColliding(bullet.position, powerUp.position, 15)) {
                    // Destroy power-up when hit by bullet
                    gameField.removeChild(powerUp.element);
                    gameState.powerUps.splice(i, 1);
                    gameField.removeChild(bullet.element);
                    gameState.bullets.splice(bulletIndex, 1);
                    createExplosion(bullet.position.x, bullet.position.y);
                    return;
                }
            }
        }
        
        function isColliding(pos1, pos2, radius) {
            const dx = pos1.x - pos2.x;
            const dy = pos1.y - pos2.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            return distance < radius;
        }
        
        function handlePlayerHit(playerId, team) {
            gameState.playerHealth--;
            
            // Only count score and respawn if player is actually dead
            if (gameState.playerHealth <= 0) {
                // Increment score for the opposite team
                if (team === 'hiyaniya') {
                    gameState.scores.abukhaseeb++;
                } else {
                    gameState.scores.hiyaniya++;
                }
                
                updateScores();
                
                // Hide the hit player
                const playerEl = document.getElementById(playerId);
                if (playerEl) {
                    playerEl.style.display = 'none';
                }
                
                // Create explosion
                createExplosion(gameState.playerPosition.x, gameState.playerPosition.y);
                
                // Play sound
                playSound('playerDeath');
                
                // Respawn player
                setTimeout(respawnPlayer, config.respawnTime);
                
                // Check if the game is over
                checkGameOver();
            } else {
                // Player hit but not dead - flash red
                const playerEl = document.getElementById(playerId);
                if (playerEl) {
                    playerEl.style.backgroundColor = '#ff0000';
                    setTimeout(() => {
                        playerEl.style.backgroundColor = '#ff5722';
                    }, 200);
                }
                
                // Play hit sound
                playSound('playerHit');
            }
        }
        
        function handleAIHit(aiIndex) {
            const ai = gameState.aiPlayers[aiIndex];
            ai.health--;
            
            // Update AI health display
            const aiEl = document.getElementById(ai.id);
            if (aiEl) {
                aiEl.setAttribute('data-health', ai.health);
            }
            
            // Play hit sound
            playSound('enemyHit');
            
            // AI is dead
            if (ai.health <= 0) {
                // Increment score
                gameState.scores.hiyaniya++;
                updateScores();
                
                // Hide the hit AI
                if (aiEl) {
                    aiEl.style.display = 'none';
                }
                
                // Mark AI as dead
                ai.alive = false;
                
                // Create explosion
                createExplosion(ai.position.x, ai.position.y);
                
                // Play death sound
                playSound('enemyDeath');
                
                // Respawn AI after delay
                setTimeout(() => respawnAI(aiIndex), config.respawnTime);
                
                // Check if game is over
                checkGameOver();
            } else {
                // AI hit but not dead - flash
                if (aiEl) {
                    aiEl.style.backgroundColor = '#ff0000';
                    setTimeout(() => {
                        aiEl.style.backgroundColor = '#2196f3';
                    }, 200);
                }
            }
        }
        
        function respawnPlayer() {
            gameState.playerPosition = {
                x: gameField.offsetWidth * 0.2,
                y: gameField.offsetHeight / 2
            };
            gameState.playerHealth = config.playerHealth;
            
            const playerEl = document.getElementById('player');
            if (playerEl) {
                playerEl.style.display = 'flex';
                playerEl.style.left = `${gameState.playerPosition.x}px`;
                playerEl.style.top = `${gameState.playerPosition.y}px`;
            }
            
            // Temporary invulnerability effect
            if (playerEl) {
                playerEl.style.opacity = '0.5';
                setTimeout(() => {
                    playerEl.style.opacity = '1';
                }, 1500);
            }
        }
        
        function respawnAI(index) {
            const ai = gameState.aiPlayers[index];
            ai.alive = true;
            ai.health = config.aiHealth;
            ai.position = {
                x: gameField.offsetWidth * 0.8,
                y: gameField.offsetHeight * (index + 1) / 5
            };
            
            const aiEl = document.getElementById(ai.id);
            if (aiEl) {
                aiEl.style.display = 'flex';
                aiEl.style.left = `${ai.position.x}px`;
                aiEl.style.top = `${ai.position.y}px`;
                aiEl.setAttribute('data-health', ai.health);
            }
        }
        
        function updateAI() {
            if (!gameState.gameRunning) return;
            
            gameState.aiPlayers.forEach(ai => {
                if (!ai.alive) return;
                
                // AI follows player with some randomness
                const dx = gameState.playerPosition.x - ai.position.x;
                const dy = gameState.playerPosition.y - ai.position.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                // Movement speed based on distance
                let speed = config.aiSpeed;
                
                // If AI is close to player, sometimes move away
                if (distance < 100 && Math.random() < 0.4) {
                    ai.position.x += (dx / distance) * -speed;
                    ai.position.y += (dy / distance) * -speed;
                } else {
                    // Move towards player with some randomness
                    const randomX = (Math.random() - 0.5) * 20;
                    const randomY = (Math.random() - 0.5) * 20;
                    
                    ai.position.x += (dx / distance) * speed * 0.5 + randomX * 0.2;
                    ai.position.y += (dy / distance) * speed * 0.5 + randomY * 0.2;
                }
                
                // Ensure AI stays within bounds
                ai.position.x = Math.max(20, Math.min(gameField.offsetWidth - 20, ai.position.x));
                ai.position.y = Math.max(20, Math.min(gameField.offsetHeight - 20, ai.position.y));
                
                // Update position in DOM
                const aiEl = document.getElementById(ai.id);
                if (aiEl) {
                    aiEl.style.left = `${ai.position.x}px`;
                    aiEl.style.top = `${ai.position.y}px`;
                }
                
                // Shoot at player with cooldown
                const now = Date.now();
                if (now - ai.lastShootTime > 1000 && Math.random() < config.aiShootChance) {
                    const direction = calculateDirection(ai.position, gameState.playerPosition);
                    createBullet(ai.position.x, ai.position.y, direction, ai.team);
                    ai.lastShootTime = now;
                }
            });
        }
        
        function calculateDirection(from, to) {
            const dx = to.x - from.x;
            const dy = to.y - from.y;
            const length = Math.sqrt(dx * dx + dy * dy);
            
            return {
                x: dx / length,
                y: dy / length
            };
        }
        
        function playerShoot() {
            if (!gameState.gameRunning) return;
            
            const now = Date.now();
            if (now - gameState.lastShootTime < gameState.shootCooldown) {
                return; // Still in cooldown
            }
            
            // Update last shoot time
            gameState.lastShootTime = now;
            
            // Player can shoot in any direction they're moving
            let direction = { x: 1, y: 0 }; // Default right
            
            if (gameState.pressedKeys.up && !gameState.pressedKeys.down) {
                if (gameState.pressedKeys.left) direction = { x: -0.7, y: -0.7 };
                else if (gameState.pressedKeys.right) direction = { x: 0.7, y: -0.7 };
                else direction = { x: 0, y: -1 };
            } else if (gameState.pressedKeys.down && !gameState.pressedKeys.up) {
                if (gameState.pressedKeys.left) direction = { x: -0.7, y: 0.7 };
                else if (gameState.pressedKeys.right) direction = { x: 0.7, y: 0.7 };
                else direction = { x: 0, y: 1 };
            } else if (gameState.pressedKeys.left && !gameState.pressedKeys.right) {
                direction = { x: -1, y: 0 };
            }
            
            createBullet(
                gameState.playerPosition.x, 
                gameState.playerPosition.y, 
                direction, 
                gameState.playerTeam
            );
        }
        
        function updateScores() {
            hiyaniyaScoreEl.textContent = gameState.scores.hiyaniya;
            abukhaseebScoreEl.textContent = gameState.scores.abukhaseeb;
        }
        
        function checkGameOver() {
            if (
                gameState.scores.hiyaniya >= config.scoreToWin || 
                gameState.scores.abukhaseeb >= config.scoreToWin
            ) {
                endGame();
            }
        }
        
        function endGame() {
            gameState.gameRunning = false;
            
            // Determine winner
            let winner = 'تعادل';
            if (gameState.scores.hiyaniya > gameState.scores.abukhaseeb) {
                winner = 'الحيانية';
            } else if (gameState.scores.abukhaseeb > gameState.scores.hiyaniya) {
                winner = 'أبو الخصيب';
            }
            
            winnerTextEl.textContent = `الفائز: ${winner}`;
            gameOverEl.style.display = 'flex';
        }
        
        function gameLoop() {
            if (!gameState.gameRunning) return;
            
            updatePlayerPosition();
            updateBullets();
            checkPowerUpCollection();
            updateExplosions();
            
            requestAnimationFrame(gameLoop);
        }
        
        // Event listeners
        upBtn.addEventListener('touchstart', () => { gameState.pressedKeys.up = true; });
        upBtn.addEventListener('touchend', () => { gameState.pressedKeys.up = false; });
        
        downBtn.addEventListener('touchstart', () => { gameState.pressedKeys.down = true; });
        downBtn.addEventListener('touchend', () => { gameState.pressedKeys.down = false; });
        
        leftBtn.addEventListener('touchstart', () => { gameState.pressedKeys.left = true; });
        leftBtn.addEventListener('touchend', () => { gameState.pressedKeys.left = false; });
        
        rightBtn.addEventListener('touchstart', () => { gameState.pressedKeys.right = true; });
        rightBtn.addEventListener('touchend', () => { gameState.pressedKeys.right = false; });
        
        shootBtn.addEventListener('touchstart', playerShoot);
        shootBtn.addEventListener('click', playerShoot);
        
        // Keyboard controls for testing on desktop
        window.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowUp') gameState.pressedKeys.up = true;
            if (e.key === 'ArrowDown') gameState.pressedKeys.down = true;
            if (e.key === 'ArrowLeft') gameState.pressedKeys.left = true;
            if (e.key === 'ArrowRight') gameState.pressedKeys.right = true;
            if (e.key === ' ') playerShoot();
        });
        
        window.addEventListener('keyup', (e) => {
            if (e.key === 'ArrowUp') gameState.pressedKeys.up = false;
            if (e.key === 'ArrowDown') gameState.pressedKeys.down = false;
            if (e.key === 'ArrowLeft') gameState.pressedKeys.left = false;
            if (e.key === 'ArrowRight') gameState.pressedKeys.right = false;
        });
        
        restartBtn.addEventListener('click', initGame);
        
        // Start the game
        window.addEventListener('load', initGame);
        
        // Create explosion effect
        function createExplosion(x, y) {
            const explosion = document.createElement('div');
            explosion.className = 'explosion';
            explosion.style.left = `${x}px`;
            explosion.style.top = `${y}px`;
            
            gameField.appendChild(explosion);
            
            // Remove explosion after animation
            setTimeout(() => {
                if (explosion.parentNode === gameField) {
                    gameField.removeChild(explosion);
                }
            }, 500);
        }
        
        // Create power-up
        function spawnPowerUp() {
            if (!gameState.gameRunning) return;
            
            // Random position on the field
            const x = Math.random() * (gameField.offsetWidth - 40) + 20;
            const y = Math.random() * (gameField.offsetHeight - 40) + 20;
            
            // Random power-up type
            const types = ['health', 'speed', 'shield'];
            const type = types[Math.floor(Math.random() * types.length)];
            
            const powerUp = document.createElement('div');
            powerUp.className = `power-up ${type}`;
            powerUp.style.left = `${x}px`;
            powerUp.style.top = `${y}px`;
            
            gameState.powerUps.push({
                element: powerUp,
                position: { x, y },
                type
            });
            
            gameField.appendChild(powerUp);
            
            // Remove power-up after some time if not collected
            setTimeout(() => {
                const index = gameState.powerUps.findIndex(p => p.element === powerUp);
                if (index !== -1) {
                    gameState.powerUps.splice(index, 1);
                    if (powerUp.parentNode === gameField) {
                        gameField.removeChild(powerUp);
                    }
                }
            }, 7000);
        }
        
        // Check power-up collection
        function checkPowerUpCollection() {
            for (let i = gameState.powerUps.length - 1; i >= 0; i--) {
                const powerUp = gameState.powerUps[i];
                
                if (isColliding(powerUp.position, gameState.playerPosition, 30)) {
                    // Player collected power-up
                    applyPowerUp(powerUp.type);
                    
                    // Remove power-up
                    gameField.removeChild(powerUp.element);
                    gameState.powerUps.splice(i, 1);
                    
                    // Play sound
                    playSound('powerUp');
                }
            }
        }
        
        // Apply power-up effect
        function applyPowerUp(type) {
            const playerEl = document.getElementById('player');
            
            switch (type) {
                case 'health':
                    gameState.playerHealth = Math.min(gameState.playerHealth + 1, 3);
                    if (playerEl) playerEl.classList.add('health-boost');
                    setTimeout(() => {
                        if (playerEl) playerEl.classList.remove('health-boost');
                    }, 2000);
                    break;
                    
                case 'speed':
                    config.playerSpeed *= 1.5;
                    if (playerEl) playerEl.classList.add('speed-boost');
                    setTimeout(() => {
                        config.playerSpeed /= 1.5;
                        if (playerEl) playerEl.classList.remove('speed-boost');
                    }, 5000);
                    break;
                    
                case 'shield':
                    if (playerEl) {
                        playerEl.classList.add('shielded');
                        gameState.playerShielded = true;
                    }
                    setTimeout(() => {
                        if (playerEl) playerEl.classList.remove('shielded');
                        gameState.playerShielded = false;
                    }, 8000);
                    break;
            }
        }
        
        // Sound effects
        const sounds = {
            shoot: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG'),
            enemyShoot: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG'),
            playerHit: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG'),
            enemyHit: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG'),
            playerDeath: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG'),
            enemyDeath: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG'),
            powerUp: new Audio('data:audio/wav;base64,UklGRjQGAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YTAG')
        };
        
        function playSound(name) {
            // Clone and play to allow overlapping sounds
            try {
                const sound = sounds[name].cloneNode();
                sound.volume = 0.3;
                sound.play();
            } catch (e) {
                // Ignore sound errors
            }
        }
        
        function updateExplosions() {
            // Update explosion animations if needed
        }
    </script>
</body>
</html>
