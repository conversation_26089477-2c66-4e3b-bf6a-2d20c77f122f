#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Demo Video Creator for Enhanced <PERSON> Video System
Creates a demonstration video showcasing the professional features.
"""

import os
import sys
from datetime import datetime

def create_demo_video():
    """Create a demonstration video with enhanced features."""
    print("🎬 CREATING DEMO VIDEO - IMAM AL-JAWAD (AS) CONDOLENCE")
    print("=" * 60)
    print("🤲 In memory of Imam <PERSON> (peace be upon him)")
    print("=" * 60)

    try:
        from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
        
        # Create demo video with Instagram square format
        print("\n📱 Creating Instagram Square Demo Video...")
        creator = ProfessionalEnhancedVideoCreator(
            aspect_ratio='1:1',
            duration_variant='short',  # 30 seconds for demo
            quality='high'
        )

        print(f"✓ Initialized creator: {creator.resolution[0]}x{creator.resolution[1]}")
        print(f"✓ Scenes to include: {len(creator.scenes)}")
        
        # Create the complete video
        print("\n🎨 Generating enhanced video frames...")
        frames = creator.create_complete_enhanced_video()
        
        if frames:
            print(f"✓ Generated {len(frames)} professional frames")
            
            # Export the video
            print("\n📤 Exporting professional video...")
            results = creator.export_professional_video(
                include_audio=True,
                create_variants=False
            )
            
            if results and results.get('main_video'):
                print(f"\n🎉 DEMO VIDEO CREATED SUCCESSFULLY!")
                print(f"📁 Video: {os.path.basename(results['main_video'])}")
                
                if results.get('audio_track'):
                    print(f"🎵 Audio: {os.path.basename(results['audio_track'])}")
                
                info = results['export_info']
                print(f"📊 Details: {info['resolution'][0]}x{info['resolution'][1]} | {info['duration']}s | {info['total_frames']} frames")
                
                return results['main_video']
            else:
                print("❌ Failed to export demo video")
                return None
        else:
            print("❌ Failed to generate video frames")
            return None
            
    except Exception as e:
        print(f"❌ Demo video creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_multiple_format_demo():
    """Create demo videos in multiple formats."""
    print("\n🎬 CREATING MULTI-FORMAT DEMO SUITE")
    print("=" * 50)
    
    formats = [
        ('1:1', 'short', 'Instagram Square'),
        ('16:9', 'short', 'YouTube Landscape'),
        ('9:16', 'short', 'Instagram Story')
    ]
    
    created_videos = []
    
    for aspect_ratio, duration, description in formats:
        print(f"\n📱 Creating {description} ({aspect_ratio})...")
        
        try:
            from professional_imam_jawad_video_enhanced import ProfessionalEnhancedVideoCreator
            
            creator = ProfessionalEnhancedVideoCreator(
                aspect_ratio=aspect_ratio,
                duration_variant=duration,
                quality='high'
            )
            
            # Create frames (limited for demo)
            frames = []
            for i, scene_name in enumerate(creator.scenes[:3]):  # First 3 scenes only
                print(f"   Creating scene {i+1}: {scene_name}")
                
                # Create 20 frames per scene (about 0.67 seconds at 30fps)
                for frame_num in range(20):
                    frame = creator.create_enhanced_scene_frame(
                        scene_name, frame_num, 20, frame_num/19
                    )
                    frames.append(frame)
            
            creator.frames = frames
            
            # Export video
            results = creator.export_professional_video(
                include_audio=True,
                create_variants=False
            )
            
            if results and results.get('main_video'):
                created_videos.append({
                    'format': description,
                    'path': results['main_video'],
                    'info': results['export_info']
                })
                print(f"   ✓ {description} completed")
            else:
                print(f"   ❌ {description} failed")
                
        except Exception as e:
            print(f"   ❌ Error creating {description}: {e}")
    
    return created_videos

def show_demo_features():
    """Display the features demonstrated in the video."""
    print("\n🎯 DEMO VIDEO FEATURES SHOWCASE")
    print("=" * 50)
    
    features = [
        "✓ Professional Arabic Typography with RTL text direction",
        "✓ Enhanced Islamic geometric patterns and decorations", 
        "✓ Sophisticated gradient backgrounds with textures",
        "✓ Advanced animations (fade, slide, zoom, divine glow)",
        "✓ Professional color grading for cinematic appearance",
        "✓ Particle effects and divine light animations",
        "✓ English subtitles with proper timing",
        "✓ Islamic ambient audio with scene synchronization",
        "✓ Multiple aspect ratios for social media platforms",
        "✓ Professional watermarking and branding",
        "✓ Authentic Shia Islamic content and sources",
        "✓ Respectful mourning traditions and terminology"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n📚 AUTHENTIC SHIA SOURCES REFERENCED:")
    sources = [
        "• Bihar al-Anwar by Allama Majlisi",
        "• Uyun Akhbar al-Ridha by Sheikh Saduq", 
        "• Al-Kafi by Sheikh Kulayni",
        "• Manaqib Aal Abi Talib by Ibn Shahr Ashub"
    ]
    
    for source in sources:
        print(f"  {source}")

def main():
    """Main demo creation function."""
    print("🎬 PROFESSIONAL IMAM AL-JAWAD VIDEO DEMO")
    print("=" * 60)
    
    # Show features first
    show_demo_features()
    
    # Create single demo video
    demo_video = create_demo_video()
    
    if demo_video:
        print(f"\n📁 Demo video location: {demo_video}")
        print(f"📂 Output directory: professional_enhanced_output")
        
        # Optionally create multiple formats
        print("\n❓ Would you like to create additional format demos?")
        print("   (This will create YouTube and Instagram Story versions)")
        
        # For automated demo, create multiple formats
        print("\n🎬 Creating additional format demos...")
        multi_videos = create_multiple_format_demo()
        
        if multi_videos:
            print(f"\n🎉 MULTI-FORMAT DEMO SUITE COMPLETED!")
            print("=" * 50)
            
            for video in multi_videos:
                info = video['info']
                print(f"✓ {video['format']}: {os.path.basename(video['path'])}")
                print(f"  📊 {info['resolution'][0]}x{info['resolution'][1]} | {info['duration']}s")
        
        print("\n🤲 May Allah bless the memory of Imam al-Jawad (AS)")
        print("   Created with respect and reverence for Ahl al-Bayt (peace be upon them)")
        
    else:
        print("\n❌ Demo video creation failed")
        print("Please ensure all dependencies are installed:")
        print("  pip install moviepy pydub")

if __name__ == "__main__":
    main()
