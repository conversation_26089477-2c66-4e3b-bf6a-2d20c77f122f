import matplotlib.pyplot as plt
import numpy as np

# Configure plot
plt.figure(figsize=(10, 8))
plt.title("Bi-Cd Phase Diagram")
plt.xlabel("Composition (% Cd)")
plt.ylabel("Temperature (°C)")
plt.grid(True, linestyle='--', alpha=0.7)
plt.xlim(0, 100)
plt.ylim(50, 350)

# Key coordinates
bi_point = (0, 270)       # Pure Bi
cd_point = (100, 320)     # Pure Cd
eutectic_point = (40, 140)  # Eutectic

# Liquidus lines
x_bi = np.linspace(0, 40, 100)
y_bi = np.linspace(270, 140, 100)

x_cd = np.linspace(40, 100, 100)
y_cd = np.linspace(140, 320, 100)

# Plot liquidus lines
plt.plot(x_bi, y_bi, 'b-', linewidth=2, label='Liquidus (Bi)')
plt.plot(x_cd, y_cd, 'r-', linewidth=2, label='Liquidus (Cd)')

# Eutectic line
plt.hlines(140, 0, 100, colors='green', linestyles='dashed', 
          linewidth=2, label='Eutectic (140°C)')

# Region filling
plt.fill_between(x_bi, y_bi, 350, color='skyblue', alpha=0.2)  # Liquid (Bi side)
plt.fill_between(x_cd, y_cd, 350, color='skyblue', alpha=0.2)  # Liquid (Cd side)
plt.fill_between(x_bi, y_bi, 140, color='orange', alpha=0.2)   # L + Bi
plt.fill_between(x_cd, y_cd, 140, color='lime', alpha=0.2)    # L + Cd
plt.fill_between([0, 100], 140, 50, color='violet', alpha=0.2) # Bi + Cd

# Annotations
plt.text(20, 170, "L + Bi", fontsize=12, ha='center')
plt.text(75, 170, "L + Cd", fontsize=12, ha='center')
plt.text(50, 250, "Liquid", fontsize=14, ha='center')
plt.text(50, 100, "Bi + Cd", fontsize=12, ha='center')

plt.scatter(*eutectic_point, s=80, color='black', zorder=3)
plt.text(41, 135, "Eutectic (40% Cd)", ha='left', fontsize=10)

# Melting points
plt.scatter(0, 270, s=80, color='blue', zorder=3)
plt.text(2, 265, "Bi (270°C)", ha='left', fontsize=10)
plt.scatter(100, 320, s=80, color='red', zorder=3)
plt.text(95, 315, "Cd (320°C)", ha='right', fontsize=10)

# 70% Cd alloy markers
plt.vlines(70, 140, 320, colors='gray', linestyles='dotted')
plt.scatter(70, 230, s=100, marker='X', color='purple', 
          label='Initial Solidification (230°C)')
plt.scatter(70, 140, s=100, marker='X', color='brown', 
          label='Final Solidification (140°C)')

plt.legend(loc='upper left')
plt.show()