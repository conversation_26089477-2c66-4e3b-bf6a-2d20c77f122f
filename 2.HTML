<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="img-src 'self' data:; default-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com;">
  <title>تغريدة تدين ناشري الفتن بين العراق وسوريا</title>
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: 'Cairo', sans-serif;
      background-color: #f5f5f5;
      direction: rtl;
      text-align: right;
      line-height: 1.6;
      color: #333;
    }
    .tweet-container {
      max-width: 600px;
      margin: 50px auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .profile-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    .profile-img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      margin-left: 10px;
      object-fit: cover;
      border: 2px solid #1da1f2;
    }
    .profile-info h3 {
      font-size: 1.1em;
      margin-bottom: 2px;
    }
    .profile-info span {
      color: #536471;
      font-size: 0.9em;
    }
    .tweet-content {
      font-size: 1.2em;
      margin-bottom: 15px;
      line-height: 1.8;
    }
    .tweet-image {
      width: 100%;
      border-radius: 15px;
      margin-bottom: 15px;
      max-height: 300px;
      object-fit: contain;
      border: 1px solid #e1e8ed;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease;
    }

    .tweet-image:hover {
      transform: scale(1.02);
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
    .tweet-actions {
      display: flex;
      justify-content: space-around;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }
    .action-btn {
      color: #536471;
      text-decoration: none;
      display: flex;
      align-items: center;
      transition: color 0.2s;
    }
    .action-btn:hover {
      color: #1da1f2;
    }
    .hashtag {
      color: #1da1f2;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="tweet-container">
    <div class="profile-header">
      <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0OCA0OCIgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4Ij48Y2lyY2xlIGN4PSIyNCIgY3k9IjI0IiByPSIyNCIgZmlsbD0iIzFkYTFmMiIgb3BhY2l0eT0iMC4yIi8+PGNpcmNsZSBjeD0iMjQiIGN5PSIxOCIgcj0iOCIgZmlsbD0iI2ZmYTUwMCIvPjxwYXRoIGQ9Ik0yNCwxNiBDMjYuMjA5MSwxNiAyOCwxNy43OTA5IDI4LDIwIEMyOCwyMi4yMDkxIDI2LjIwOTEsMjQgMjQsMjQgQzIxLjc5MDksMjQgMjAsMjIuMjA5MSAyMCwyMCBDMjAsMTcuNzkwOSAyMS43OTA5LDE2IDI0LDE2IFogTTI0LDI2IEMyOC40MTgzLDI2IDMyLDI3Ljc5MDkgMzIsMzAgTDMyLDMxIEMzMiwzMS41NTIzIDMxLjU1MjMsMzIgMzEsMzIgTDE3LDMyIEMxNi40NDc3LDMyIDE2LDMxLjU1MjMgMTYsMzEgTDE2LDMwIEMxNiwyNy43OTA5IDE5LjU4MTcsMjYgMjQsMjYgWiIgZmlsbD0iI2ZmYTUwMCIvPjwvc3ZnPg==" alt="الصورة الشخصية" class="profile-img">
      <div class="profile-info">
        <h3>AliToucan</h3>
        <span>@Alitoucan99</span>
      </div>
    </div>

    <div class="tweet-content">
      أدين بشدة ناشري الفتن والأكاذيب بين العراق وسوريا عبر منصات التواصل 🇮🇶 🇸🇾

      تاريخنا المشترك وروابط الدم والثقافة أقوى من محاولاتكم البائسة لتمزيق نسيجنا الاجتماعي.

      لن تنجحوا في زرع الفتنة بين شعبين جمعتهما الأخوة والمصير المشترك.

      <span class="hashtag">#العراق_وسوريا_يد_واحدة</span>
      <span class="hashtag">#لا_لناشري_الفتن</span>
      <span class="hashtag">#وحدة_المصير</span>
    </div>

    <img src="data:image/svg+xml;base64,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" alt="العلم العراقي" class="tweet-image">

    <div class="tweet-actions">
      <a href="#" class="action-btn">🔄 إعادة تغريد</a>
      <a href="#" class="action-btn">❤️ إعجاب</a>
      <a href="#" class="action-btn">↩️ رد</a>
      <a href="#" class="action-btn">⬆️ مشاركة</a>
    </div>
  </div>

  <script>
    // التأكد من أن الصفحة تعمل بشكل صحيح
    document.addEventListener('DOMContentLoaded', function() {
      console.log('تم تحميل الصفحة بنجاح');

      // إضافة تأثيرات تفاعلية للأزرار
      const actionButtons = document.querySelectorAll('.action-btn');
      actionButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
          e.preventDefault();
          this.style.color = '#1da1f2';
          setTimeout(() => {
            this.style.color = '';
          }, 300);
        });
      });
    });
  </script>
</body>
</html>
