from PyQt5.QtWidgets import QWidget
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QCursor, QPixmap
from PyQt5.QtCore import Qt, QRect, QPoint, QPointF, QSize, QRectF, pyqtSignal, QObject

class DrawingState:
    """Class to hold the state of the drawing canvas"""
    
    def __init__(self):
        self.zoom_level = 1.0
        self.offset_x = 0
        self.offset_y = 0
        self.selected_items = []
        self.clipboard = []
        self.snap_to_grid = True
        self.grid_visible = True
        self.grid_size = 10
        
class DrawingItem:
    """Base class for all drawing items"""
    
    def __init__(self, item_type="undefined", layer=None):
        self.item_type = item_type
        self.layer = layer
        self.selected = False
        self.properties = {}
        self.id = id(self)  # Unique identifier
        
    def draw(self, painter, state):
        """Draw the item on the canvas"""
        pass
        
    def contains_point(self, point):
        """Check if the point is contained within the item"""
        return False
        
    def move(self, dx, dy):
        """Move the item by the given offset"""
        pass
        
    def clone(self):
        """Create a copy of this item"""
        pass
        
    def get_bounding_rect(self):
        """Get the bounding rectangle of this item"""
        return QRectF()
        
    def set_property(self, name, value):
        """Set a property value"""
        self.properties[name] = value
        
    def get_property(self, name, default=None):
        """Get a property value"""
        return self.properties.get(name, default)

class LineItem(DrawingItem):
    """Line drawing item"""
    
    def __init__(self, start_point, end_point, layer=None):
        super().__init__("line", layer)
        self.start_point = QPointF(start_point)
        self.end_point = QPointF(end_point)
        self.properties["line_width"] = 1
        self.properties["line_color"] = QColor(0, 0, 0)
        self.properties["line_style"] = Qt.SolidLine
        
    def draw(self, painter, state):
        """Draw the line on the canvas"""
        if self.layer and not self.layer.visible:
            return
            
        pen = QPen(self.properties["line_color"])
        pen.setWidth(self.properties["line_width"])
        pen.setStyle(self.properties["line_style"])
        
        if self.selected:
            # Use a different color or style for selected lines
            pen.setColor(QColor(255, 0, 0))
            
        painter.setPen(pen)
        
        # Apply zoom and offset
        start_x = self.start_point.x() * state.zoom_level + state.offset_x
        start_y = self.start_point.y() * state.zoom_level + state.offset_y
        end_x = self.end_point.x() * state.zoom_level + state.offset_x
        end_y = self.end_point.y() * state.zoom_level + state.offset_y
        
        painter.drawLine(start_x, start_y, end_x, end_y)
        
        # Draw selection handles if selected
        if self.selected:
            handle_size = 6
            painter.setBrush(QBrush(QColor(255, 0, 0)))
            painter.drawRect(start_x - handle_size/2, start_y - handle_size/2, handle_size, handle_size)
            painter.drawRect(end_x - handle_size/2, end_y - handle_size/2, handle_size, handle_size)
        
    def contains_point(self, point):
        """Check if the point is near the line"""
        line_threshold = 5  # Distance in pixels
        
        # Calculate the distance from point to line
        line_len = ((self.end_point.x() - self.start_point.x()) ** 2 + 
                    (self.end_point.y() - self.start_point.y()) ** 2) ** 0.5
                    
        if line_len == 0:
            # Start and end points are the same
            return self.distance_to_point(point, self.start_point) <= line_threshold
            
        t = ((point.x() - self.start_point.x()) * (self.end_point.x() - self.start_point.x()) +
             (point.y() - self.start_point.y()) * (self.end_point.y() - self.start_point.y())) / (line_len ** 2)
             
        # Clamp t to [0,1] for line segment
        t = max(0, min(1, t))
        
        closest_x = self.start_point.x() + t * (self.end_point.x() - self.start_point.x())
        closest_y = self.start_point.y() + t * (self.end_point.y() - self.start_point.y())
        
        distance = ((point.x() - closest_x) ** 2 + (point.y() - closest_y) ** 2) ** 0.5
        
        return distance <= line_threshold
        
    def distance_to_point(self, p1, p2):
        """Calculate the distance between two points"""
        return ((p1.x() - p2.x()) ** 2 + (p1.y() - p2.y()) ** 2) ** 0.5
        
    def move(self, dx, dy):
        """Move the line by the given offset"""
        self.start_point.setX(self.start_point.x() + dx)
        self.start_point.setY(self.start_point.y() + dy)
        self.end_point.setX(self.end_point.x() + dx)
        self.end_point.setY(self.end_point.y() + dy)
        
    def clone(self):
        """Create a copy of this line"""
        new_line = LineItem(self.start_point, self.end_point, self.layer)
        for key, value in self.properties.items():
            new_line.properties[key] = value
        return new_line
        
    def get_bounding_rect(self):
        """Get the bounding rectangle of this line"""
        x1, y1 = self.start_point.x(), self.start_point.y()
        x2, y2 = self.end_point.x(), self.end_point.y()
        return QRectF(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))

class CircleItem(DrawingItem):
    """Circle drawing item"""
    
    def __init__(self, center, radius, layer=None):
        super().__init__("circle", layer)
        self.center = QPointF(center)
        self.radius = radius
        self.properties["line_width"] = 1
        self.properties["line_color"] = QColor(0, 0, 0)
        self.properties["line_style"] = Qt.SolidLine
        self.properties["fill_color"] = QColor(255, 255, 255, 0)  # Transparent by default
        
    def draw(self, painter, state):
        """Draw the circle on the canvas"""
        if self.layer and not self.layer.visible:
            return
            
        pen = QPen(self.properties["line_color"])
        pen.setWidth(self.properties["line_width"])
        pen.setStyle(self.properties["line_style"])
        
        if self.selected:
            # Use a different color for selected circles
            pen.setColor(QColor(255, 0, 0))
            
        painter.setPen(pen)
        painter.setBrush(QBrush(self.properties["fill_color"]))
        
        # Apply zoom and offset
        center_x = self.center.x() * state.zoom_level + state.offset_x
        center_y = self.center.y() * state.zoom_level + state.offset_y
        radius = self.radius * state.zoom_level
        
        painter.drawEllipse(center_x - radius, center_y - radius, radius * 2, radius * 2)
        
        # Draw selection handles if selected
        if self.selected:
            handle_size = 6
            painter.setBrush(QBrush(QColor(255, 0, 0)))
            # Top, right, bottom, left handles
            painter.drawRect(center_x - handle_size/2, center_y - radius - handle_size/2, handle_size, handle_size)
            painter.drawRect(center_x + radius - handle_size/2, center_y - handle_size/2, handle_size, handle_size)
            painter.drawRect(center_x - handle_size/2, center_y + radius - handle_size/2, handle_size, handle_size)
            painter.drawRect(center_x - radius - handle_size/2, center_y - handle_size/2, handle_size, handle_size)
            
            # Center handle
            painter.drawRect(center_x - handle_size/2, center_y - handle_size/2, handle_size, handle_size)
        
    def contains_point(self, point):
        """Check if the point is near the circle's outline"""
        distance = ((point.x() - self.center.x()) ** 2 + (point.y() - self.center.y()) ** 2) ** 0.5
        return abs(distance - self.radius) <= 5  # Within 5 pixels of the circle outline
        
    def move(self, dx, dy):
        """Move the circle by the given offset"""
        self.center.setX(self.center.x() + dx)
        self.center.setY(self.center.y() + dy)
        
    def clone(self):
        """Create a copy of this circle"""
        new_circle = CircleItem(self.center, self.radius, self.layer)
        for key, value in self.properties.items():
            new_circle.properties[key] = value
        return new_circle
        
    def get_bounding_rect(self):
        """Get the bounding rectangle of this circle"""
        return QRectF(self.center.x() - self.radius, self.center.y() - self.radius, 
                     self.radius * 2, self.radius * 2)
                     
class RectangleItem(DrawingItem):
    """Rectangle drawing item"""
    
    def __init__(self, top_left, bottom_right, layer=None):
        super().__init__("rectangle", layer)
        self.top_left = QPointF(top_left)
        self.bottom_right = QPointF(bottom_right)
        self.properties["line_width"] = 1
        self.properties["line_color"] = QColor(0, 0, 0)
        self.properties["line_style"] = Qt.SolidLine
        self.properties["fill_color"] = QColor(255, 255, 255, 0)  # Transparent by default
        
    def draw(self, painter, state):
        """Draw the rectangle on the canvas"""
        if self.layer and not self.layer.visible:
            return
            
        pen = QPen(self.properties["line_color"])
        pen.setWidth(self.properties["line_width"])
        pen.setStyle(self.properties["line_style"])
        
        if self.selected:
            # Use a different color for selected rectangles
            pen.setColor(QColor(255, 0, 0))
            
        painter.setPen(pen)
        painter.setBrush(QBrush(self.properties["fill_color"]))
        
        # Apply zoom and offset
        x1 = self.top_left.x() * state.zoom_level + state.offset_x
        y1 = self.top_left.y() * state.zoom_level + state.offset_y
        x2 = self.bottom_right.x() * state.zoom_level + state.offset_x
        y2 = self.bottom_right.y() * state.zoom_level + state.offset_y
        
        painter.drawRect(x1, y1, x2 - x1, y2 - y1)
        
        # Draw selection handles if selected
        if self.selected:
            handle_size = 6
            painter.setBrush(QBrush(QColor(255, 0, 0)))
            
            # Corner handles
            painter.drawRect(x1 - handle_size/2, y1 - handle_size/2, handle_size, handle_size)  # Top-left
            painter.drawRect(x2 - handle_size/2, y1 - handle_size/2, handle_size, handle_size)  # Top-right
            painter.drawRect(x1 - handle_size/2, y2 - handle_size/2, handle_size, handle_size)  # Bottom-left
            painter.drawRect(x2 - handle_size/2, y2 - handle_size/2, handle_size, handle_size)  # Bottom-right
            
            # Middle handles
            mid_x = (x1 + x2) / 2
            mid_y = (y1 + y2) / 2
            painter.drawRect(mid_x - handle_size/2, y1 - handle_size/2, handle_size, handle_size)  # Top
            painter.drawRect(mid_x - handle_size/2, y2 - handle_size/2, handle_size, handle_size)  # Bottom
            painter.drawRect(x1 - handle_size/2, mid_y - handle_size/2, handle_size, handle_size)  # Left
            painter.drawRect(x2 - handle_size/2, mid_y - handle_size/2, handle_size, handle_size)  # Right
        
    def contains_point(self, point):
        """Check if the point is near the rectangle's outline"""
        x1, y1 = self.top_left.x(), self.top_left.y()
        x2, y2 = self.bottom_right.x(), self.bottom_right.y()
        
        # Ensure x1 < x2 and y1 < y2
        if x1 > x2:
            x1, x2 = x2, x1
        if y1 > y2:
            y1, y2 = y2, y1
            
        # Check if the point is inside the rectangle
        if x1 <= point.x() <= x2 and y1 <= point.y() <= y2:
            # Check if it's near the border (within 5 pixels)
            border_threshold = 5
            near_left_edge = abs(point.x() - x1) <= border_threshold
            near_right_edge = abs(point.x() - x2) <= border_threshold
            near_top_edge = abs(point.y() - y1) <= border_threshold
            near_bottom_edge = abs(point.y() - y2) <= border_threshold
            
            return near_left_edge or near_right_edge or near_top_edge or near_bottom_edge
            
        return False
        
    def move(self, dx, dy):
        """Move the rectangle by the given offset"""
        self.top_left.setX(self.top_left.x() + dx)
        self.top_left.setY(self.top_left.y() + dy)
        self.bottom_right.setX(self.bottom_right.x() + dx)
        self.bottom_right.setY(self.bottom_right.y() + dy)
        
    def clone(self):
        """Create a copy of this rectangle"""
        new_rect = RectangleItem(self.top_left, self.bottom_right, self.layer)
        for key, value in self.properties.items():
            new_rect.properties[key] = value
        return new_rect
        
    def get_bounding_rect(self):
        """Get the bounding rectangle of this rectangle"""
        x1, y1 = self.top_left.x(), self.top_left.y()
        x2, y2 = self.bottom_right.x(), self.bottom_right.y()
        return QRectF(min(x1, x2), min(y1, y2), abs(x2 - x1), abs(y2 - y1))

class TextItem(DrawingItem):
    """Text drawing item"""
    
    def __init__(self, position, text, layer=None):
        super().__init__("text", layer)
        self.position = QPointF(position)
        self.text = text
        self.properties["font_family"] = "Arial"
        self.properties["font_size"] = 12
        self.properties["font_bold"] = False
        self.properties["font_italic"] = False
        self.properties["text_color"] = QColor(0, 0, 0)
        self.properties["angle"] = 0  # Rotation angle in degrees
        
    def draw(self, painter, state):
        """Draw the text on the canvas"""
        if self.layer and not self.layer.visible:
            return
            
        # Create font
        font = QFont(self.properties["font_family"], self.properties["font_size"])
        font.setBold(self.properties["font_bold"])
        font.setItalic(self.properties["font_italic"])
        painter.setFont(font)
        
        if self.selected:
            # Use a different color for selected text
            painter.setPen(QPen(QColor(255, 0, 0)))
        else:
            painter.setPen(QPen(self.properties["text_color"]))
        
        # Apply zoom and offset
        x = self.position.x() * state.zoom_level + state.offset_x
        y = self.position.y() * state.zoom_level + state.offset_y
        
        # Save current transformation
        painter.save()
        
        # Move to position and rotate
        painter.translate(x, y)
        if self.properties["angle"] != 0:
            painter.rotate(self.properties["angle"])
            
        # Draw text at origin (0,0) since we've already translated
        painter.drawText(0, 0, self.text)
        
        # Draw selection box if selected
        if self.selected:
            font_metrics = painter.fontMetrics()
            text_rect = font_metrics.boundingRect(self.text)
            
            # Draw selection handles
            handle_size = 6
            painter.setBrush(QBrush(QColor(255, 0, 0)))
            
            # Corner handles for the text box
            painter.drawRect(-handle_size/2, -handle_size/2, handle_size, handle_size)  # Top-left
            painter.drawRect(text_rect.width() - handle_size/2, -handle_size/2, handle_size, handle_size)  # Top-right
            painter.drawRect(-handle_size/2, text_rect.height() - handle_size/2, handle_size, handle_size)  # Bottom-left
            painter.drawRect(text_rect.width() - handle_size/2, text_rect.height() - handle_size/2, handle_size, handle_size)  # Bottom-right
            
            # Draw the selection rectangle
            pen = QPen(QColor(255, 0, 0))
            pen.setStyle(Qt.DashLine)
            painter.setPen(pen)
            painter.setBrush(QBrush())  # No fill
            painter.drawRect(0, -text_rect.height(), text_rect.width(), text_rect.height())
        
        # Restore original transformation
        painter.restore()
        
    def contains_point(self, point):
        """Check if the point is inside the text area"""
        text_rect = self.get_bounding_rect()
        return text_rect.contains(point)
        
    def move(self, dx, dy):
        """Move the text by the given offset"""
        self.position.setX(self.position.x() + dx)
        self.position.setY(self.position.y() + dy)
        
    def clone(self):
        """Create a copy of this text"""
        new_text = TextItem(self.position, self.text, self.layer)
        for key, value in self.properties.items():
            new_text.properties[key] = value
        return new_text
        
    def get_bounding_rect(self):
        """Get the bounding rectangle of this text"""
        # This is a simplified version that doesn't account for rotation
        # For a real implementation, we'd need to calculate the rotated bounds
        font = QFont(self.properties["font_family"], self.properties["font_size"])
        font.setBold(self.properties["font_bold"])
        font.setItalic(self.properties["font_italic"])
        
        font_metrics = QFontMetrics(font)
        text_rect = font_metrics.boundingRect(self.text)
        
        return QRectF(self.position.x(), self.position.y() - text_rect.height(), 
                     text_rect.width(), text_rect.height())

class DrawingCanvas(QWidget):
    """Main drawing canvas widget"""
    
    item_created = pyqtSignal(DrawingItem)
    item_selected = pyqtSignal(DrawingItem)
    item_moved = pyqtSignal(DrawingItem)
    items_deleted = pyqtSignal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.items = []  # List of drawing items
        self.state = DrawingState()
        self.tool = None  # Current active tool
        self.mouse_position = QPoint(0, 0)
        self.drawing_in_progress = False
        self.temp_item = None
        self.selected_items = []
        self.drag_start_pos = None
        self.last_cursor_pos = None
        
        # Set widget properties
        self.setMouseTracking(True)
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMinimumSize(400, 300)
        self.setStyleSheet("background-color: white;")
    
    def set_tool(self, tool):
        """Set the current drawing tool"""
        self.tool = tool
        if tool:
            cursor = tool.get_cursor()
            if cursor:
                self.setCursor(cursor)
            else:
                self.setCursor(Qt.ArrowCursor)
        else:
            self.setCursor(Qt.ArrowCursor)
    
    def add_item(self, item):
        """Add a drawing item to the canvas"""
        self.items.append(item)
        self.item_created.emit(item)
        self.update()
    
    def remove_item(self, item):
        """Remove a drawing item from the canvas"""
        if item in self.items:
            self.items.remove(item)
            if item in self.selected_items:
                self.selected_items.remove(item)
            self.update()
    
    def clear(self):
        """Clear all items from the canvas"""
        self.items = []
        self.selected_items = []
        self.update()
    
    def select_item(self, item, add_to_selection=False):
        """Select a drawing item"""
        if not add_to_selection:
            for selected_item in self.selected_items:
                selected_item.selected = False
            self.selected_items = []
        
        if item:
            item.selected = True
            if item not in self.selected_items:
                self.selected_items.append(item)
                
        self.item_selected.emit(item if len(self.selected_items) == 1 else None)
        self.update()
    
    def select_items_in_rect(self, rect):
        """Select all items that intersect with the given rectangle"""
        for item in self.items:
            if rect.intersects(item.get_bounding_rect()):
                item.selected = True
                if item not in self.selected_items:
                    self.selected_items.append(item)
        
        self.item_selected.emit(None)  # Multiple items selected
        self.update()
    
    def deselect_all(self):
        """Deselect all items"""
        for item in self.selected_items:
            item.selected = False
        self.selected_items = []
        self.item_selected.emit(None)
        self.update()
    
    def select_all(self):
        """Select all items on the canvas"""
        self.selected_items = []
        for item in self.items:
            item.selected = True
            self.selected_items.append(item)
        
        self.item_selected.emit(None)  # Multiple items selected
        self.update()
    
    def delete_selected(self):
        """Delete all selected items"""
        if not self.selected_items:
            return
            
        deleted_items = self.selected_items.copy()
        
        for item in self.selected_items:
            self.items.remove(item)
            
        self.selected_items = []
        self.items_deleted.emit(deleted_items)
        self.update()
    
    def item_at(self, point):
        """Find item at the given point"""
        for item in reversed(self.items):  # Check from top to bottom
            if item.contains_point(self.screen_to_world(point)):
                return item
        return None
    
    def screen_to_world(self, point):
        """Convert screen coordinates to world coordinates"""
        x = (point.x() - self.state.offset_x) / self.state.zoom_level
        y = (point.y() - self.state.offset_y) / self.state.zoom_level
        return QPointF(x, y)
    
    def world_to_screen(self, point):
        """Convert world coordinates to screen coordinates"""
        x = point.x() * self.state.zoom_level + self.state.offset_x
        y = point.y() * self.state.zoom_level + self.state.offset_y
        return QPoint(int(x), int(y))
    
    def zoom_in(self):
        """Zoom in on the canvas"""
        self.state.zoom_level *= 1.2
        self.update()
    
    def zoom_out(self):
        """Zoom out on the canvas"""
        self.state.zoom_level *= 0.8
        self.update()
    
    def zoom_fit(self):
        """Zoom to fit all content"""
        if not self.items:
            return
            
        # Find the bounding rectangle of all items
        min_x = float('inf')
        min_y = float('inf')
        max_x = float('-inf')
        max_y = float('-inf')
        
        for item in self.items:
            rect = item.get_bounding_rect()
            min_x = min(min_x, rect.left())
            min_y = min(min_y, rect.top())
            max_x = max(max_x, rect.right())
            max_y = max(max_y, rect.bottom())
        
        # Add some margin
        margin = 50
        min_x -= margin / self.state.zoom_level
        min_y -= margin / self.state.zoom_level
        max_x += margin / self.state.zoom_level
        max_y += margin / self.state.zoom_level
        
        # Calculate the required zoom and offset
        content_width = max_x - min_x
        content_height = max_y - min_y
        
        if content_width <= 0 or content_height <= 0:
            return
            
        widget_width = self.width()
        widget_height = self.height()
        
        zoom_x = widget_width / content_width
        zoom_y = widget_height / content_height
        
        self.state.zoom_level = min(zoom_x, zoom_y)
        self.state.offset_x = -min_x * self.state.zoom_level
        self.state.offset_y = -min_y * self.state.zoom_level
        
        self.update()
    
    def pan(self, dx, dy):
        """Pan the canvas by the given offset"""
        self.state.offset_x += dx
        self.state.offset_y += dy
        self.update()
    
    def copy_selected(self):
        """Copy selected items to clipboard"""
        self.state.clipboard = []
        for item in self.selected_items:
            self.state.clipboard.append(item.clone())
    
    def cut_selected(self):
        """Cut selected items to clipboard"""
        self.copy_selected()
        self.delete_selected()
    
    def paste(self):
        """Paste items from clipboard"""
        offset = 20  # Offset for pasted items
        
        for item in self.state.clipboard:
            new_item = item.clone()
            new_item.move(offset, offset)
            self.items.append(new_item)
            
        self.update()
    
    def update_selected_properties(self, property_name, value):
        """Update properties of selected items"""
        for item in self.selected_items:
            item.set_property(property_name, value)
        self.update()
    
    def load_from_project(self, project, view=None):
        """Load drawing items from a project"""
        # This would load items for the specified view from the project
        pass
    
    def paintEvent(self, event):
        """Handle the paint event"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw the background
        painter.fillRect(event.rect(), QColor(255, 255, 255))
        
        # Draw the grid if visible
        if self.state.grid_visible:
            self.draw_grid(painter)
        
        # Draw all items
        for item in self.items:
            item.draw(painter, self.state)
        
        # Draw the temporary item if drawing is in progress
        if self.drawing_in_progress and self.temp_item:
            self.temp_item.draw(painter, self.state)
    
    def draw_grid(self, painter):
        """Draw the grid lines"""
        # Get viewport dimensions
        width = self.width()
        height = self.height()
        
        # Set up grid properties
        grid_size = self.state.grid_size * self.state.zoom_level
        if grid_size < 5:
            grid_size = self.state.grid_size * self.state.zoom_level * 2
            
        # Adjust the grid offset based on the current canvas offset
        offset_x = (self.state.offset_x % grid_size)
        offset_y = (self.state.offset_y % grid_size)
        
        # Draw vertical grid lines
        pen = QPen(QColor(200, 200, 200))
        pen.setStyle(Qt.DotLine)
        painter.setPen(pen)
        
        x = offset_x
        while x < width:
            painter.drawLine(x, 0, x, height)
            x += grid_size
        
        # Draw horizontal grid lines
        y = offset_y
        while y < height:
            painter.drawLine(0, y, width, y)
            y += grid_size
    
    def mousePressEvent(self, event):
        """Handle mouse press event"""
        if not self.tool:
            return
            
        world_pos = self.screen_to_world(event.pos())
        
        if event.button() == Qt.LeftButton:
            if self.tool.is_drawing_tool():
                # Start drawing with the current tool
                self.drawing_in_progress = True
                self.temp_item = self.tool.start_drawing(world_pos)
            elif self.tool.name == "select":
                # Try to select an item
                item = self.item_at(event.pos())
                self.select_item(item, event.modifiers() & Qt.ControlModifier)
                
                # Start drag operation if an item is selected
                if item:
                    self.drag_start_pos = event.pos()
                    self.last_cursor_pos = event.pos()
            
            self.update()
    
    def mouseMoveEvent(self, event):
        """Handle mouse move event"""
        self.mouse_position = event.pos()
        
        if self.drawing_in_progress and self.temp_item:
            # Update temporary item during drawing
            world_pos = self.screen_to_world(event.pos())
            self.tool.continue_drawing(self.temp_item, world_pos)
            self.update()
        elif self.drag_start_pos and event.buttons() & Qt.LeftButton:
            # Move selected items
            dx = event.x() - self.last_cursor_pos.x()
            dy = event.y() - self.last_cursor_pos.y()
            
            # Convert screen distance to world distance
            world_dx = dx / self.state.zoom_level
            world_dy = dy / self.state.zoom_level
            
            for item in self.selected_items:
                item.move(world_dx, world_dy)
                
            self.item_moved.emit(None)  # Notify that items have been moved
            self.last_cursor_pos = event.pos()
            self.update()
    
    def mouseReleaseEvent(self, event):
        """Handle mouse release event"""
        if event.button() == Qt.LeftButton:
            if self.drawing_in_progress:
                # Finish drawing
                world_pos = self.screen_to_world(event.pos())
                completed_item = self.tool.finish_drawing(self.temp_item, world_pos)
                
                if completed_item:
                    self.add_item(completed_item)
                    
                self.drawing_in_progress = False
                self.temp_item = None
            
            self.drag_start_pos = None
            self.last_cursor_pos = None
            
        self.update()
    
    def keyPressEvent(self, event):
        """Handle key press event"""
        # Delete key to delete selected items
        if event.key() == Qt.Key_Delete:
            self.delete_selected()
        # Escape key to cancel drawing
        elif event.key() == Qt.Key_Escape:
            if self.drawing_in_progress:
                self.drawing_in_progress = False
                self.temp_item = None
                self.update()
            else:
                self.deselect_all()
        # Ctrl+A to select all
        elif event.key() == Qt.Key_A and event.modifiers() & Qt.ControlModifier:
            self.select_all()
        # Ctrl+C to copy
        elif event.key() == Qt.Key_C and event.modifiers() & Qt.ControlModifier:
            self.copy_selected()
        # Ctrl+X to cut
        elif event.key() == Qt.Key_X and event.modifiers() & Qt.ControlModifier:
            self.cut_selected()
        # Ctrl+V to paste
        elif event.key() == Qt.Key_V and event.modifiers() & Qt.ControlModifier:
            self.paste()
        # Plus key or Ctrl++ to zoom in
        elif event.key() == Qt.Key_Plus or (event.key() == Qt.Key_Equal and event.modifiers() & Qt.ControlModifier):
            self.zoom_in()
        # Minus key or Ctrl+- to zoom out
        elif event.key() == Qt.Key_Minus and event.modifiers() & Qt.ControlModifier:
            self.zoom_out()
        # Ctrl+0 to zoom fit
        elif event.key() == Qt.Key_0 and event.modifiers() & Qt.ControlModifier:
            self.zoom_fit()
            
    def wheelEvent(self, event):
        """Handle mouse wheel event for zooming"""
        delta = event.angleDelta().y()
        
        if delta > 0:
            # Zoom in
            zoom_factor = 1.1
        else:
            # Zoom out
            zoom_factor = 0.9
            
        # Get the mouse position before zoom
        old_pos = self.screen_to_world(event.pos())
        
        # Apply zoom
        self.state.zoom_level *= zoom_factor
        
        # Get the new position under mouse
        new_pos = self.screen_to_world(event.pos())
        
        # Adjust offset to keep the point under the mouse fixed
        dx = (new_pos.x() - old_pos.x()) * self.state.zoom_level
        dy = (new_pos.y() - old_pos.y()) * self.state.zoom_level
        
        self.state.offset_x -= dx
        self.state.offset_y -= dy
        
        self.update()
        
    def resizeEvent(self, event):
        """Handle widget resize event"""
        super().resizeEvent(event)
        # Optional: adjust the view when the widget is resized 