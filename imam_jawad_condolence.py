#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PIL import Image, ImageDraw, ImageFont
import os
import math

def create_imam_jawad_condolence_design():
    """
    Create a 1:1 aspect ratio condolence design for <PERSON> (AS) martyrdom.
    """
    # Design specifications
    size = 1080  # 1080x1080 for social media
    background_color = (0, 0, 0)  # Black background for mourning
    primary_text_color = (255, 215, 0)  # Gold color
    secondary_text_color = (255, 255, 255)  # White color
    accent_color = (139, 69, 19)  # Dark brown/bronze

    # Create the image
    img = Image.new('RGB', (size, size), background_color)
    draw = ImageDraw.Draw(img)

    # Try to find Arabic-supporting fonts
    font_paths = [
        "C:\\Windows\\Fonts\\arial.ttf",
        "C:\\Windows\\Fonts\\calibri.ttf",
        "C:\\Windows\\Fonts\\tahoma.ttf",
        "arial.ttf",
        "calibri.ttf"
    ]

    # Font sizes
    title_font = None
    main_font = None
    subtitle_font = None
    watermark_font = None

    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                title_font = ImageFont.truetype(font_path, 48)
                main_font = ImageFont.truetype(font_path, 36)
                subtitle_font = ImageFont.truetype(font_path, 28)
                watermark_font = ImageFont.truetype(font_path, 20)
                break
            except Exception:
                continue

    # Fallback to default fonts if no TrueType fonts found
    if title_font is None:
        title_font = ImageFont.load_default()
        main_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        watermark_font = ImageFont.load_default()
        print("Warning: Using default fonts. Arabic text may not display properly.")

    # Arabic text content (Right-to-Left)
    texts = {
        'title': 'بسم الله الرحمن الرحيم',
        'main_condolence': 'بمناسبة ذكرى استشهاد',
        'imam_name': 'الإمام محمد الجواد عليه السلام',
        'date_hijri': 'التاسع والعشرون من ذي القعدة',
        'condolence_phrase': 'أحيا الله ذكراكم وأعظم أجوركم',
        'prayer': 'اللهم صل على محمد وآل محمد',
        'watermark': 'AliToucan'
    }

    # Draw decorative border
    border_width = 8
    draw.rectangle([border_width, border_width, size-border_width, size-border_width],
                  outline=primary_text_color, width=3)

    # Draw inner decorative frame
    inner_margin = 60
    draw.rectangle([inner_margin, inner_margin, size-inner_margin, size-inner_margin],
                  outline=accent_color, width=2)

    # Calculate text positions (centered)
    y_positions = {
        'title': 120,
        'main_condolence': 220,
        'imam_name': 300,
        'date_hijri': 420,
        'condolence_phrase': 520,
        'prayer': 620,
        'decorative_line': 680
    }

    # Helper function to center text
    def draw_centered_text(text, y_pos, font, color):
        # Get text bounding box
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        x_pos = (size - text_width) // 2
        draw.text((x_pos, y_pos), text, font=font, fill=color)

    # Draw title (Bismillah)
    draw_centered_text(texts['title'], y_positions['title'], subtitle_font, secondary_text_color)

    # Draw main condolence text
    draw_centered_text(texts['main_condolence'], y_positions['main_condolence'], main_font, primary_text_color)

    # Draw Imam's name (highlighted)
    draw_centered_text(texts['imam_name'], y_positions['imam_name'], title_font, primary_text_color)

    # Draw date
    draw_centered_text(texts['date_hijri'], y_positions['date_hijri'], subtitle_font, secondary_text_color)

    # Draw condolence phrase
    draw_centered_text(texts['condolence_phrase'], y_positions['condolence_phrase'], main_font, primary_text_color)

    # Draw prayer
    draw_centered_text(texts['prayer'], y_positions['prayer'], subtitle_font, secondary_text_color)

    # Draw decorative line
    line_y = y_positions['decorative_line']
    line_start = size // 4
    line_end = 3 * size // 4
    draw.line([(line_start, line_y), (line_end, line_y)], fill=accent_color, width=3)

    # Add small decorative elements (Islamic geometric pattern)
    center_x, center_y = size // 2, size // 2

    # Draw small decorative circles in corners
    corner_offset = 100
    circle_radius = 15
    corners = [
        (corner_offset, corner_offset),
        (size - corner_offset, corner_offset),
        (corner_offset, size - corner_offset),
        (size - corner_offset, size - corner_offset)
    ]

    for corner_x, corner_y in corners:
        draw.ellipse([corner_x - circle_radius, corner_y - circle_radius,
                     corner_x + circle_radius, corner_y + circle_radius],
                    outline=accent_color, width=2)

    # Add watermark (AliToucan)
    watermark_x = size - 150
    watermark_y = size - 40
    draw.text((watermark_x, watermark_y), texts['watermark'],
              font=watermark_font, fill=(128, 128, 128))

    return img

def save_design_with_options(img, base_filename="imam_jawad_condolence"):
    """
    Save the design in multiple formats and create output directory.
    """
    # Create output directory
    output_dir = "imam_jawad_designs"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    # Save in different formats
    formats = [
        ("png", "PNG"),
        ("jpg", "JPEG")
    ]

    saved_files = []

    for ext, format_name in formats:
        filename = f"{base_filename}.{ext}"
        filepath = os.path.join(output_dir, filename)

        if ext == "jpg":
            # Convert to RGB for JPEG
            rgb_img = Image.new('RGB', img.size, (0, 0, 0))
            rgb_img.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            rgb_img.save(filepath, format_name, quality=95)
        else:
            img.save(filepath, format_name)

        saved_files.append(filepath)
        print(f"✓ Saved: {filepath}")

    return saved_files

def create_multiple_designs():
    """
    Create multiple design variations.
    """
    designs = [
        ("imam_jawad_condolence_classic", (0, 0, 0), (255, 215, 0)),  # Black/Gold
        ("imam_jawad_condolence_elegant", (25, 25, 25), (200, 200, 200)),  # Dark Gray/Silver
        ("imam_jawad_condolence_traditional", (0, 0, 0), (255, 255, 255))  # Black/White
    ]

    all_saved_files = []

    for design_name, bg_color, primary_color in designs:
        print(f"\nCreating {design_name}...")
        img = create_custom_design(bg_color, primary_color)
        saved_files = save_design_with_options(img, design_name)
        all_saved_files.extend(saved_files)

    return all_saved_files

def create_custom_design(background_color=(0, 0, 0), primary_text_color=(255, 215, 0)):
    """
    Create a customizable version of the design.
    """
    # Use the same logic as create_imam_jawad_condolence_design but with custom colors
    size = 1080
    secondary_text_color = (255, 255, 255)
    accent_color = (139, 69, 19)

    img = Image.new('RGB', (size, size), background_color)
    draw = ImageDraw.Draw(img)

    # Font setup (same as before)
    font_paths = [
        "C:\\Windows\\Fonts\\arial.ttf",
        "C:\\Windows\\Fonts\\calibri.ttf",
        "C:\\Windows\\Fonts\\tahoma.ttf",
        "arial.ttf",
        "calibri.ttf"
    ]

    title_font = main_font = subtitle_font = watermark_font = None

    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                title_font = ImageFont.truetype(font_path, 48)
                main_font = ImageFont.truetype(font_path, 36)
                subtitle_font = ImageFont.truetype(font_path, 28)
                watermark_font = ImageFont.truetype(font_path, 20)
                break
            except Exception:
                continue

    if title_font is None:
        title_font = main_font = subtitle_font = watermark_font = ImageFont.load_default()

    # Same text content and layout as before
    texts = {
        'title': 'بسم الله الرحمن الرحيم',
        'main_condolence': 'بمناسبة ذكرى استشهاد',
        'imam_name': 'الإمام محمد الجواد عليه السلام',
        'date_hijri': 'التاسع والعشرون من ذي القعدة',
        'condolence_phrase': 'أحيا الله ذكراكم وأعظم أجوركم',
        'prayer': 'اللهم صل على محمد وآل محمد',
        'watermark': 'AliToucan'
    }

    # Draw all elements with custom colors
    border_width = 8
    draw.rectangle([border_width, border_width, size-border_width, size-border_width],
                  outline=primary_text_color, width=3)

    inner_margin = 60
    draw.rectangle([inner_margin, inner_margin, size-inner_margin, size-inner_margin],
                  outline=accent_color, width=2)

    y_positions = {
        'title': 120, 'main_condolence': 220, 'imam_name': 300,
        'date_hijri': 420, 'condolence_phrase': 520, 'prayer': 620,
        'decorative_line': 680
    }

    def draw_centered_text(text, y_pos, font, color):
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        x_pos = (size - text_width) // 2
        draw.text((x_pos, y_pos), text, font=font, fill=color)

    # Draw all text elements
    draw_centered_text(texts['title'], y_positions['title'], subtitle_font, secondary_text_color)
    draw_centered_text(texts['main_condolence'], y_positions['main_condolence'], main_font, primary_text_color)
    draw_centered_text(texts['imam_name'], y_positions['imam_name'], title_font, primary_text_color)
    draw_centered_text(texts['date_hijri'], y_positions['date_hijri'], subtitle_font, secondary_text_color)
    draw_centered_text(texts['condolence_phrase'], y_positions['condolence_phrase'], main_font, primary_text_color)
    draw_centered_text(texts['prayer'], y_positions['prayer'], subtitle_font, secondary_text_color)

    # Decorative elements
    line_y = y_positions['decorative_line']
    line_start, line_end = size // 4, 3 * size // 4
    draw.line([(line_start, line_y), (line_end, line_y)], fill=accent_color, width=3)

    # Corner decorations
    corner_offset, circle_radius = 100, 15
    corners = [(corner_offset, corner_offset), (size - corner_offset, corner_offset),
               (corner_offset, size - corner_offset), (size - corner_offset, size - corner_offset)]

    for corner_x, corner_y in corners:
        draw.ellipse([corner_x - circle_radius, corner_y - circle_radius,
                     corner_x + circle_radius, corner_y + circle_radius],
                    outline=accent_color, width=2)

    # Watermark
    draw.text((size - 150, size - 40), texts['watermark'],
              font=watermark_font, fill=(128, 128, 128))

    return img

def main():
    """
    Main function with options for single or multiple designs.
    """
    print("Imam al-Jawad (AS) Condolence Design Generator")
    print("=" * 50)

    choice = input("Create (1) Single design or (2) Multiple variations? [1]: ").strip()

    try:
        if choice == "2":
            print("\nCreating multiple design variations...")
            saved_files = create_multiple_designs()
        else:
            print("\nCreating single design...")
            design_img = create_imam_jawad_condolence_design()
            saved_files = save_design_with_options(design_img)

        print("\n" + "=" * 50)
        print("Design Creation Complete!")
        print(f"Total files saved: {len(saved_files)}")

        for file_path in saved_files:
            file_size = os.path.getsize(file_path) / 1024
            print(f"  - {file_path} ({file_size:.1f} KB)")

        print("\nDesign Features:")
        print("  ✓ 1:1 aspect ratio (1080x1080)")
        print("  ✓ Arabic condolence text for Imam al-Jawad (AS)")
        print("  ✓ Islamic mourning color scheme")
        print("  ✓ Decorative Islamic elements")
        print("  ✓ AliToucan watermark")
        print("  ✓ High resolution for social media sharing")

        # Open first design for preview
        if saved_files:
            try:
                first_design = Image.open(saved_files[0])
                first_design.show()
                print(f"\n  ✓ Preview opened: {saved_files[0]}")
            except Exception:
                print("\n  Note: Could not open preview automatically")

    except Exception as e:
        print(f"Error creating design: {e}")
        return False

    return True

if __name__ == "__main__":
    main()
