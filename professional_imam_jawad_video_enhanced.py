#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Professional Enhanced Imam <PERSON> (AS) Condolence Video Creator
Complete professional video creation system with MP4 output, audio integration,
advanced effects, and social media optimization.
"""

import os
import sys
import math
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance

# Import our enhanced modules
from premium_arabic_renderer import PremiumArabicRenderer
from enhanced_imam_jawad_content import EnhancedImamJawadContent
from enhanced_video_effects import EnhancedVideoEffects
from audio_manager import IslamicAudioManager
from mp4_video_creator import ProfessionalMP4Creator

class ProfessionalEnhancedVideoCreator:
    """Professional-grade enhanced video creator with complete feature set."""

    def __init__(self, aspect_ratio='1:1', duration_variant='medium', quality='high'):
        """Initialize with professional enhanced settings."""
        print("🎬 PROFESSIONAL ENHANCED IMAM AL-JAWAD VIDEO CREATOR")
        print("=" * 60)

        # Initialize core components
        self.content = EnhancedImamJawadContent()
        self.renderer = PremiumArabicRenderer()
        self.audio_manager = IslamicAudioManager()
        self.mp4_creator = ProfessionalMP4Creator()

        # Video configuration
        self.aspect_ratio = aspect_ratio
        self.duration_variant = duration_variant
        self.quality = quality

        # Get resolution and scenes
        aspect_configs = self.content.get_aspect_ratio_configs()
        self.resolution = aspect_configs[aspect_ratio]['resolution']
        self.width, self.height = self.resolution

        # Initialize effects engine
        self.effects = EnhancedVideoEffects(self.resolution)

        duration_config = self.content.get_duration_variants()[duration_variant]
        self.scenes = duration_config['scenes']
        self.target_duration = duration_config['total_duration']

        # Professional settings
        self.fps = 30
        self.transition_duration = 1.0
        self.frames = []
        self.scene_timings = {}

        # Create output directory
        self.output_dir = "professional_enhanced_output"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"✓ Created enhanced output directory: {self.output_dir}")

        print(f"✓ Configuration: {aspect_ratio} | {self.resolution[0]}x{self.resolution[1]} | {duration_variant}")

    def create_enhanced_scene_frame(self, scene_name, frame_number, total_frames, scene_progress):
        """Create an enhanced frame with all professional effects."""
        scene_data = self.content.content[scene_name]
        style_name = scene_data.get('style', 'elegant')
        style_config = self.content.get_enhanced_styles()[style_name]
        animation_type = scene_data.get('animation', 'gentle_fade')

        # Calculate animation progress with easing
        progress = self._apply_easing(frame_number / max(1, total_frames - 1), 'ease_out')

        # Create base image with enhanced gradient background
        img = self._create_enhanced_background(style_config)

        # Add Islamic geometric patterns
        pattern_overlay = self.effects.create_islamic_geometric_pattern(
            self.resolution,
            pattern_type='star',
            color=style_config.get('accent_color', (255, 215, 0)),
            alpha=30
        )
        if img.mode != 'RGBA':
            img = img.convert('RGBA')
        img = Image.alpha_composite(img, pattern_overlay)

        # Add sophisticated decorations
        img = self._add_enhanced_decorations(img, style_config)

        # Apply text with enhanced effects
        img = self._render_enhanced_text(img, scene_data, style_config, progress, animation_type)

        # Add particle effects for special scenes
        if 'quranic' in scene_name or 'prayer' in scene_name:
            img = self.effects.create_particle_effect(img, progress, 'light_rays')
        elif 'hadith' in scene_name:
            img = self.effects.create_particle_effect(img, progress, 'stars')

        # Apply divine light effect for spiritual content
        if 'divine' in animation_type or 'quranic' in scene_name:
            center_point = (self.width // 2, self.height // 2 - 100)
            img = self.effects.add_divine_light_effect(img, center_point, progress * 0.5, 0.2)

        # Apply professional color grading
        if 'golden' in style_name or 'hadith' in scene_name:
            img = self.effects.apply_professional_color_grading(img, 'warm_gold')
        elif 'quranic' in scene_name:
            img = self.effects.apply_professional_color_grading(img, 'cool_blue')

        # Add elegant watermark
        img = self._add_enhanced_watermark(img, progress, scene_progress)

        return img

    def _create_enhanced_background(self, style_config):
        """Create enhanced gradient background with textures."""
        if 'bg_gradient' in style_config:
            bg = self.renderer.create_gradient_background(
                self.resolution,
                style_config['bg_gradient'],
                direction='vertical'
            )
        else:
            bg_color = style_config.get('bg_color', (0, 0, 0))
            bg = Image.new('RGB', self.resolution, bg_color)

        # Add subtle texture overlay
        texture_overlay = self._create_subtle_texture()
        if bg.mode != 'RGBA':
            bg = bg.convert('RGBA')
        bg = Image.alpha_composite(bg, texture_overlay)

        return bg

    def _create_subtle_texture(self):
        """Create subtle texture overlay for enhanced visual depth."""
        texture = Image.new('RGBA', self.resolution, (0, 0, 0, 0))
        draw = ImageDraw.Draw(texture)

        # Add subtle noise pattern
        import random
        random.seed(42)  # Consistent pattern

        for _ in range(100):
            x = random.randint(0, self.width)
            y = random.randint(0, self.height)
            size = random.randint(1, 3)
            alpha = random.randint(5, 15)

            draw.ellipse([x-size, y-size, x+size, y+size],
                        fill=(255, 255, 255, alpha))

        return texture

    def _add_enhanced_decorations(self, img, style_config):
        """Add enhanced Islamic decorative elements."""
        border_style = style_config.get('border_style', 'classic')
        accent_color = style_config.get('accent_color', (255, 215, 0))

        # Apply ornate border
        img = self.renderer.draw_ornate_border(img, border_style, accent_color)

        # Add corner decorations based on aspect ratio
        if self.aspect_ratio in ['1:1', '4:5']:
            self._add_enhanced_square_decorations(img, accent_color)
        elif self.aspect_ratio == '16:9':
            self._add_enhanced_widescreen_decorations(img, accent_color)
        elif self.aspect_ratio == '9:16':
            self._add_enhanced_vertical_decorations(img, accent_color)

        return img

    def _add_enhanced_square_decorations(self, img, color):
        """Add enhanced decorations for square layouts."""
        draw = ImageDraw.Draw(img)

        # Central decorative line with ornaments
        center_y = self.height // 2 + 120
        line_start = self.width // 4
        line_end = 3 * self.width // 4

        # Main decorative line
        draw.line([(line_start, center_y), (line_end, center_y)], fill=color, width=4)

        # Ornamental end caps with Islamic motifs
        cap_size = 20
        for x in [line_start, line_end]:
            # Outer circle
            draw.ellipse([x-cap_size, center_y-cap_size, x+cap_size, center_y+cap_size],
                        outline=color, width=3)
            # Inner star
            star_points = []
            for i in range(8):
                angle = i * math.pi / 4
                inner_radius = cap_size * 0.6
                sx = x + inner_radius * math.cos(angle)
                sy = center_y + inner_radius * math.sin(angle)
                star_points.append((sx, sy))

            if len(star_points) >= 3:
                draw.polygon(star_points, outline=color, width=2)

    def _add_enhanced_widescreen_decorations(self, img, color):
        """Add enhanced decorations for widescreen layouts."""
        draw = ImageDraw.Draw(img)

        # Side decorative elements with Islamic patterns
        margin = 120
        center_y = self.height // 2

        for x in [margin, self.width - margin]:
            # Vertical decorative line
            draw.line([(x, center_y - 80), (x, center_y + 80)], fill=color, width=3)

            # Decorative elements along the line
            for i in range(5):
                y_offset = (i - 2) * 30
                y_pos = center_y + y_offset

                # Diamond shapes
                diamond_size = 8
                diamond_points = [
                    (x, y_pos - diamond_size),
                    (x + diamond_size, y_pos),
                    (x, y_pos + diamond_size),
                    (x - diamond_size, y_pos)
                ]
                draw.polygon(diamond_points, outline=color, width=2)

    def _add_enhanced_vertical_decorations(self, img, color):
        """Add enhanced decorations for vertical layouts."""
        draw = ImageDraw.Draw(img)

        # Top and bottom decorative bands with Islamic motifs
        band_height = 80
        margin = 100

        for y in [band_height, self.height - band_height]:
            # Main decorative band
            draw.rectangle([margin, y-4, self.width - margin, y+4], fill=color)

            # Decorative elements along the band
            for x in range(margin + 40, self.width - margin, 80):
                # Small Islamic star
                star_size = 12
                star_points = []
                for i in range(6):
                    angle = i * math.pi / 3
                    sx = x + star_size * math.cos(angle)
                    sy = y + star_size * math.sin(angle)
                    star_points.append((sx, sy))

                if len(star_points) >= 3:
                    draw.polygon(star_points, outline=color, width=2)

    def _render_enhanced_text(self, img, scene_data, style_config, progress, animation_type):
        """Render text with enhanced effects and animations."""
        layout = self._calculate_enhanced_text_layout(scene_data)
        font_sizes = self._get_enhanced_font_sizes()

        # Main Arabic text
        if 'arabic' in scene_data:
            main_font, _ = self.renderer.get_best_font(font_sizes['main'], 'title')

            # Apply animation effects
            if animation_type == 'fade_with_calligraphy':
                scale_factor = 0.8 + (0.2 * progress)
                scaled_size = int(font_sizes['main'] * scale_factor)
                main_font, _ = self.renderer.get_best_font(scaled_size, 'title')

            # Enhanced text effects
            effects = {
                'text_glow': True,
                'glow_radius': 4,
                'divine_glow': 'quranic' in scene_data.get('style', '')
            }

            img = self.renderer.draw_centered_text(
                img, scene_data['arabic'],
                layout['main_arabic'], main_font,
                style_config['primary_color'], effects
            )

        # English subtitle with fade-in
        if 'english' in scene_data and progress > 0.3:
            english_font, _ = self.renderer.get_best_font(font_sizes['english'], 'modern')
            english_alpha = min(1.0, (progress - 0.3) * 2)

            img = self.renderer.draw_centered_text(
                img, scene_data['english'],
                layout['main_english'], english_font,
                style_config['secondary_color']
            )

        # Additional subtitle if present
        if 'subtitle_arabic' in scene_data and progress > 0.5:
            subtitle_font, _ = self.renderer.get_best_font(font_sizes['subtitle'], 'regular')

            img = self.renderer.draw_centered_text(
                img, scene_data['subtitle_arabic'],
                layout.get('subtitle_arabic', layout['main_arabic'] + 60), subtitle_font,
                style_config.get('accent_color', style_config['secondary_color'])
            )

        return img

    def _calculate_enhanced_text_layout(self, scene_data):
        """Calculate enhanced text layout with better spacing."""
        has_subtitle = 'subtitle_arabic' in scene_data or 'subtitle_english' in scene_data

        if self.aspect_ratio == '1:1':
            if has_subtitle:
                return {
                    'main_arabic': self.height // 2 - 100,
                    'main_english': self.height // 2 - 40,
                    'subtitle_arabic': self.height // 2 + 40,
                    'subtitle_english': self.height // 2 + 100
                }
            else:
                return {
                    'main_arabic': self.height // 2 - 50,
                    'main_english': self.height // 2 + 10
                }
        elif self.aspect_ratio == '16:9':
            if has_subtitle:
                return {
                    'main_arabic': self.height // 2 - 80,
                    'main_english': self.height // 2 - 30,
                    'subtitle_arabic': self.height // 2 + 30,
                    'subtitle_english': self.height // 2 + 80
                }
            else:
                return {
                    'main_arabic': self.height // 2 - 40,
                    'main_english': self.height // 2 + 20
                }
        else:  # 9:16 and others
            if has_subtitle:
                return {
                    'main_arabic': self.height // 2 - 120,
                    'main_english': self.height // 2 - 60,
                    'subtitle_arabic': self.height // 2 + 60,
                    'subtitle_english': self.height // 2 + 120
                }
            else:
                return {
                    'main_arabic': self.height // 2 - 60,
                    'main_english': self.height // 2
                }

    def _get_enhanced_font_sizes(self):
        """Get enhanced font sizes optimized for readability."""
        base_size = min(self.width, self.height) // 22

        return {
            'title': int(base_size * 2.2),
            'main': int(base_size * 1.6),
            'subtitle': int(base_size * 1.1),
            'english': int(base_size * 1.0),
            'watermark': int(base_size * 0.7)
        }

    def _add_enhanced_watermark(self, img, progress, scene_progress):
        """Add enhanced animated watermark."""
        if scene_progress < 0.7:  # Show watermark in later part of each scene
            return img

        watermark_alpha = int(120 * min(1.0, (scene_progress - 0.7) * 3))
        watermark_font, _ = self.renderer.get_best_font(self._get_enhanced_font_sizes()['watermark'], 'modern')

        # Position based on aspect ratio
        if self.aspect_ratio == '9:16':
            x_pos = self.width - 140
            y_pos = self.height - 100
        else:
            x_pos = self.width - 180
            y_pos = self.height - 60

        # Enhanced watermark with subtle glow
        draw = ImageDraw.Draw(img)
        watermark_color = (160, 160, 160, watermark_alpha) if watermark_alpha < 255 else (160, 160, 160)

        # Add subtle shadow
        draw.text((x_pos + 2, y_pos + 2), "AliToucan", font=watermark_font, fill=(0, 0, 0, watermark_alpha // 2))
        # Main watermark
        draw.text((x_pos, y_pos), "AliToucan", font=watermark_font, fill=watermark_color[:3])

        return img

    def _apply_easing(self, t, easing_type='ease_out'):
        """Apply easing functions for smoother animations."""
        if easing_type == 'ease_out':
            return 1 - (1 - t) ** 3
        elif easing_type == 'ease_in':
            return t ** 3
        elif easing_type == 'ease_in_out':
            return 3 * t ** 2 - 2 * t ** 3
        else:
            return t

    def create_complete_enhanced_video(self):
        """Create complete enhanced video with all professional features."""
        print(f"\n🎬 Creating Enhanced Professional Video...")
        print(f"   Scenes: {len(self.scenes)}")
        print(f"   Target Duration: {self.target_duration}s")
        print(f"   Resolution: {self.width}x{self.height}")

        all_frames = []
        current_time = 0.0

        # Calculate scene durations and timings
        for i, scene_name in enumerate(self.scenes):
            scene_data = self.content.content[scene_name]
            scene_duration = scene_data.get('duration', 4.0)

            self.scene_timings[scene_name] = {
                'start': current_time,
                'end': current_time + scene_duration,
                'duration': scene_duration
            }
            current_time += scene_duration

        # Create frames for each scene
        for i, scene_name in enumerate(self.scenes):
            print(f"   📝 Creating scene {i+1}/{len(self.scenes)}: {scene_name}")

            scene_timing = self.scene_timings[scene_name]
            scene_duration = scene_timing['duration']
            frames_per_scene = int(scene_duration * self.fps)

            scene_frames = []
            for frame_num in range(frames_per_scene):
                frame_progress = frame_num / max(1, frames_per_scene - 1)
                overall_progress = (current_time + frame_progress * scene_duration) / self.target_duration

                frame = self.create_enhanced_scene_frame(
                    scene_name, frame_num, frames_per_scene, frame_progress
                )
                scene_frames.append(frame)

            # Add transition frames between scenes
            if i < len(self.scenes) - 1:
                transition_frames = self._create_transition_frames(
                    scene_frames[-1], scene_name, self.scenes[i + 1]
                )
                scene_frames.extend(transition_frames)

            all_frames.extend(scene_frames)

        self.frames = all_frames
        print(f"   ✓ Created {len(all_frames)} total frames")

        return all_frames

    def _create_transition_frames(self, last_frame, current_scene, next_scene):
        """Create smooth transition frames between scenes."""
        transition_frames = []
        transition_duration = 0.5  # 0.5 seconds
        transition_frame_count = int(transition_duration * self.fps)

        for i in range(transition_frame_count):
            progress = i / max(1, transition_frame_count - 1)

            # Create fade-out effect
            faded_frame = self.effects.apply_fade_transition(
                last_frame.copy(), 1.0 - progress, 'gentle'
            )
            transition_frames.append(faded_frame)

        return transition_frames

    def create_audio_track(self):
        """Create synchronized audio track for the video."""
        print("🎵 Creating synchronized audio track...")

        # Get audio recommendation based on video style
        audio_recommendation = self.audio_manager.get_audio_recommendations(
            'peaceful_remembrance', self.target_duration
        )

        print(f"   Audio style: {audio_recommendation['description']}")

        # Create timed audio track
        audio_track = self.audio_manager.create_timed_audio_track(
            self.target_duration, self.scene_timings, audio_recommendation['style']
        )

        if audio_track:
            # Save audio track
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            audio_filename = f"imam_jawad_audio_{self.aspect_ratio.replace(':', 'x')}_{timestamp}"
            audio_path = self.audio_manager.save_audio_track(audio_track, audio_filename)

            print(f"   ✓ Audio track created: {audio_filename}.mp3")
            return audio_path
        else:
            print("   ⚠️  Audio track creation skipped (audio libraries not available)")
            return None

    def export_professional_video(self, include_audio=True, create_variants=True):
        """Export professional video in multiple formats."""
        print("\n📤 EXPORTING PROFESSIONAL VIDEO")
        print("=" * 50)

        if not self.frames:
            print("❌ No frames available. Create video first.")
            return None

        results = {
            'main_video': None,
            'audio_track': None,
            'social_variants': [],
            'export_info': {}
        }

        # Create audio track if requested
        audio_path = None
        if include_audio:
            audio_path = self.create_audio_track()
            results['audio_track'] = audio_path

        # Create main MP4 video
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_filename = f"imam_jawad_enhanced_{self.aspect_ratio.replace(':', 'x')}_{self.duration_variant}"

        print("🎬 Creating main MP4 video...")
        main_video_path = self.mp4_creator.create_mp4_from_frames(
            self.frames,
            base_filename,
            fps=self.fps,
            audio_path=audio_path,
            fade_duration=1.0
        )

        if main_video_path:
            results['main_video'] = main_video_path

            # Optimize for web
            optimized_path = self.mp4_creator.optimize_for_web(main_video_path, 'high')
            if optimized_path != main_video_path:
                results['optimized_video'] = optimized_path

        # Create social media variants if requested
        if create_variants and main_video_path:
            print("📱 Creating social media variants...")
            variants = self.mp4_creator.create_social_media_variants(
                self.frames, base_filename, audio_path
            )
            results['social_variants'] = variants

        # Export information
        results['export_info'] = {
            'timestamp': timestamp,
            'resolution': self.resolution,
            'aspect_ratio': self.aspect_ratio,
            'duration': self.target_duration,
            'fps': self.fps,
            'total_frames': len(self.frames),
            'scenes': len(self.scenes),
            'audio_included': include_audio and audio_path is not None
        }

        return results

def create_professional_enhanced_video_suite():
    """Create complete suite of professional enhanced videos."""
    print("🎬 PROFESSIONAL ENHANCED IMAM AL-JAWAD VIDEO SUITE")
    print("=" * 70)
    print("🤲 In memory of Imam Muhammad al-Jawad (peace be upon him)")
    print("=" * 70)

    # Configuration matrix for different platforms
    configs = [
        ('1:1', 'medium', 'Instagram Square / Facebook Post'),
        ('16:9', 'medium', 'YouTube / Widescreen'),
        ('9:16', 'short', 'Instagram Stories / TikTok'),
        ('4:5', 'short', 'Facebook Feed Optimized')
    ]

    all_results = []

    for aspect_ratio, duration, description in configs:
        print(f"\n📹 Creating {description} ({aspect_ratio})...")

        try:
            # Create enhanced video creator
            creator = ProfessionalEnhancedVideoCreator(
                aspect_ratio=aspect_ratio,
                duration_variant=duration,
                quality='high'
            )

            # Create video frames
            frames = creator.create_complete_enhanced_video()

            if frames:
                # Export professional video
                results = creator.export_professional_video(
                    include_audio=True,
                    create_variants=False  # Create variants separately
                )

                if results['main_video']:
                    all_results.append({
                        'config': (aspect_ratio, duration, description),
                        'results': results,
                        'creator': creator
                    })

                    print(f"   ✓ {description} completed successfully")
                else:
                    print(f"   ❌ Failed to export {description}")
            else:
                print(f"   ❌ Failed to create frames for {description}")

        except Exception as e:
            print(f"   ❌ Error creating {description}: {e}")

    return all_results

if __name__ == "__main__":
    results = create_professional_enhanced_video_suite()

    if results:
        print("\n" + "=" * 70)
        print("🎉 PROFESSIONAL ENHANCED VIDEO SUITE COMPLETED!")
        print("=" * 70)

        for result in results:
            config = result['config']
            video_results = result['results']

            print(f"\n✓ {config[2]}:")
            print(f"   📁 Main Video: {os.path.basename(video_results['main_video'])}")
            if video_results.get('optimized_video'):
                print(f"   🔧 Optimized: {os.path.basename(video_results['optimized_video'])}")
            if video_results['audio_track']:
                print(f"   🎵 Audio: {os.path.basename(video_results['audio_track'])}")

            info = video_results['export_info']
            print(f"   📊 {info['resolution'][0]}x{info['resolution'][1]} | {info['duration']}s | {info['total_frames']} frames")

        print(f"\n📁 Output directory: professional_enhanced_output")
        print("\n🎯 ENHANCED FEATURES IMPLEMENTED:")
        print("  ✓ Professional MP4 video output with audio")
        print("  ✓ Premium Arabic typography with enhanced rendering")
        print("  ✓ Advanced Islamic geometric patterns and decorations")
        print("  ✓ Sophisticated animations and transitions")
        print("  ✓ Professional color grading and visual effects")
        print("  ✓ Particle effects and divine light animations")
        print("  ✓ English subtitles with proper timing")
        print("  ✓ Multiple aspect ratios for all social platforms")
        print("  ✓ Islamic ambient audio with scene synchronization")
        print("  ✓ Web-optimized output for fast loading")
        print("  ✓ Professional watermarking and branding")

        print("\n🤲 May Allah bless the memory of Imam al-Jawad (AS)")
        print("   Created with respect and reverence for Ahl al-Bayt (peace be upon them)")
    else:
        print("\n❌ No videos were created successfully.")
        print("Please check the error messages above and ensure all dependencies are installed.")
